<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);
?>
<div class="md:pt-7 px-3 md:px-0 pb-4 text-cgrey-0 text-base leading-6 bg-container-darker relative">
    <!-- Dev Test -->
    <?php
    $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
    $httpContext = $objectManager->get(\Magento\Framework\App\Http\Context::class);
    $isCustomerLoggedIn = (bool) $httpContext->getValue('customer_logged_in');
    ?>
    <div class="hidden dev-test">
        <?php if ($isCustomerLoggedIn): ?>
            <p>Customer is logged in.</p>
        <?php else: ?>
            <p>Customer is logged out.</p>
        <?php endif; ?>
    </div>
    <!-- Dev Test -->
    <div class="container">
        <div class="flex flex-wrap order-first gap-y-2 justify-between">
            <div class="lg:w-9/12 w-full flex flex-wrap cfooter-top">
                <?= $block->getBlockHtml('footer-cms-content') ?>
            </div>
            <div class="w-full lg:w-3/12 lg:max-w-[250px] lg:justify-end">
                <?= $block->getBlockHtml('form.subscribe') ?>
            </div>
        </div>
        <div>
            <div class="bg-container-darker my-0 md:mt-4">
                <?= $block->getBlockHtml('footer-cms-copyright') ?>
            </div>
        </div>
    </div>
</div>

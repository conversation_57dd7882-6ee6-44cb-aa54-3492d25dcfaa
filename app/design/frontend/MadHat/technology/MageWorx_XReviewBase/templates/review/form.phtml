<?php
/**
 * Copyright © MageWorx. All rights reserved.
 * See https://www.mageworx.com/terms-and-conditions for license details.
 */
//@formatter:off
/** @var \MageWorx\XReviewBase\Block\Review\Form $block */
/** @var \Magento\Framework\Escaper $escaper */

/** @var ReCaptcha $recaptchaViewModel */
//Do not replace this with $viewModels->require(ReCaptcha::class); that might break the dependency on
//Magento's Recaptcha module
$recaptchaViewModel = $block->getData('viewModelRecaptcha');
$recaptchaInputFieldBlock = '';
$recaptchaLegalNoticeBlock = '';
if ($recaptchaViewModel !== null && $recaptchaData = $recaptchaViewModel->getRecaptchaData('product_review')) {
    $recaptchaInputFieldBlock = $recaptchaData[ReCaptcha::RECAPTCHA_INPUT_FIELD];
    $recaptchaLegalNoticeBlock = $recaptchaData[ReCaptcha::RECAPTCHA_LEGAL_NOTICE_BLOCK];
}

/** @var \Hyva\Theme\ViewModel\HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsSolid::class);
/** @var \MadHat\Catalog\ViewModel\MadHatCatalogConfig $madHatVarientInfo */
$madHatVarientInfo = $viewModels->require(\MadHat\Catalog\ViewModel\MadHatCatalogConfig::class);
?>
<div class="py-6 col-span-1 justify-items-end" id="review-form">
    <div class="card w-full px-6 py-3">
        <div class="text-xl my-3">
            <?= $escaper->escapeHtml(__('Write Your Own Review')) ?>
        </div>
        <?php if ($block->getAllowWriteReviewFlag()): ?>
            <form class="review-form"
                  action="<?= $escaper->escapeUrl($block->getAction()) ?>"
                  id="review-form"
                  method="post"
                  enctype="multipart/form-data"
            >
                <?= $block->getBlockHtml('formkey') ?>
                <?= $block->getChildHtml('form_fields_before') ?>
                <?= $block->getBlockHtml($recaptchaInputFieldBlock) ?>
                <fieldset>
                    <legend class="text-xs">
                        <span>
                            <?= $escaper->escapeHtml(
                                __("You're reviewing:")
                            ) ?>
                        </span>
                        <span class="font-semibold">
                            <?= $escaper->escapeHtml($block->getProductInfo()->getName()) ?>
                        </span>
                    </legend>

                    <?php if ($block->getRatings() && $block->getRatings()->getSize()): ?>
                        <fieldset>
                            <div class="mt-4 mb-3">
                                <div class="text-md"><?= $escaper->escapeHtml(__('Your Rating')) ?>:</div>
                                <div>
                                    <div id="product-review-table">
                                        <?php foreach ($block->getRatings() as $rating): ?>
                                            <div class="mt-2">
                                                <label class="mb-0"
                                                       id="<?= $escaper->escapeHtml($rating->getRatingCode()) ?>_rating_label">
                                                    <span><?= $escaper->escapeHtml($rating->getRatingCode()) ?></span>
                                                </label>
                                                <div class="flex flex-row flex-grow-0" x-data="{ clickedRatingId: 0 }">
                                                    <?php $options = $rating->getOptions(); ?>
                                                    <?php $iterator = 1;
                                                    foreach ($options as $option): ?>
                                                        <div class="relative"
                                                             @click="clickedRatingId = <?= (int)$iterator ?> || 0">
                                                            <input class="absolute opacity-0 bottom-0 left-0 cursor-pointer"
                                                                   type="radio"
                                                                   <?php if ($iterator === 1): ?>required<?php endif; ?>
                                                                   name="ratings[<?= $escaper->escapeHtmlAttr($rating->getId()) ?>]"
                                                                   id="<?= $escaper->escapeHtmlAttr(
                                                                       $rating->getRatingCode() . '_' . $option->getValue()
                                                                   ) ?>"
                                                                   value="<?= $escaper->escapeHtmlAttr($option->getId()) ?>"/>
                                                            <label class="rating-<?= (int)$iterator ?> m-0 cursor-pointer"
                                                                   for="<?= $escaper->escapeHtmlAttr(
                                                                       $rating->getRatingCode() . '_' . $option->getValue()
                                                                   ) ?>"
                                                                   title="<?= $escaper->escapeHtmlAttr(
                                                                       __('%1 %2',
                                                                           $iterator,
                                                                           $iterator > 1
                                                                               ? __('stars')
                                                                               : __('star')
                                                                       )
                                                                   ) ?>"
                                                                   id="<?= $escaper->escapeHtmlAttr(
                                                                       $rating->getRatingCode() . '_' . $option->getValue()
                                                                   ) ?>_label">
                                                                <span :class="<?= (int)$iterator ?> <= clickedRatingId ?'text-yellow-400' : 'text-gray-400'">
                                                                    <?= $heroicons->starHtml('w-8 h-8', null, null) ?>
                                                                </span>
                                                            </label>
                                                        </div>
                                                        <?php $iterator++; ?>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                    <input type="hidden" name="validate_rating" value=""/>
                                </div>
                            </div>
                        </fieldset>
                    <?php endif ?>


                    <div>
                        <!-- START Variant attributes info -->

                        <input x-data="{'sku':''}" :value="sku" type="hidden" name="variant_sku" @simple-detail-product-active.window="sku = $event.detail.product.sku"/>

                        <?php $varients = $madHatVarientInfo->getPdpProductAttributeSetMapping(); ?>
                        <?php foreach ($varients as $key=>$val): ?>
                            <?php foreach ($val as $k=>$v): ?>
                                <input x-data="{<?= $escaper->escapeHtml($v) ?>:''}" :value="<?= $escaper->escapeHtml($v) ?>" type="hidden" name="<?= $escaper->escapeHtml($v) ?>"
                                       @simple-detail-product-active.window="<?= $escaper->escapeHtml($v) ?>=$event.detail.product.additional_info.<?= $escaper->escapeHtml($v) ?>?'<?= $escaper->escapeHtml($k).':'; ?>' + $event.detail.product.additional_info.<?= $escaper->escapeHtml($v) ?>.value : '';"/>
                            <?php endforeach;?>
                        <?php endforeach;?>
                        <!-- END Variant attributes info -->

                        <!-- Nickname -->
                        <div>
                            <label for="nickname_field" class="sr-only">
                                <span><?= $escaper->escapeHtml(__('Nickname')) ?></span>
                            </label>
                            <input class="form-input mt-3 block w-full text-sm"
                                   required title="<?= $escaper->escapeHtmlAttr(__('Nickname')) ?>"
                                   placeholder="<?= $escaper->escapeHtmlAttr(__('Nickname') . '*') ?>" type="text"
                                   name="nickname" id="nickname_field"/>
                        </div>
                        <!-- Nickname -->

                        <!-- Summary -->
                        <div>
                            <label for="summary_field" class="sr-only">
                                <span><?= $escaper->escapeHtml(__('Summary')) ?></span>
                            </label>
                            <input class="form-input mt-3 block w-full text-sm"
                                   required title="<?= $escaper->escapeHtml(__('Summary')) ?>"
                                   placeholder="<?= $escaper->escapeHtml(__('Summary') . '*') ?>" type="text"
                                   name="title"
                                   id="summary_field"/>
                        </div>
                        <!-- Summary -->

                        <!-- Review -->
                        <div>
                            <label for="review_field" class="sr-only">
                                <span><?= $escaper->escapeHtml(__('Review')) ?></span>
                            </label>
                            <textarea class="form-input mt-3 block w-full text-sm"
                                      required title="<?= $escaper->escapeHtml(__('Review')) ?>"
                                      placeholder="<?= $escaper->escapeHtml(__('Review') . '*') ?>" name="detail"
                                      id="review_field" cols="5" rows="3"></textarea>
                        </div>
                        <!-- Review -->

                        <?php if ($block->isAllowProsAndCons()): ?>
                            <!-- Pros and cons -->
                            <div class="my-4">
                                <label class="flex items-start gap-x-2">
                                    <?= $heroicons->plusCircleHtml('w-5 h-5 text-green-500', null, null); ?>
                                    <?= $escaper->escapeHtml(__('Pros')) ?>
                                </label>
                                <div>
                                    <textarea class="form-input block w-full text-sm"
                                              name="pros" id="pros_field" rows="5"></textarea>
                                </div>
                            </div>
                            <div class="my-4">
                                <label class="flex items-start gap-x-2">
                                    <?= $heroicons->minusCircleHtml('w-5 h-5 text-red-500', null, null); ?>
                                    <?= $escaper->escapeHtml(__('Cons')) ?>
                                </label>
                                <div>
                                    <textarea class="form-input block w-full text-sm"
                                              name="cons" id="cons_field" rows="5"></textarea>
                                </div>
                            </div>
                            <!-- Pros and cons -->
                        <?php endif; ?>

                        <?php if ($block->isAllowRecommend()): ?>
                            <!-- Is allow recommend -->
                            <div class="mt-4">
                                <label for="recommend_field"
                                    class="border p-4 flex flex-row items-center gap-y-2 gap-x-2 rounded cursor-pointer">
                                    <input type="checkbox" name="is_recommend" id="recommend_field" value="1"/>
                                    <span><?= $escaper->escapeHtml(__('Recommend this product')) ?></span>
                                </label>
                            </div>
                            <!-- Is allow recommend -->
                        <?php endif; ?>

                        <?php if ($block->isAllowImagesUploading()): ?>
                            <script>
                                const deleteImage = "<?= $escaper->escapeHtml(__('Delete image')) ?>";

                                function createWrapper() {
                                    const $wrapper = document.createElement("div");
                                    $wrapper.classList.add("flex", "flex-col", "gap-y-2");
                                    return $wrapper;
                                }
                                function createPreviewImage(src) {
                                    const $image = document.createElement("img");
                                    $image.classList.add("h-24", "w-auto");
                                    $image.src = src;
                                    return $image;
                                }
                                function createInput(num, $wrapper) {
                                    const $input = document.createElement("input");
                                    $input.type="file";
                                    $input.classList.add("sr-only");
                                    $input.name=`customer-image-${num}`;
                                    $input.autocomplete="off";
                                    $input.accept="image/*";
                                    $input.addEventListener("change", () => {
                                        const reader = new FileReader();
                                        reader.onload = e => {
                                            const src = e.target.result;

                                            $image = createPreviewImage(src);
                                            $wrapper.append($image);
                                            $removeButton = createRemoveButton($wrapper);
                                            $wrapper.append($removeButton);
                                        };
                                        reader.onabort = e => $wrapper.remove();
                                        reader.readAsDataURL($input.files[0]);
                                    });
                                    return $input;
                                }
                                function createRemoveButton(wrapper) {
                                    const $button = document.createElement("button");
                                    $button.type = "button";
                                    $button.innerText = deleteImage;
                                    $button.addEventListener("click", () => wrapper.remove());
                                    return $button;
                                }

                                function initMwImagesSelect(){
                                    const $images = document.getElementById("mw-uploaded-images");

                                    return {
                                        refCount: 0,
                                        selectImage() {
                                            const $wrapper = createWrapper();
                                            const $input = createInput(this.refCount, $wrapper);
                                            $wrapper.append($input);
                                            $images.append($wrapper);

                                            this.refCount++;
                                            $input.click();
                                        },
                                    }
                                }
                            </script>

                            <!-- Image upload -->
                            <div class="mt-4" x-data="initMwImagesSelect()">
                                <div class="border p-4 flex flex-row flex-wrap items-center gap-y-2 gap-x-2 w-full block rounded cursor-pointer my-2"
                                       for="customer-image" @click.prevent="selectImage()">
                                    <?= $heroicons->cameraHtml('w-8 h-8 text-gray-500', null, null); ?>
                                    <span><?= $escaper->escapeHtml(__('Upload photo')) ?></span>
                                </div>
                                <div class="mt-2 p-2 flex flex-wrap gap-x-4" id="mw-uploaded-images">
                                </div>
                            </div>
                            <!-- Image upload -->
                        <?php endif; ?>

                        <?php if ($block->isAllowPolicyField()): ?>
                            <!-- Policy field -->
                            <div class="my-4 ml-2">
                                <div class="flex flex-wrap items-center gap-x-2">
                                    <input type="checkbox" name="policy" id="policy_field" required/>
                                    <label for="policy_field" class="mb-0">
                                        <span><?= $escaper->escapeHtml($block->getPolicyMessage(), ['a']) ?></span>
                                    </label>
                                </div>
                            </div>
                            <!-- Policy field -->
                        <?php endif; ?>
                    </div>
                </fieldset>
                <div class="my-3">
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <span>
                                <?= $escaper->escapeHtml(__('Submit Review')) ?>
                            </span>
                        </button>
                    </div>
                    <?= $block->getChildHtml('form_additional_after') ?>
                    <?= $block->getBlockHtml($recaptchaLegalNoticeBlock) ?>
                </div>
            </form>

        <?php else: ?>
            <div id="review-form">
                <div class="pb-6">
                    <?= $escaper->escapeHtml(
                        __(
                            'Only registered users can write reviews. Please <a href="%1" class="underline">Sign in</a> or <a href="%2" class="underline">create an account</a>',
                            $block->getLoginLink(),
                            $block->getRegisterUrl()
                        ),
                        ['a']
                    ) ?>
                </div>
            </div>
        <?php endif ?>
    </div>
</div>


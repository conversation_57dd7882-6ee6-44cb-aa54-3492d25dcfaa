<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var \Magento\Framework\Escaper $escaper */
/** @var \Magento\Sales\Model\Order\Shipment\Item $_item */
$_item = $block->getItem();
$orderItem = $_item->getOrderItem();
// image adjustments for simple & configurable products for Order email

if ($orderItem->getProduct()->getTypeId() == "configurable") {
    $sku = $block->getSku($_item);
    $loadProduct = $block->getLayout()->createBlock(\MadHat\Catalog\Block\Product\View::class);
    $product = $loadProduct->getProductBySku($sku);
    $imageUrl = $this->helper('Magento\Catalog\Helper\Image')
        ->init($product, 'cart_page_product_thumbnail')
        ->getUrl();
} else {
    $imageUrl = $this->helper('Magento\Catalog\Helper\Image')
        ->init($orderItem->getProduct(), 'cart_page_product_thumbnail')
        ->getUrl();
}
?>
<tr>
    <td class="item-info<?= ($block->getItemOptions() ? ' has-extra' : '') ?>">
        <table width="100%" class="thumbnail-table">
            <tr>
                <td class="thumbnail-td" width="20%">
                    <div class="thumbnail-img">
                        <img src="<?= $imageUrl ?>" height="64" width="64" alt="Thumbnail Img"/>
                    </div>
                </td>
                <td>
                    <p class="product-name"><?= $escaper->escapeHtml($_item->getName()) ?></p>
                    <p class="sku"><?= $escaper->escapeHtml(__('SKU')) ?>
                        : <?= $escaper->escapeHtml($block->getSku($_item)) ?></p>
                    <?php if ($block->getItemOptions()): ?>
                        <div class="item-options">
                            <?php foreach ($block->getItemOptions() as $option): ?>
                                <?php /* <dt><strong><em><?= $escaper->escapeHtml($option['label']) ?></em></strong></dt> */ ?>
                                <span>
                                    <?= /* @noEscape */
                                    nl2br($option['value']) ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
                    <?php if ($addInfoBlock): ?>
                        <?= $addInfoBlock->setItem($_item->getOrderItem())->toHtml() ?>
                    <?php endif; ?>
                    <?= $escaper->escapeHtml($_item->getDescription()) ?>
                </td>
            </tr>
        </table>
    </td>
    <td class="item-qty"><?= (float)$_item->getQty() ?></td>
</tr>

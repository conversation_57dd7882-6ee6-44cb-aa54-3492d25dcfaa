<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Advanced Search Hyva Compatibility M2 by Amasty
 */

declare(strict_types=1);

use Amasty\XsearchHyvaCompatibility\ViewModel\SearchPopup;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Amasty\XsearchHyvaCompatibility\ViewModel\ComponentsRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var ComponentsRegistry $componentRegistry */
/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$popupViewModel = $viewModels->require(SearchPopup::class);
$blocks = $popupViewModel->getBlockPositions();
$componentRegistry = $viewModels->require(ComponentsRegistry::class);
$hyvaUIHeaderVariant = $popupViewModel->getHyvaUIHeaderVariant();
?>
<?= /** @noEscape */ $componentRegistry->renderOnce('search-js-products') ?>
<?= /** @noEscape */ $componentRegistry->renderOnce('search-js-autocomplete') ?>
<?= /** @noEscape */ $componentRegistry->renderOnce('search-js-carousel') ?>

<?php if (!empty($popupViewModel->getHyvaUIHeaderVariant())): ?>
    <?= /** @noEscape  */ $componentRegistry->renderOnce('search-js-hyva-ui-header-compat') ?>
<?php endif ?>

<div :class="{
            'container': !config.isFullScreenMode,
            'relative mx-auto text-black z-20': true,
            'max-w-none px-0': isHyvaUiHeader() && isFullWidthSearch,
            'px-0': isHyvaUiHeader() && isHorizontalView
     }"
     x-data="Object.assign(
        {amXsearchCarouselComponent},
         amXsearchProductsComponent(),
         amXsearchAutocompleteComponent(),
         <?php if (!empty($hyvaUIHeaderVariant)): ?>
            amXsearchAutocompleteHyvaUIHeaderCompat(),
         <?php endif ?>
     )"
     x-init="dynamicLayout($el)">

    <?= /** @noEscape */ $componentRegistry->render('loading') ?>


    <div x-show="showOverlay"
         class="fixed inset-0 bg-transparent bg-opacity-50 z-20 w-full h-full top-0 left-0 right-0 bottom-0"
         @click.prevent="closeSearch()"
         @keydown.window.escape="closeSearch()"></div>


    <div class="form mini-search z-20 relative mx-auto"
         :class="{
         'right-0': !isHyvaUiHeader(),
         'flex justify-center': checkHyvaUiHeaderVariant('c') && !isDefaultSearchInput && !isFullWidthSearch
         }"
    >
        <form id="search_mini_form" @submit.prevent="redirect()"
              action="<?= $escaper->escapeUrl($popupViewModel->getResultsPageUrl()) ?>"
              :class="{
              'w-full absolute right-0': checkHyvaUiHeaderVariant('a') && !isDefaultSearchInput && !isFullWidthSearch,
              'justify-center': !checkHyvaUiHeaderVariant('c')
              }"
              class="flex" method="GET">
            <label class="hidden" for="search" data-role="mini-search-label">
                <span><?= $escaper->escapeHtml(__('Search')) ?></span>
            </label>
            <div class="w-full relative flex align-center justify-center bg-white"
                 :style="`max-width: ${!isDefaultSearchInput && !isFullWidthSearch ? searchPopupWidth + 'px' : '100%'}`">
                <input id="search"
                       x-ref="searchInput"
                       maxlength="<?= $escaper->escapeHtml($popupViewModel->getMaxQueryLength()) ?>"
                       form="search_mini_form"
                       type="search"
                       name="<?= $escaper->escapeHtml($popupViewModel->getQueryParamName()) ?>"
                       value="<?= $escaper->escapeHtml($popupViewModel->getQueryText()) ?>"
                       autocomplete="off"
                       placeholder="<?= $escaper->escapeHtmlAttr(__('Search...')) ?>"
                       class="text-base leading-normal transition appearance-none text-grey-800 relative z-0
                    focus:outline-none focus:border-transparent flex-am-search-sidebar rounded-md"
                       :class="{
                       'rounded': isHyvaUiHeader(),
                       'rounded': !'<?= $escaper->escapeHtml($popupViewModel->showSearchButton()) ?>' && isHyvaUiHeader()
                       }"
                       :style="`width: ${checkHyvaUiHeaderVariant('c') && !isDefaultSearchInput && !isFullWidthSearch ? searchPopupWidth + 'px' : '100%'}`"
                       @focus="doSearch()"
                       @search="checkSearchInput()"
                       @input.debounce.<?= $escaper->escapeHtml($popupViewModel->getSearchInputDelayInMS()) ?>="doSearch()"
                    <?php if (!empty($hyvaUIHeaderVariant) && $hyvaUIHeaderVariant !== 'c'): ?>
                        x-intersect:enter.once="applyHyvaUiHeaderConfig()"
                    <?php endif ?>
                       @keydown.arrow-down.prevent="focusElement($el.querySelector('[tabindex]'))"/>
                <?php if ($popupViewModel->showSearchButton()): ?>
                    <button type="submit" title="<?= $escaper->escapeHtml(__('Search')) ?>"
                            class="am-custom-search-button search-button"
                            x-bind:disabled="isLoading || latestQuery.length < minSearchLength"
                            :class="{
                                'opacity-50': isLoading || latestQuery.length < minSearchLength,
                                'am-custom-search-button': <?= $escaper->escapeHtml($popupViewModel->isCustomLayoutEnabled() ? 'true' : 'false') ?>,
                                '': isHyvaUiHeader()
                            }"
                            aria-label="Search">
                        <?php if (!empty($hyvaUIHeaderVariant)): ?>
                            <?= $heroicons->searchHtml('', 24, 24, ["aria-hidden" => "true"]) ?>
                        <?php else: ?>
                            <?= $heroicons->searchHtml('', 24, 24, ["aria-hidden" => "true"]) ?>
                        <?php endif; ?>
                    </button>
                <?php endif; ?>
            </div>
        </form>
        <template x-if="searchInitialized">
            <div
                class="search-bottom-container amsearch-container"
                :class="{
                    'absolute': !config.isFullScreenMode || isHyvaUiHeader(),
                    '': isHyvaUiHeader(),
                    'right-0': !checkHyvaUiHeaderVariant('c'),
                    'top-12': checkHyvaUiHeaderVariant('c') && !isHorizontalView,
                    'top-14': checkHyvaUiHeaderVariant('a') && isHorizontalView
                }"
                tabindex="-1"
                x-cloak
                :style="`${!isDefaultSearchInput && !isFullWidthSearch ? 'max-width:' + searchPopupWidth + 'px' : ''}; ${!isHorizontalView && checkHyvaUiHeaderVariant('a') ? 'top: 44px; margin-top: 1px;' : ''}`"
                x-show="!isLoading && showOverlay">
                <div
                    class="flex w-full content-start flex-wrap box-border relative"
                    :class="{
                            'flex-col': isHorizontalView,
                            'flex-col lg:flex-row': !isHorizontalView,
                         }">
                    <div class="lg:border-r mt-6 am-search-sidebar flex flex-col w-full lg:w-1/4"
                         :class="{
                            'border-gray-600': showSideBar() && getResultProductsCount() > 0,
                            'hidden': latestQuery.length < minSearchLength && (
                                !showOnFirstClick('recentSearches') && !showOnFirstClick('popularSearches') && !showOnFirstClick('browsingHistory')
                            ) || isSidebarSectionsDisabled(),
                            'am-search-horizontal-view': isHorizontalView,
                         }">
                        <?php foreach ($blocks as $blockName => $position): ?>
                            <template
                                x-if="<?= $escaper->escapeHtml($blockName === 'products' ? 'isHorizontalView && !noResultsFound && ' : '') ?>
                                        sections?.<?= $escaper->escapeHtml($blockName) ?>?.items?.length">
                                <div
                                    x-show="(latestQuery.length >= minSearchLength || showOnFirstClick('<?= $escaper->escapeHtml($blockName) ?>'))"
                                    x-cloak>
                                    <?= /** @noEscape */
                                    $componentRegistry->render('search-' . $blockName) ?>
                                </div>
                            </template>
                        <?php endforeach; ?>
                    </div>
                    <div class="relative"
                         :class="{
                            'am-search-content p-2 pt-6 w-full md:w-3/4': latestQuery.length >= minSearchLength || (
                                showOnFirstClick('recentSearches') || showOnFirstClick('popularSearches') || showOnFirstClick('browsingHistory')
                            ),
                            'w-full': latestQuery.length < minSearchLength && (
                                !showOnFirstClick('recentSearches') && !showOnFirstClick('popularSearches') && !showOnFirstClick('browsingHistory')
                            ),
                            'am-search-horizontal-view': isHorizontalView,
                            'p-2 pt-6': slider.recentlyViewed || slider.bestsellers,
                            'am-search-sidebar-disabled': isSidebarSectionsDisabled(),
                         }">
                        <div class="flex flex-nowrap flex-col" x-show="latestQuery.length < minSearchLength" x-cloak>
                            <?php if ($popupViewModel->isRecentlyViewedEnabled()): ?>
                                <template x-if="slider.recentlyViewed">
                                    <?= /** @noEscape */ $componentRegistry->render('search-recentlyViewed') ?>
                                </template>
                            <?php endif; ?>

                            <?php if ($popupViewModel->isBestsellersEnabled()): ?>
                                <template x-if="slider.bestsellers">
                                    <?= /** @noEscape */ $componentRegistry->render('search-bestsellers') ?>
                                </template>
                            <?php endif; ?>
                        </div>

                        <template x-if="!noResultsFound && !isHorizontalView && latestQuery.length >= minSearchLength">
                            <?= /** @noEscape */ $componentRegistry->render('search-products') ?>
                        </template>

                        <div class="p-4 box-border w-full h-full flex" x-show="noResultsFound && !errorMessage" x-cloak>
                            <div class="no-result flex justify-center content-center flex-row items-center flex-wrap align-center
                                 w-full h-full min-h-80 max-h-[90vh] text-center text-base word-break bg-gray-100"
                                 x-html="'<?= $escaper->escapeHtml(__('We could not find anything for&nbsp;<strong>"%s"</strong>'), ['strong']) ?>'.replace('%s', latestQuery)">
                            </div>
                        </div>

                        <div class="p-4 box-border w-full h-full flex" x-show="noResultsFound && errorMessage" x-cloak>
                            <div
                                class="text-center flex align-center w-full text-xl justify-center content-center flex-row bg-gray-100 items-center flex-wrap h-full max-h-[90vh] word-break"
                                x-html="errorMessage">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</div>

<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Request a Quote Hyva Compatibility
 */

use Amasty\RequestQuoteHyva\ViewModel\QuoteIcons;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Amasty\RequestQuoteHyva\Block\Product\Action;

/** @var Escaper $escaper */
/** @var Action $block */
/** @var ViewModelRegistry $viewModels */

$quoteIcons = $viewModels->require(QuoteIcons::class);
$product = $block->getProduct();
?>
<?php if ($product->isSaleable()): ?>
    <button type="submit"
            form="product_addtocart_form"
            title="<?= $escaper->escapeHtmlAttr(__('Update Quote')) ?>"
            class="amquote-addto-button btn btn-secondary"
            id="product-addtocart-button">
        <?= $quoteIcons->quoteHtml(
            'w-8 h-8 md:h-6 md:w-6 hover:text-black',
            25,
            25
        ) ?>
        <span class="hidden sm:block md:hidden lg:block">
            <?= $escaper->escapeHtml(__('Update Quote')) ?>
        </span>
    </button>
<?php endif; ?>

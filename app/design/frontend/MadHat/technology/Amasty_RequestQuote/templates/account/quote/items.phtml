<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Request a Quote Hyva Compatibility
 */

use Amasty\RequestQuote\Block\Account\Quote\Items;
use Magento\Framework\Escaper;

/** @var Items $block */
/** @var Escaper $escaper */
?>

<div class="quote-items order-items">
    <?php if ($block->isPagerDisplayed()): ?>
        <?= $block->getPagerHtml() ?>
    <?php endif ?>

    <div class="hidden lg:grid grid-cols-8 text-sm text-secondary mt-2">
        <div class="p-2 col-span-2"><?= $escaper->escapeHtml(__('Product Name')) ?></div>
        <div class="p-2"><?= $escaper->escapeHtml(__('Original Price')) ?></div>
        <div class="p-2"><?= $escaper->escapeHtml(__('Requested Price')) ?></div>
        <div class="p-2"><?= $escaper->escapeHtml(__('Approved Price')) ?></div>
        <div class="p-2"><?= $escaper->escapeHtml(__('Qty')) ?></div>
        <div class="p-2"><?= $escaper->escapeHtml(__('Discount Amount')) ?></div>
        <div class="p-2"><?= $escaper->escapeHtml(__('Subtotal')) ?></div>
    </div>
    <?php $items = $block->getItems(); ?>
    <?php foreach ($items as $item): ?>
        <?php if ($item->getParentItem()) {
            continue;
        } ?>
        <div class="parent-item mb-2">
            <div class="lg:grid grid-cols-8 py-2">
                <?= $block->getItemHtml($item) ?>
                <?php if ($item->getNotes()): ?>
                        <?php if ($item->getNotes()->getCustomerNote()): ?>
                        <div class="flex p-2 col-span-2">
                            <div>
                                <div class="font-semibold">
                                    <?= $escaper->escapeHtml(__('Customer Note:')) ?>
                                </div>
                                <div class="item-note amquote-note">
                                     <?= /* @noEscape */ nl2br($escaper->escapeHtml($item->getNotes()->getCustomerNote())) ?>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php if ($item->getNotes()->getAdminNote()): ?>
                            <div class="flex p-2 col-span-2">
                                <div>
                                    <div class="font-semibold">
                                        <?= $escaper->escapeHtml(__('Administrator Note:')) ?>
                                    </div>
                                    <div lass="item-note amquote-note">
                                        <?= /* @noEscape */ nl2br($escaper->escapeHtml($item->getNotes()->getAdminNote())) ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php endforeach; ?>
    <?php if ($block->isPagerDisplayed()): ?>
        <?= $block->getPagerHtml() ?>
    <?php endif ?>
</div>

<div class="justify-end border-t border-container mt-2">
    <div>
        <div class="text-right p-2 gap-2">
            <?= $block->getChildHtml('quote_totals') ?>
        </div>
    </div>
</div>

<div class="actions-toolbar">
    <div class="secondary flex gap-3 flex-col md:flex-row">
        <?php if ($block->isQuoteComplete()): ?>
            <?= $escaper->escapeHtml(__('Order ID')) ?>: <a class="action back" href="<?= $escaper->escapeHtml($block->getOrderViewUrl()) ?>">
                <?= $escaper->escapeHtml(__('# %1', $block->getQuote()->getReservedOrderId())) ?>
            </a>
        <?php endif; ?>
        <?php if ($block->isMoveShowed()): ?>
            <?php $moveShoppingCartFormData = json_decode($block->getPostData($block->getMoveUrl()), true) ?>
            <form action="<?= $escaper->escapeUrl($moveShoppingCartFormData['action']) ?>" method="post" class="inline-flex items-center justify-end">
                <?= $block->getBlockHtml('formkey'); ?>
                <input type="hidden" name="data" value='<?= /** @noEscape */ json_encode($moveShoppingCartFormData['data']) ?>'/>
                <button type="submit"
                        class="btn btn-primary"
                        title="<?= $escaper->escapeHtmlAttr(__('Move to Shopping Cart')) ?>">
                    <?= $escaper->escapeHtml(__('Move to Shopping Cart')) ?>
                </button>
            </form>

            <?php $moveCheckoutFormData = json_decode($block->getPostData($block->getMoveUrl(['redirect_url' => 'checkout'])), true) ?>
            <form action="<?= $escaper->escapeUrl($moveCheckoutFormData['action']) ?>" method="post" class="inline-flex items-center justify-end">
                <?= $block->getBlockHtml('formkey'); ?>
                <input type="hidden" name="data" value='<?= /** @noEscape */ json_encode($moveCheckoutFormData['data']) ?>'/>
                <button type="submit"
                        class="btn btn-primary"
                        title="<?= $escaper->escapeHtmlAttr(__('Move to Checkout')) ?>">
                    <?= $escaper->escapeHtml(__('Move to Checkout')) ?>
                </button>
            </form>
        <?php endif; ?>
    </div>
</div>

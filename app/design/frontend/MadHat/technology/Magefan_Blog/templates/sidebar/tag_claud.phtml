<?php
/**
 * Copyright © Magefan (<EMAIL>). All rights reserved.
 * Please visit Magefan.com for license details (https://magefan.com/end-user-license-agreement).
 */
?>
<?php
/**
 * Blog tag claud sidebar template
 *
 * @var $block \Magefan\Blog\Block\Sidebar\TagClaud
 */
?>
<?php $tags = $block->getTags(); ?>

<?php if (count($tags)) { ?>
<div class="block-tagclaud mb-10">
   <div class="title text-xl font-semibold mb-3">
        <?= $escaper->escapeHtml(__('Tags')) ?>
    </div>

    <div class="tagclaud-hld card">
    <?php foreach ($tags as $tag) { ?>
        <?php $title = $tag->getTitle() ?>
        <span class="<?= $escaper->escapeHtml($block->getTagClass($tag)) ?>">
            <a class="hover:underline" href="<?= $escaper->escapeUrl($tag->getTagUrl()) ?>" title="<?= $escaper->escapeHtml($title) ?>">
                <?= $escaper->escapeHtml($title) ?>
            </a>
        </span>
    <?php } ?>
    </div>
</div>
<?php } ?>

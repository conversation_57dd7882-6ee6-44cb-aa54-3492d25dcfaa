<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop By Hyva Compatibility
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Amasty\ShopbyBase\Model\FilterSetting;
use Amasty\Shopby\Model\Layer\Filter\Item;
use Amasty\Shopby\Block\Navigation\FilterRenderer\Category;
use Amasty\Shopby\Model\Layer\Filter\CategoryItems;
use Amasty\Shopby\Model\Source\SubcategoriesExpand;
use Amasty\ShopbyHyvaCompatibility\ViewModel\ProductListAjax;

/** @var Escaper $escaper */
/** @var FilterSetting $filterSetting */
/** @var Item $filterItem */
/** @var Category $block */
/** @var CategoryItems $filterItems */
/** @var ViewModelRegistry $viewModels */

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var ProductListAjax $productListAjax */
$productListAjax = $viewModels->require(ProductListAjax::class);

$filterItems = $block->getFilter()->getItems();
$categoryHelper = $block->getCategoryHelper();
$filterSetting = $block->getFilterSetting();
$filterSubcategoriesView = $block->isFolding();
$isFlyOut = !$filterSubcategoriesView;
$filterSubcategoriesType = $filterSubcategoriesView
    ? 'labelsFolding'
    : 'flyOut';
$filterFoldingClass = $filterSubcategoriesView ? '-folding' : '';
$isParent = $block->isParent($filterItems, $block->getPath());
$showProductQuantities = $filterSetting->getShowProductQuantities();

$filterPathItems = $filterItems->getItems($block->getPath());
$isByClickSetting = $filterSubcategoriesView && $filterSetting->getSubcategoriesExpand() == SubcategoriesExpand::BY_CLICK;
?>
<?php if (count($filterPathItems)): ?>
    <?php foreach ($filterPathItems as $filterItem): ?>
        <?php $currentPath = ($block->getPath() ?: $filterItems->getStartPath()) . '/'
            . $filterItem->getValueString();
        ?>
        <?php
        $filterItemId = uniqid();
        $filterItemUrl = $filterItem->getUrl();
        ?>
        <script>
            function initInput_<?= $escaper->escapeHtml($filterItemId); ?>() {
                return {
                    isSelected: Boolean(<?= /** @noEscape */ $block->checkedFilter($filterItem)?>),
                    open: false,
                    positioned: false,
                    selectInput(url) {
                        location.href = url;
                    },
                    openParentFilter(element) {
                        this.$nextTick(() => {
                            if (element.querySelector('.items-children .amshopby-link-selected')) {
                                this.open = true;
                            }
                        })
                    }
                }
            }
        </script>
        <li x-data="initInput_<?= $escaper->escapeHtml($filterItemId); ?>()" x-init="openParentFilter($el)"
            class="item item-category flex flex-wrap items-center py-1 hover:text-black
            <?php if (!$filterItem->getCount()): ?>
                <?= "-empty-value"; ?>
            <?php endif; ?>
            <?php if ($filterItems->getItemsCount($currentPath)): ?>
                <?= "-is-collapsible"; ?>
            <?php endif; ?>
            <?php if ($block->isExpandByClick($currentPath)): ?>
                <?= "-is-by-click"; ?>
            <?php else: ?>
                <?= "-is-expanded"; ?>
            <?php endif; ?>
            <?php if ($block->getFilter()->getItemsCount($currentPath)): ?>
                <?= "-filter-parent"; ?>
            <?php endif; ?>
            <?php if ($isFlyOut): ?>
                <?= " flyout-element"; ?>
            <?php endif; ?>"
            data-label="<?= /* @noEscape */ $block->stripTags($filterItem->getOptionLabel()) ?>"
            <?php if ($isFlyOut): ?>
            @mouseenter="positioned = true"
            @mouseleave="positioned = false"
            :class="{ 'relative': positioned }"
            <?php endif; ?>>
            <?php if ($block->isExpandByClick($currentPath)): ?>
                <button
                        type="button"
                        class="filter-options-title flex justify-between items-center mr-2 cursor-pointer hover:text-secondary-darker border-container"
                        @click="open = !open" :class="{'collapsed' : !open, 'opened' : open}"
                        :aria-expanded="open ? 'true' : 'false' "
                >
                        <span class="py-1 px-1 rounded border border-container">
                                <?= $heroicons->chevronDownHtml(
                                    'transition-transform transform duration-300 ease-in-out',
                                    12,
                                    12,
                                    [":class" => "{ 'rotate-180': open }"]
                                ); ?>
                         </span>
                </button>
            <?php endif; ?>

            <?php if ($filterItem->getCount() > 0): ?>
                <?php $inputId = 'amshopby-' . $block->getFilter()->getRequestVar() . '-' . $filterItem->getValueString() . ' - ' . uniqid() ?>
            <div class="filter-row category-filter-row flex items-center" role="radiogroup"
                 aria-label="<?= $escaper->escapeHtmlAttr($filterItem->getOptionLabel()) ?>">
                    <?php $filterUniqId = uniqid(); ?>
                    <label for="amfilter-id-<?= $escaper->escapeHtmlAttr($inputId) ?>">
                        <span class="sr-only">
                            <?= $escaper->escapeHtmlAttr($filterItem->getOptionLabel()) ?>
                        </span>
                    </label>
                    <input class="am-input mr-2 hidden"
                       id="amfilter-id-<?= $escaper->escapeHtmlAttr($inputId) ?>"
                        name="amshopby[<?= $escaper->escapeHtmlAttr($block->getFilter()->getRequestVar()); ?>][]"
                        value="<?= $escaper->escapeHtmlAttr($filterItem->getValueString()); ?>"
                        type="<?= /* @noEscape */ $block->getInputType() ?>"
                        <?php if ($productListAjax->canShowBlock()): ?>
                            @change="window.dispatchEvent(new CustomEvent('amFilterElementClick', {detail: {element: $el}}));"
                        <?php else: ?>
                            @change="selectInput('<?= $escaper->escapeUrl($filterItemUrl) ?>');"
                        <?php endif ?>
                        :checked="isSelected"
                    />
                    <a class="flex align-middle am-filter-item-<?= /* @noEscape */ $escaper->escapeHtmlAttr($filterItemId) ?>
                        <?= /* @noEscape */ ($isParent) ? 'amshopby-filter-parent' : '' ?>"
                        data-am-js="filter-item-default"
                        title="<?= $escaper->escapeHtmlAttr($filterItem->getOptionLabel()); ?>"
                        href="<?= $escaper->escapeUrl($filterItem->getUrl()) ?>"
                        :class="{'amshopby-link-selected': isSelected}"
                        @click<?php if ($productListAjax->canShowBlock()): ?>.prevent.stop<?php endif ?>="
                            isSelected = !isSelected;
                            <?php if ($productListAjax->canShowBlock()): ?>
                                window.dispatchEvent(new CustomEvent('amFilterElementClick', {detail: {element: $event.currentTarget}}));
                            <?php endif ?>
                        " <?= /* @noEscape */ $filterItem->isAddNofollow() ? ' rel="nofollow"' : '' ?>>
            <?php endif; ?>

            <?php if ($block->getFilter()->useLabelsOnly()): ?>
                <span class="label <?= /**@noEscape */ $block->getLevel() == 1 ? 'font-semibold' : ''; ?>">
                    <?= /* @noEscape */ $filterItem->getOptionLabel() ?></span>
            <?php else: ?>
                <?php if ($block->isShowThumbnail($filterItem->getValue())): ?>
                    <img src="<?= /* @noEscape */ $categoryHelper->getCategoryImageUrl($filterItem->getValue()) ?>"
                         class="am-category-image mr-1 inline-block"
                         title="<?= $escaper->escapeHtmlAttr($filterItem->getOptionLabel()); ?>"
                         alt="<?= $escaper->escapeHtmlAttr($filterItem->getOptionLabel()); ?>"
                         height="<?= /* @noEscape */ $categoryHelper->getCategoryFilterImageSize(); ?>"
                         width="<?= /* @noEscape */ $categoryHelper->getCategoryFilterImageSize(); ?>"/>
                <?php endif; ?>
                <?php if ($block->getFilter()->useLabelsAndImages()): ?>
                    <span class="label <?= /**@noEscape */ $block->getLevel() == 1 ? 'font-semibold' : ''; ?>">
                        <?= /* @noEscape */ $filterItem->getOptionLabel() ?></span>
                <?php endif; ?>
            <?php endif; ?>

            <?php if ($block->isShowProductQuantities($showProductQuantities)): ?>
                <span class="count text-primary ml-auto">
                        <?= /* @noEscape */ "(" . $filterItem->getCount() . ")"; ?>
                        <span class="filter-count-label sr-only">
                                <?php $title = ($filterItem->getCount() == 1) ? __('item') : __('items');?>
                                <?= /* @noEscape */ $escaper->escapeHtml($title) ?>
                        </span>
                    </span>
            <?php endif; ?>

            <?php if ($filterItem->getCount() > 0): ?>
                    </a>
                </div>
            <?php endif; ?>


            <?php if ($filterItems->getItemsCount($currentPath)): ?>
                <?php $level = $block->getLevel();?>
                    <ul class="items items-children w-full mt-2
                        level-<?= /* @noEscape */ $level . ' ' . $filterFoldingClass; ?>
                        <?= /**@noEscape*/ $isFlyOut ? 'absolute z-30 px-6 py-4 md:-ml-1 md:m-0 shadow-lg bg-container-lighter md:left-full left-0 md:-top-2 top-6 md:w-auto w-full' : 'ml-5';?>
                        <?= /**@noEscape*/ $isByClickSetting  ? 'ml-10' : ''?>"
                        <?php if ($isFlyOut): ?>
                        :class="{ 'hidden': !positioned, 'block': positioned }"
                        <?php endif; ?>
                        <?php if ($isByClickSetting):?>
                        x-show="open" x-cloak
                        <?php endif; ?>>
                        <?= /* @noEscape */ $block->renderChildrenItems($currentPath); ?>
                    </ul>
            <?php endif; ?>
        </li>
    <?php endforeach; ?>
<?php endif; ?>

<?php
/**
 * Schema.org markup for variant products (simple products that are children of configurable products)
 * This template generates Product schema with ProductGroup reference for individual variant pages
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

// Get the current variant product
$product = $block->getProduct();
$parentIds = $block->getParentIds();

if (!$product || empty($parentIds)) {
    return;
}

// Get view models
$productPriceViewModel = $viewModels->require(ProductPrice::class);

// For now, let's create a simple schema without complex dependencies
// This is a basic implementation that can be enhanced later

// Get product pricing
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);

// Create a simple variant schema for now
$variantName = $product->getName();

// Get brand information
$brandValue = '';
if ($product->getMadhatBrand()) {
    $brandValue = $product->getAttributeText('madhat_brand');
}

?>
<script type="application/ld+json">
[
    {
        "@context": "https://schema.org/",
        "@type": "Product",
        "name": "<?= $escaper->escapeJs($variantName) ?>",
        "sku": "<?= $escaper->escapeJs($product->getSku()) ?>",
        "url": "<?= $escaper->escapeJs($product->getProductUrl()) ?>",
        <?php if ($brandValue): ?>
        "brand": {
            "@type": "Brand",
            "name": "<?= $escaper->escapeJs($brandValue) ?>"
        },
        <?php endif; ?>
        "offers": {
            "@type": "Offer",
            "url": "<?= $escaper->escapeJs($product->getProductUrl()) ?>",
            "price": "<?= $escaper->escapeJs(round($finalPrice, 2)) ?>",
            "availability": "<?= $product->getIsSalable() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock' ?>"
        }
    }
]
</script>

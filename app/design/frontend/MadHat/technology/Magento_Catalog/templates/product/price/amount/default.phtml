<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\Pricing\Render\Amount;
use Magento\Framework\Escaper;

$viewMode = $block->getData('view_mode');
$viewIsGrid = $viewMode === 'grid';

$priceType = $block->getPriceType();
$oldPriceTypeClass = '';

if ($priceType == 'oldPrice') {
    $oldPriceTypeClass = 'text-cgrey-80 text-xs line-through text-xs-[1px]';
}

/** @var Amount $block */
/** @var Escaper $escaper */
?>

<span
    <?php if ($block->getPriceId()): ?>x-data x-id="['<?= $escaper->escapeJs($block->getPriceId()) ?>']"<?php endif; ?>
    class="price-container <?= $escaper->escapeHtmlAttr($block->getAdjustmentCssClasses()) ?>"
        <?= $block->getSchema() ? ' itemprop="offers" itemscope itemtype="http://schema.org/Offer"' : '' ?>
>
    <span <?php if ($block->getPriceId()): ?> :id="$id('<?= $escaper->escapeJs($block->getPriceId()) ?>')"<?php endif; ?>
        <?= ($block->getPriceDisplayLabel()) ? 'data-label="' . $escaper->escapeHtmlAttr($block->getPriceDisplayLabel() . $block->getPriceDisplayInclExclTaxes()) . '"' : '' ?>
        data-price-amount="<?= $escaper->escapeHtmlAttr($block->getDisplayValue()) ?>"
        data-price-type="<?= $escaper->escapeHtmlAttr($block->getPriceType()) ?>"
        class="<?= $oldPriceTypeClass ?> price-wrapper product-price-out flex flex-wrap <?= $viewIsGrid ? 'justify-between ml-auto' : '' ?> <?= $escaper->escapeHtmlAttr($block->getPriceWrapperCss()) ?>"
    >
    <?= $escaper->escapeHtml($block->formatCurrency($block->getDisplayValue(), (bool)$block->getIncludeContainer()), ['span']) ?>
    </span>
    <?php if ($block->hasAdjustmentsHtml()): ?>
        <?= $block->getAdjustmentsHtml() ?>
    <?php endif; ?>
    <?php if ($block->getSchema()): ?>
        <meta itemprop="price" content="<?= $escaper->escapeHtmlAttr($block->getDisplayValue()) ?>" />
        <meta itemprop="priceCurrency" content="<?= $escaper->escapeHtmlAttr($block->getDisplayCurrencyCode()) ?>" />
    <?php endif; ?>
</span>

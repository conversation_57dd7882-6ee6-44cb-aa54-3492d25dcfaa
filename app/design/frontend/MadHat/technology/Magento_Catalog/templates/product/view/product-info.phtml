<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Product $product */
$product = $productViewModel->getProduct();

?>
<div class="w-full mb-6">
    <div class="my-2 flex">
        <?= $block->getChildHtml('product.info.review') ?>
    </div>

    <!-- TODO For Developer -->
    <div class="pdp-article-ean-out">
        <div class="left-col">
            <span class=""><?= __('Artikel nr.') ?></span>
            <span class=""><?= $product->getSku() ?></span>
        </div>
        <div class="right-col">
            <div class="flex gap-2">
                <span class=""><?= __('EAN') ?></span>
                <span class=""><?= $product->getEan() ?></span>
            </div>
            <div class="flex gap-2">
                <span class="">Tillverkarens ArtNr</span>
                <span class="">1001020439</span>
            </div>
        </div>
    </div>

    <div class="price-section">
        <div role="group" aria-label="<?= $escaper->escapeHtmlAttr('Price') ?>">
            <?= $block->getChildHtml("product.info.price") ?>
        </div>
    </div>
    <!-- TODO DONE by Developer -->
    <div class="vat-message">
        <?= __('Inc. VAT excl. Delivery') ?>
    </div>

    <div class="flex flex-col sm:flex-row justify-between mb-7">
        <?= $block->getChildHtml("product.info.stockstatus") ?>
        <?= $block->getChildHtml("alert.urls") ?>
    </div>

    <?= $block->getChildHtml('product.info.form') ?>

    <div class="flex mb-4 justify-between sm:justify-start py-6 border-cgrey-75 border-t border-b">
        <div class="flex">
            <?php if ($product->isSaleable()): ?>
                <?= $block->getChildHtml("product.info.quantity") ?>
            <?php endif; ?>
        </div>
        <div class="flex w-2/4 md:w-2/5">
            <?php if ($product->isSaleable()): ?>
                <?= $block->getChildHtml("product.info.addtocart") ?>
            <?php endif; ?>
        </div>
        <div class="flex">
        <?= $block->getChildHtml('product.info.addtowishlist'); ?>
        </div>

    </div>

    <?php if ($product->isSaleable()): ?>
        <div class="flex justify-end">
            <?= $block->getChildHtml('addtocart.shortcut.buttons') ?>
        </div>
    <?php endif; ?>

    <?php if ($shortDescription = $productViewModel->getShortDescription(true, false)) { ?>
        <div class="mb-4 leading-relaxed product-description prose line-clamp-6">
            <?= /* @noEscape */ $shortDescription ?>
        </div>
    <?php } ?>



    <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
        <div class="py-4 my-2 tier-price-container">
            <?= /** @noEscape */ $tierPriceBlock ?>
        </div>
    <?php endif; ?>

    <?= $block->getChildHtml("product.info.additional") ?>
</div>

<?php
// Add schema.org markup for variant products
// Check if this is a simple product that is a variant of a configurable product
if ($product->getTypeId() === 'simple') {
    $configurableProduct = $viewModels->require(\Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable::class);
    $parentIds = $configurableProduct->getParentIdsByChild($product->getId());

    if (!empty($parentIds)) {
        // This is a variant product, include the variant schema template
        echo $block->getLayout()->createBlock(\Magento\Framework\View\Element\Template::class)
            ->setTemplate('Magento_Catalog::product/view/variant-schema.phtml')
            ->setProduct($product)
            ->setParentIds($parentIds)
            ->toHtml();
    }
}
?>

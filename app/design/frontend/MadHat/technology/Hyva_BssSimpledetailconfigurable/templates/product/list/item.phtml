<?php
/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category  BSS
 * @package   Hyva_BssSimpledetailconfigurable
 * <AUTHOR> Team
 * @copyright Copyright (c) 2023 BSS Commerce Co. ( http://bsscommerce.com )
 * @license   http://bsscommerce.com/Bss-Commerce-License.txt
 */

declare(strict_types=1);

use Hyva\BssSimpledetailconfigurable\ViewModel\Helper as ModuleViewModel;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\ProductPrice;
use Hyva\Theme\ViewModel\Wishlist;
use MadHat\Catalog\ViewModel\VariantAttributes;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Catalog\ViewModel\Product\OptionsData as ProductOptionsData;
use Magento\Framework\Escaper;
use MadHat\InventoryImport\Model\Source\ProductStatus;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$productOptionsViewmodel = $viewModels->require(ProductOptionsData::class);

$simpleDetailViewModel = $viewModels->require(ModuleViewModel::class);

$variantAttributeViewModel = $viewModels->require(VariantAttributes::class);

/** @var Magento\Catalog\Model\Product $product */
$product = $block->getData('product');
$imageDisplayArea = $block->getData('image_display_area');
$templateType = $block->getData('template_type');
$viewMode = $block->getData('view_mode');
$showDescription = $block->getData('show_description');
$showAddToWishlist = $wishlistViewModel->isEnabled();
$showAddToCompare = $compareViewModel->showInProductList();
$viewIsGrid = $viewMode === 'grid';

if (!$product) {
    return '';
}
$productId = $product->getId();
$options = $productOptionsViewmodel->getOptionsData($product);
$uniqueId = '_' . uniqid();

$hideDetails = $block->getData('hide_details') ?: false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?: false;

$imageCustomAttributes = $product->getData('image_custom_attributes')
    ?? $block->getData('image_custom_attributes')
    ?? [];

$productName = $catalogOutputHelper->productAttribute($product, $product->getName(), 'name');

/** @var ProductPrice $productPriceViewModel */
$productPriceViewModel = $viewModels->require(ProductPrice::class);
$regularPrice = $productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
$finalPrice = $productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);
if ($regularPrice > 0) {
    $discountPercentage = (($regularPrice - $finalPrice) / $regularPrice) * 100;
} else {
    $discountPercentage = 0;
}
?>

<?php if ($product->isSaleable()): ?>
<form method="post"
      action="<?= $escaper->escapeUrl($productViewModel->getAddToCartUrl($product, ['useUencPlaceholder' => true])) ?>"
      class="product-item-wrapper product_addtocart_form flex w-full <?= $viewIsGrid ? 'flex-col ' : 'flex-col sm:flex-row flex-wrap' ?>"
    <?php if ($product->getOptions()): ?>
        enctype="multipart/form-data"
    <?php endif; ?>
>
    <?= /** @noEscape */
    $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="product" value="<?= (int)$productId ?>"/>
    <?php foreach ($options as $optionItem): ?>
        <input type="hidden"
               name="<?= $escaper->escapeHtml($optionItem['name']) ?>"
               value="<?= $escaper->escapeHtml($optionItem['value']) ?>">
    <?php endforeach; ?>
    <?php else: ?>
    <div class="product-item-wrapper flex w-full <?= $viewIsGrid ? 'flex-col ' : 'flex-col sm:flex-row flex-wrap' ?>">
        <?php endif; ?>
        <?php /* Product Image */ ?>
        <div class="product-item-top relative <?= $viewIsGrid ? ' relative p-2 pb-0' : 'sm:w-3/6 lg:w-2/6' ?>">
            <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
               class="product photo product-item-photo block mx-auto <?= $viewIsGrid ? '' : 'w-full' ?>"
               tabindex="-1"
            >
                <?= $block->getImage($product, $imageDisplayArea)
                    ->setTemplate('Magento_Catalog::product/list/image.phtml')
                    ->setData('custom_attributes', $imageCustomAttributes)
                    ->setProductId($productId)
                    ->toHtml(); ?>
            </a>
            <?php if ($discountPercentage > 0) : ?>
                <div
                    class="absolute top-0 left-0 flex justify-center items-center bg-cred text-xs font-semibold text-cgrey-0 min-w-14 min-h-7 px-2">
                    <?= '-' . number_format($discountPercentage, 0) . '%' ?>
                </div>
            <?php endif; ?>
        </div>

        <div
            class="product-info flex flex-col grow relative <?= $viewIsGrid ? 'p-1 sm:p-2' : 'md:text-left sm:w-3/6 lg:w-4/6 p-4' ?>">
            <div class="product-min-height">
                <p class="product-brand-name <?= $viewIsGrid ? '' : 'md:text-left mb-0' ?>">
                    <?php
                    $brandValue = (string)$product->getAttributeText('madhat_brand');
                    if (strtolower($brandValue) == 'no value') {
                        $brandValue = 'brand name';
                    }
                    ?>
                    <?= /* @noEscape */
                    $brandValue; ?>
                </p>
                <?php $productNameStripped = $block->stripTags($product->getName(), null, true); ?>
                <?php if ($product->getTypeId() == "configurable" && $simpleDetailViewModel->getModuleConfig()->isShowName()): ?>
                    <script>
                        function initSimpleDetailItem_<?=$uniqueId?>() {
                            return {
                                productId: <?= $product->getId() ?>,
                                productName: "<?= /* @noEscape */ $catalogOutputHelper->productAttribute($product, $product->getName(), 'name') ?>",
                                jsonChildProduct: <?= /* @escapeNotVerified */ $simpleDetailViewModel->getJsonChildProductData($product->getId()); ?>,
                                eventListeners: {
                                    ['@configurable-selection-list-changed.window']($event) {
                                        if (this.productId == $event.detail.productId) {
                                            let product = this.jsonChildProduct.child[$event.detail.productIndex];
                                            this.productName = product.name;
                                        }
                                    }
                                }
                            }
                        }
                    </script>
                    <div x-data="initSimpleDetailItem_<?= $uniqueId ?>()" x-bind="eventListeners"
                         class="product-title <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                        <a class="product-item-link"
                           href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>" x-html="productName">
                            <?= /* @noEscape */
                            $catalogOutputHelper->productAttribute($product, $product->getName(), 'name') ?>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="product-title <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                        <a class="product-item-link"
                           href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>">
                            <?= /* @noEscape */
                            $catalogOutputHelper->productAttribute($product, $product->getName(), 'name') ?>
                        </a>
                    </div>
                <?php endif; ?>
                <?php $variantAttributes = $variantAttributeViewModel->getVariantAttributes($product); ?>
                <div class="variants-more-option-container">
                    <?php if (!empty($variantAttributes)): ?>
                        <div class="product-variants">
                            <?php foreach ($variantAttributes as $variantAttribute): ?>
                                <?php $variantAttributeValue = $variantAttributeViewModel->getAttributeValue($product, $variantAttribute); ?>
                                <div class="<?= /* noEscape */
                                $variantAttribute ?>-container">
                                <span class="icon-<?= /* noEscape */
                                $variantAttribute ?> break-words inline-block "
                                      title="<?= $variantAttributeValue ?>"><?= /* @noEscape */
                                    $variantAttributeValue; ?></span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <div class="more-options min-h-5 text-sm my-1 text-center">
                            <span><?= /* noEscape */
                                __('More options available') ?></span>
                        </div>
                    <?php else: ?>
                        <div class="product-short-description">
                            <p>
                                <?php if ($catalogOutputHelper->productAttribute($product, $product->getShortDescription(), 'short_description')) {
                                    // Remove all HTML tags
                                    echo strip_tags($catalogOutputHelper->productAttribute($product, $product->getShortDescription(), 'short_description'));
                                } ?>
                            </p>
                        </div>
                    <?php endif; ?>
                </div>
                <?php if ($showDescription): ?>
                    <div
                        class="mt-2 mb-1 items-center justify-center text-primary text-center <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                        <?= /* @noEscape */
                        $productViewModel->getShortDescriptionForProduct($product) ?>
                    </div>
                <?php endif; ?>


                <?php if (!$hideRatingSummary): ?>
                    <?php if ($product->getTypeId() == "configurable" && $simpleDetailViewModel->getModuleConfig()->isShowAdditionalInfo()): ?>
                        <script>
                            function initSimpleDetailStar_<?=$uniqueId?>() {
                                return {
                                    productId: <?= $product->getId() ?>,
                                    starFragment: 0,
                                    yellowHex: '#f6e05e',
                                    greyHex: '#cbd5e0',
                                    eventListeners: {
                                        ['@configurable-selection-list-changed.window']($event) {
                                            var self = this;
                                            var childProductId = $event.detail.productIndex;
                                            var starElements = this.$el.querySelectorAll('.rating-summary svg');
                                            var starSummary = this.$el.querySelector('.bss-summary-star');
                                            if (this.productId == $event.detail.productId) {
                                                fetch(BASE_URL + '/bss_sdcp/ajax_product/reviewStars/id/' + childProductId, {
                                                    headers: {
                                                        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                                                    },
                                                    method: "GET",
                                                    mode: "cors",
                                                    credentials: "include",
                                                }).then(response => {
                                                        return response.json()
                                                    }
                                                ).then(data => {
                                                    if (data.reviewsHtml) {
                                                        var docReview = document.createElement('div');
                                                        docReview.innerHTML = data.reviewsHtml;

                                                        var ratingValues = docReview.querySelectorAll('[itemprop="ratingValue"]');
                                                        var ratingCount = ratingValues.length;
                                                        var ratingValue = 0;
                                                        ratingValues.forEach(function (rating) {
                                                            ratingValue += parseInt(rating.innerHTML);
                                                        });
                                                        var averageRating = ratingValue / ratingCount;
                                                        if (starElements.length > averageRating && (averageRating - Math.floor(averageRating) > 0)) {
                                                            this.starFragment = averageRating - Math.floor(averageRating);
                                                        }
                                                        var starCrescent = this.$el.querySelector('.bss-crescent-star');
                                                        var cloneStarCrescent = starCrescent.cloneNode(true);
                                                        var stopElements = cloneStarCrescent.querySelectorAll('stop');
                                                        stopElements.forEach(function (element) {
                                                            element.removeAttribute(":stop-color");
                                                            element.removeAttribute(":offset");
                                                        });

                                                        starElements.forEach(function (star, index) {
                                                            if (index < Math.floor(averageRating)) {
                                                                var clone = starSummary.cloneNode(true);
                                                                clone.classList.remove('hidden');
                                                                clone.style.color = self.yellowHex;
                                                                star.parentNode.replaceChild(clone, star);
                                                            } else {
                                                                if (averageRating > index) {
                                                                    cloneStarCrescent.classList.remove('hidden');
                                                                    star.parentNode.replaceChild(cloneStarCrescent, star);
                                                                } else {
                                                                    var clone = starSummary.cloneNode(true);
                                                                    clone.classList.remove('hidden');
                                                                    clone.style.color = self.greyHex;
                                                                    star.parentNode.replaceChild(clone, star);
                                                                }
                                                            }
                                                        });
                                                    } else {
                                                        starElements.forEach(function (star) {
                                                            var clone = starSummary.cloneNode(true);
                                                            clone.classList.remove('hidden');
                                                            clone.style.color = self.greyHex;
                                                            star.parentNode.replaceChild(clone, star);
                                                        });
                                                    }
                                                });
                                            }
                                        }
                                    }
                                }
                            }
                        </script>
                        <div class="star-rating text-xs <?= $viewIsGrid ? '' : 'md:mx-0 md:w-auto' ?>"
                             x-data="initSimpleDetailStar_<?= $uniqueId ?>()" x-bind="eventListeners">
                            <?= $block->getReviewsSummaryHtml($product, 'short') ?>
                            <svg xmlns="http://www.w3.org/2000/svg" class="fill-current w-6 h-6 hidden bss-summary-star"
                                 viewBox="3 0 20 20"
                                 fill="currentColor">
                                <path
                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371
                            1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54
                            1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1
                            1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="3 0 20 20" fill="currentColor"
                                 class="w-6 h-6 bss-crescent-star hidden">
                                <defs>
                                    <linearGradient id="partialFill<?= /* @noEscape */
                                    $uniqueId ?>">
                                        <stop offset="0%" :stop-color="yellowHex"/>
                                        <stop :offset="(starFragment * 100) + '%'" :stop-color="yellowHex"/>
                                        <stop :offset="(starFragment * 100) + '%'   " :stop-color="greyHex"/>
                                        <stop offset="100%" :stop-color="greyHex"/>
                                    </linearGradient>
                                </defs>
                                <g fill="url(#partialFill<?= $uniqueId ?>)">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969
                                0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54
                                1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1
                                0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                </g>
                            </svg>
                        </div>
                    <?php else: ?>
                        <div class="star-rating text-xs <?= $viewIsGrid ? '' : 'md:mx-0 md:w-auto' ?>">
                            <?= $block->getReviewsSummaryHtml($product, 'short') ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

                <script>
                    function initPriceBox_<?= /** @noEscape */ $uniqueId ?>() {
                        return {
                            updatePrice(priceData) {
                                const regularPriceLabel = this.$root.querySelector('.normal-price .price-label');
                                const regularPriceElement = this.$root.querySelector('.normal-price [data-price-type=finalPrice].price-wrapper .price');
                                const basePriceElement = this.$root.querySelector('.normal-price [data-price-type=basePrice].price-wrapper .price');
                                if (regularPriceLabel) {
                                    if (priceData.finalPrice.amount < priceData.oldPrice.amount) {
                                        regularPriceLabel.classList.add('hidden');
                                    } else {
                                        regularPriceLabel.classList.remove('hidden');
                                    }
                                }
                                regularPriceElement.innerText = hyva.formatPrice(priceData.finalPrice.amount);
                                basePriceElement && (basePriceElement.innerText = hyva.formatPrice(priceData.basePrice.amount));
                            }
                        }
                    }
                </script>
                <div class="product-price-box-wrapper text-gray-900"
                     x-data="initPriceBox_<?= /** @noEscape */
                     $uniqueId ?>()"
                     @update-prices-<?= (int)$productId ?>.window="updatePrice($event.detail);"
                >
                    <?= /* @noEscape */
                    $productListItemViewModel->getProductPriceHtml($product) ?>

                    <?php if ($product->isAvailable() && !$hideDetails): ?>
                        <?= $block->getProductDetailsHtml($product) ?>
                    <?php endif; ?>

                    <?php if ($showDescription): ?>
                        <div
                            class="mt-2 mb-1 items-center justify-center text-primary text-center <?= $viewIsGrid ? '' : 'md:text-left' ?>">
                            <?= /* @noEscape */
                            $productViewModel->getShortDescriptionForProduct($product) ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div
                    class="product-item-wishlist-compare flex <?= $viewIsGrid ? 'absolute right-8 top-0 sm:top-2 w-4' : 'space-x-2 md:text-left' ?>">
                    <?php if ($showAddToWishlist): ?>
                        <button x-data="initWishlist()"
                                @click.prevent="addToWishlist(<?= (int)$productId ?>)"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Wish List')) ?>"
                                type="button"
                                class="flex items-center justify-center text-gray-500 hover:text-yellow-500 mt-1">
                            <?= $heroicons->heartHtml("w-5 h-5", 25, 25) ?>
                        </button>
                    <?php endif; ?>
                    <?php if ($showAddToCompare): ?>
                        <button x-data="initCompareOnProductList()"
                                @click.prevent="addToCompare(<?= (int)$productId ?>)"
                                aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Compare')) ?>"
                                type="button"
                                class="flex items-center justify-center text-gray-500 hover:text-yellow-500 mt-1">
                            <?= $heroicons->scaleHtml("w-5 h-5", 25, 25) ?>
                        </button>
                    <?php endif; ?>
                    <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                        <?= $addToBlock->setProduct($product)->getChildHtml() ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="product-add-to-cart-btn">
                <div class="mt-auto space-x-4 flex flex-wrap make-center items-center <?= $viewIsGrid ? '' : 'pt-6' ?>">
                    <div class="<?= $viewIsGrid ? 'w-full' : 'left-4 bottom-4 w-full md:w-auto' ?>">
                        <?php if ($product->isSaleable()) : ?>
                            <button class="add-to-cart-btn
                        <?= $viewIsGrid ? 'mr-auto' : 'mr-auto md:mr-0' ?>"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
                            >
                            <span class="ml-2 inline <?= $viewIsGrid ? 'md:ml-0 lg:ml-2 lg:inline' : '' ?>">
                                <?= $escaper->escapeHtml(__('Add to Cart')) ?>
                            </span>
                            </button>

                        <?php else: ?>
                            <button class="add-to-cart-btn add-to-cart-disabled disable
                            <?= $viewIsGrid ? 'mr-auto' : 'mr-auto md:mr-0' ?>"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
                            >
                            <span class="ml-2 inline <?= $viewIsGrid ? 'md:ml-0 lg:ml-2 lg:inline' : '' ?>">
                                <?= $escaper->escapeHtml(__('Add to Cart')) ?>
                            </span>
                            </button>
                        <?php endif; ?>
                    </div>

                </div>

                <?php if ($product->getTypeId() == "simple" && $product->isSaleable()): ?>
                    <div
                        class="product-stock text-center text-[11px] sm:text-xs text-cgrey-80 flex items-center pt-1 <?= $viewIsGrid ? ' justify-center' : 'make-center' ?>">
                        <?php $madhatInventoryStatus = (string)$product->getAttributeText('madhat_inventory_status'); ?>
                        <?php if ($madhatInventoryStatus == ProductStatus::LABEL_IN_STOCK): ?>
                            <div class="flex items-center">
                                <span class="bg-cgreen size-2.5 mr-1 rounded-full">&nbsp;</span>
                                <?= /* @noEscape */ $madhatInventoryStatus; ?>
                            </div>
                        <?php elseif ($madhatInventoryStatus == ProductStatus::LABEL_AVAILABLE_ON_DEMAND
                            || $madhatInventoryStatus == ProductStatus::LABEL_AVAILABLE_ON_PREORDER
                            || $madhatInventoryStatus == ProductStatus::LABEL_ETA_2_DAYS
                            || $madhatInventoryStatus == ProductStatus::LABEL_ETA_3_DAYS
                            || $madhatInventoryStatus == ProductStatus::LABEL_ETA_7_DAYS
                            || $madhatInventoryStatus == ProductStatus::LABEL_ETA_14_DAYS
                            || $madhatInventoryStatus == ProductStatus::LABEL_ETA_30_DAYS
                        ): ?>
                            <div class="flex items-center">
                                <span class="bg-yellow-500 size-2.5 mr-1 rounded-full">&nbsp;</span>
                                <?= /* @noEscape */$madhatInventoryStatus; ?>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center justify-center">
                                <span class="bg-cred size-2.5 mr-1 rounded-full">&nbsp;</span>
                                <?= /* @noEscape */ $madhatInventoryStatus;?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php elseif($product->getTypeId() == "simple" && !$product->isSaleable()): ?>
                    <div class="product-stock text-center text-[11px] sm:text-xs text-cgrey-80 flex items-center pt-1 <?= $viewIsGrid ? ' justify-center' : 'make-center' ?>">
                        <div class="flex items-center justify-center">
                            <span class="bg-cred size-2.5 mr-1 rounded-full">&nbsp;</span>
                            <?php $madhatInventoryStatus = (string)$product->getAttributeText('madhat_inventory_status'); ?>
                            <?php if ($madhatInventoryStatus == ProductStatus::LABEL_IN_STOCK): ?>
                                <?= /* @noEscape */ __(ProductStatus::LABEL_OUT_OF_STOCK);?>
                            <?php else: ?>
                                <?= /* @noEscape */$madhatInventoryStatus; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <?php if ($product->isSaleable()): ?>
</form>
<?php else: ?>
    </div>
<?php endif; ?>

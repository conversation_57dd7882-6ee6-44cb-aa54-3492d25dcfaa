<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop By Hyva Compatibility
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Hyva\Theme\Model\ViewModelRegistry;
use Amasty\ShopbyHyvaCompatibility\ViewModel\ProductListAjax;
use Amasty\ShopbyHyvaCompatibility\ViewModel\JsInit;
use Amasty\Shopby\Block\Product\ProductList\Ajax;

/** @var Escaper $escaper */
/** @var Ajax $block */
/** @var ViewModelRegistry $viewModels */

/** @var ProductListAjax $productListAjax */
$productListAjax = $viewModels->require(ProductListAjax::class);

/** @var JsInit $jsInitViewModel */
$jsInitViewModel = $viewModels->require(JsInit::class);

$additionalOptions = ['page' => 'p'];
?>
<script>
    /**
     * Cut zeroes from value
     *
     * @param {Number} digits - signs after dot
     * @returns {String | Number}
     */
    // eslint-disable-next-line no-extend-native
    Number.prototype.amToFixed = function (digits, hideDigitsAfterDot) {
        var value = this.toFixed(digits);

        if (hideDigitsAfterDot && parseInt(value) == this) {
            value = parseInt(value)
        }

        return value;
    };

    function shopBy () {
        return {
            submitByClick: <?= (int) $block->submitByClick() ?>,
            isAjaxSettingEnabled: <?= (int) $productListAjax->isAjaxSettingEnabled() ?>,
            scrollUp: <?= (int) $block->scrollUp() ?>,
            clearUrl: '<?= $escaper->escapeJs($block->getClearUrl()) ?>',
            currentCategoryId: <?= (int) $block->getCurrentCategoryId() ?>,
            isCategorySingleSelect: <?= (int) $block->isCategorySingleSelect() ?>,
            isMemorizerAllowed: <?= (int) $block->isMemorizingAllowed() ?>,
            isAjax: <?= (int) $block->canShowBlock() ?>,
            isLoading: false,
            filters: {},
            cacheKey: null,
            cached: [],
            response: null,
            memorizeData: [],
            showButtonClick: false,
            startAjax: false,
            controller: new AbortController(),
            signal: null,
            currentFilters: [],
            toolbarOptions: <?= /* @noEscape */ $productListAjax->getToolbarWidgetOptions($additionalOptions) ?>
                    .productListToolbarForm || {},
            element: null,
            toolbarElements: {
                mode: '[data-role="mode-switcher"]',
                sortDir: '.sorter-action',
                sort: '[data-role="sorter"]',
                limiter: '[data-role="limiter"]'
            },
            options: {
                isAjax: 0,
                collectFilters: 0,
                clearUrl: null,
                delta: [],
                deltaFrom: 0,
                deltaTo: 0,
                curRate: '1',
                digitsAfterDot: 2
            },
            classes: {
                savedFilterValues: 'amshopby-saved-values',
                allTop: 'amshopby-all-top-filters-append-left',
                filterTop: 'amshopby-filter-top'
            },
            selectors: {
                top_nav: '.amasty-catalog-topnav',
                top_navigation: '.catalog-topnav .block-filter-top',
                title_head: '[data-ui-id="page-title-wrapper"]',
                products_wrapper: '#amasty-shopby-product-list, .search.results',
                fromToWidget: '[data-am-js="fromto-widget"]',
                filterForm: 'form[data-amshopby-filter]',
                filterFormAttr: 'form[data-amshopby-filter={attr}]',
                filterItems: '[class*="am-filter-items"]',
                filterName: '[name="amshopby[{name}][]"]',
                sidebar: '.sidebar',
                topNav: '.catalog-topnav',
                filterRequestVar: '[data-amshopby-filter-request-var="cat"]',
                filterOptionsContent: '.filter-options-content',
                filterOptionsItem: '.filter-option',
                removeItems: '.filter-current .items .radio-remove-item',
                removeAllItems: '.filter-current .items .amshopby-remove-item',
                improvedSortingWrapper: '[x-data="amSortingDirection()"]'
            },
            selectors_top_filters: {
                sidebar: '.sidebar.sidebar-main',
                sidebarList: '.sidebar.sidebar-main #narrow-by-list',
                layeredFilter: '#layered-filter-block',
                blockFilter: '.block-filter-top',
            },
            isImrovedSorting: false,

            initShopBy() {
                const self = this;

                <?php if ($block->canShowBlock()): ?>
                    if (typeof window.history.replaceState === "function") {
                        window.history.replaceState({url: document.URL}, document.title);

                        setTimeout(() => {
                            /*
                            Timeout is a workaround for iPhone
                            Reproduce scenario is following:
                            1. Open category
                            2. Use pagination
                            3. Click on product
                            4. Press "Back"
                            Result: Ajax loads the same content right after regular page load
                            */
                            window.onpopstate = e => {
                                if (e.state) {
                                    if (this.submitByClick) {
                                        this.showButtonClick = true;
                                    }
                                    self.callAjax(e.state.url, []);
                                }
                            };
                        }, 0);
                    }

                    self.qsa('.pages-items a').forEach(link => {
                        link.addEventListener('click', e => {
                            self.pagerEvent(e);
                            self.stopEvents(e);
                        }, false);
                    });
                <?php endif ?>

                this.signal = this.controller.signal;

                <?php if (!$jsInitViewModel->isSingleChoiceMode()): ?>
                    this.qsa(this.selectors.removeItems).forEach(item => {
                        const name = item.getAttribute('data-container'),
                            value = item.getAttribute('data-value');

                        let filter = document.createElement('div');
                        filter.setAttribute('class', 'filter-option');
                        filter.innerHTML = '<form data-amshopby-filter="attr_'+ name +
                                '" class="am-filter-items"><input value="' + value +
                                '" type="hidden" name="amshopby[' + name + '][]" /></div>';

                        this.qs('#layered-filter-block-content').appendChild(filter);
                    });
                <?php endif ?>

                self.isImrovedSorting = !!self.qs(self.selectors.improvedSortingWrapper);

                if (self.qs(self.toolbarElements.mode)) {
                    self.addToolbarEvent(self.toolbarElements.mode, 'click', 'mode');
                }

                if (self.qs(self.toolbarElements.sortDir)) {
                    self.addToolbarEvent(self.toolbarElements.sortDir, 'click', 'direction');
                }

                if (self.qs(self.toolbarElements.sort)) {
                    self.addToolbarEvent(self.toolbarElements.sort, 'change', 'order');
                }

                if (self.qs(self.toolbarElements.limiter)) {
                    self.addToolbarEvent(self.toolbarElements.limiter, 'change', 'limit');
                }

                this.topFiltersCheckOnMobile();
                this.setSelectedPrtoductSwatch();
            },

            setSelectedPrtoductSwatch() {
                this.qsa(this.selectors.removeAllItems).forEach(item => {
                    if (item.getAttribute('data-massset')) {
                        this.setSwatchMassToProducts(
                            item.getAttribute('data-massset'),
                            item.getAttribute('data-attribute-id')
                        )
                    }

                    if (item.getAttribute('data-swatchset')) {
                        this.setSwatchToProducts(
                            item.getAttribute('data-swatchset'),
                            item.getAttribute('data-attribute-id')
                        )
                    }
                });
            },

            addToolbarEvent(element, event, paramValue) {
                const self = this,
                    el = self.qs(element);

                el.removeAttribute('@click.prevent');
                el.removeAttribute('@change');
                el.replaceWith(el.cloneNode(true));

                self.qs(element).addEventListener(event, e => {
                    self.stopEvents(e);

                    let value;

                    if (paramValue === 'order' || paramValue === 'limit') {
                        value = e.currentTarget.value;
                    } else if (paramValue === 'direction') {
                        const classNames = e.currentTarget.getAttribute('class');
                        value = classNames.indexOf('sort-desc') === -1 ? 'desc' : 'asc';
                    } else {
                        value = e.currentTarget.getAttribute('data-value');
                    }

                    self.changeUrl(
                        paramValue,
                        value
                    );
                }, false);
            },

            /**
             * @public
             * @param {Object} element
             * @return {Boolean}
             */
            isFinderAndCategory(element) {
                return location.href.indexOf('find=') !== -1
                && element.type === 'radio'
                && element.name === 'amshopby[cat][]';
            },

            setSwatchBorder(element) {
                element.classList.toggle('border-container-darker');
                element.classList.toggle('border-container-lighter');
                element.classList.toggle('ring');
                element.classList.toggle('ring-primary');
                element.classList.toggle('ring-opacity-50');
                element.classList.toggle('amshopby-link-selected');
            },

            /**
             * @public
             * @param {String} name
             * @param {String} value
             * @return {void}
             */
            setDefault(name, value) {
                var self = this,
                    valueSelector = self.selectors.filterName.replace('{name}', name),
                    type,
                    selected;

                this.qsa(valueSelector).forEach((filter, index) => {
                    type = filter.tagName;

                    switch (type) {
                        case 'SELECT':
                            if (name === 'price') {
                                filter.querySelectorAll('option').forEach((element, index) => {
                                    if (self.toValidView(element.value.split('-')) === value) {
                                        element.selected = false;
                                    }
                                });

                                filter.querySelector('[value="' + value + '"]').selected = true;
                            }
                        break;

                        case 'INPUT':
                            selected = '';

                            if (filter.getAttribute('type') !== 'text' && filter.getAttribute('type') !== 'hidden') {
                                this.qsa(valueSelector + '[value="' + value + '"]').forEach(selected => {
                                    selected.checked = false;

                                    const selectedSibling = [...selected.parentNode.children]
                                        .filter((child) => child !== selected);
                                    selectedSibling.forEach(sibling => { sibling.classList.remove('selected'); });
                                });
                            } else if ((filter.getAttribute('type') === 'hidden'
                                && self.isEquals(name, filter.value, value))
                                || name === 'price'
                            ) {
                                filter.value = '';
                            }
                        break;
                    }
                });
            },

            /**
             * @public
             * @param {Array} values
             * @return {String}
             */
            toValidView(values) {
                values[0] = values[0] ? parseFloat(values[0]).toFixed() : values[0];
                values[1] = values[1] ? parseFloat(values[1]).toFixed() : values[1];

                return values[0] + '-' + values[1];
            },

            /**
             * @public
             * @param {String} name
             * @param {String} filterValue
             * @param {String} value
             * @return {Boolean}
             */
            isEquals(name, filterValue, value) {
                var values = value.split('-'),
                    filterValues = filterValue.split('-');

                if (values.length > 1) {
                    filterValue = this.toValidView(filterValues);
                    value = this.toValidView(values);
                }

                return filterValue === value;
            },

            /**
             * @public
             * @param {String | null} element
             * @param {String | null} clearUrl
             * @param {Boolean | null} [clearFilter]
             * @param {Boolean} [isSorting]
             * @return {Array}
             */
            prepareTriggerAjax(element, clearUrl, clearFilter, isSorting) {
                let self = this,
                    selectors = this.selectors,
                    forms = this.qsa(this.selectors.filterForm),
                    attributeName,
                    excludedFormSelector,
                    existFields = [],
                    savedFilters = [],
                    className,
                    startPos,
                    endPos,
                    filterClass,
                    isPriceType,
                    serializeForms = [],
                    isPriceExist,
                    data;

                if (typeof this.element !== 'undefined' && clearFilter) {
                    attributeName = selectors.filterFormAttr
                        .replace('{attr}', this.element
                            .closest(selectors.filterOptionsContent)
                            .querySelector('form')
                            .getAttribute('data-amshopby-filter'));
                    excludedFormSelector = (this.element.closest(selectors.sidebar)
                        ? selectors.topNav : selectors.sidebar) + ' ' + attributeName;

                    forms = excludingElement(forms, excludedFormSelector);
                }

                forms.forEach((item, index) => {
                    className = '';

                    if (item.closest(selectors.filterItems)) {
                        className = item.closest(selectors.filterItems).className;
                    } else if (item.querySelector(selectors.filterItems)) {
                        className = item.querySelector(selectors.filterItems).className;
                    }

                    startPos = className.indexOf('am-filter-items');
                    endPos = className.indexOf(' ', startPos + 1) === -1 ? 100 : className.indexOf(' ', startPos + 1);
                    filterClass = className.substring(startPos, endPos);

                    isPriceType = item.closest(selectors.filterOptionsItem)
                        .querySelectorAll(selectors.fromToWidget).length;

                    if (filterClass && existFields[filterClass] && !isPriceType) {
                        forms[index] = '';
                    } else {
                        existFields[filterClass] = true;
                    }
                });

                forms.forEach(form => {
                    const serializeData = self.serializeForm(form);
                    if (serializeData.length) {
                        serializeForms = [...serializeForms, ...serializeData];
                    }
                });

                isPriceExist = false;

                // eslint-disable-next-line consistent-return
                serializeForms.map(item => {
                    if (item.name === 'amshopby[price][]') {
                        isPriceExist = true;

                        return false;
                    }
                });

                if (!isPriceExist && savedFilters) {
                    // eslint-disable-next-line no-shadow
                    savedFilters.forEach(element => {
                        serializeForms.push(self.serializeForm(element)[0]);
                    });
                }

                data = this.normalizeData(serializeForms, isSorting, clearFilter);
                data.clearUrl = data.clearUrl ? data.clearUrl : clearUrl;

                // eslint-disable-next-line no-param-reassign
                element = element || document;

                if (this.options.delta.length) {
                    data = data.concat(this.options.delta);
                }

                if (element && element !== document && element.closest('.price-ranges')) {
                   data.push({name: 'price-ranges', value: 1});
                }

                window.dispatchEvent(
                    new CustomEvent(
                        'amshopby-submit-filters',
                        {
                            detail: {
                                data: data,
                                clearFilter: clearFilter,
                                isSorting: isSorting
                            }
                        }
                    )
                );

                return data;
            },

            /**
             * @public
             * @param {Array} data
             * @param {Boolean} [isSorting]
             * @param {Boolean} [clearFilter]
             * @return {Array}
             */
            normalizeData(data, isSorting, clearFilter) {
                var self = this,
                    normalizedData = [],
                    clearUrl;

                data.forEach(item => {
                    if (item && item.value.trim() !== '' && item.value !== '-1') {
                        // eslint-disable-next-line vars-on-top
                        let isNormalizeItem = normalizedData.find(normalizeItem => {
                            return normalizeItem.name === item.name && normalizeItem.value === item.value
                                || item.name === 'amshopby[price][]' && normalizeItem.name === item.name;
                        });

                        if (!isNormalizeItem) {
                            if (item.name === 'amshopby[price][]') {
                                item.value = self.normalizePrice(item.value);
                            }

                            normalizedData.push(item);

                            if (self.isCategorySingleSelect === 1
                                && item.name === 'amshopby[cat][]'
                                && item.value !== self.currentCategoryId
                                && !clearFilter
                                && !isSorting
                            ) {
                                clearUrl = this.qs('*' + self.selectors.filterRequestVar +
                                        ' *[value="' + item.value + '"]')
                                        .closest('.item').querySelector('a').getAttribute('href');
                            }
                        }
                    }
                });

                normalizedData = this.groupDataByName(normalizedData);

                if (clearUrl) {
                    const locationData = clearUrl.split("?");
                    if (locationData.length > 1) {
                        const url = locationData[0],
                            urlParams = locationData[1].split('&');

                        urlParams.map((param, index) => {
                            const paramKey = param.split(/=(.*)/)[0];

                            if (!this.qs('.amshopby-remove-item[data-container="'+ paramKey +'"]') &&
                                    this.qs('[name="amshopby['+ paramKey +'][]"]')) {
                                urlParams.splice(index, 1);
                            }
                        });

                        clearUrl = url + '?' + urlParams.join('&');
                    }

                    normalizedData.clearUrl = clearUrl;
                }

                return normalizedData;
            },

            /**
             * @public
             * @param {Array} formData
             * @return {Array}
             */
            groupDataByName(formData) {
                var hash = Object.create(null);

                return formData.reduce((result, currentValue) => {
                    if (!hash[currentValue.name]) {
                        hash[currentValue.name] = {};
                        hash[currentValue.name].name = currentValue.name;
                        result.push(hash[currentValue.name]);
                    }

                    if (hash[currentValue.name].value) {
                        hash[currentValue.name].value += ',' + currentValue.value;
                    } else {
                        hash[currentValue.name].value = currentValue.value;
                    }

                    return result;
                }, []);
            },

            /**
             * @public
             * @param {String} value
             * @return {String}
             */
            normalizePrice(value) {
                var result = value.split('-'),
                    i;

                for (i = 0; i < result.length; i++) {
                    if (typeof result[i] == 'undefined') {
                        result[i] = 0;
                    }

                    result[i] = this.processPrice(true, result[i])
                            .amToFixed(2, this.getHideDigitsAfterDot());
                }

                return result.join('-').replace(/[ \r\n]/g, '');
            },

            /**
             * @public
             * @param {Boolean} toBasePrice
             * @param {String | Number} input
             * @param {String | Number} [delta]
             * @returns {Number}
             */
            processPrice(toBasePrice, input, delta) {
                var rate = Number(this.options.curRate),
                    inputPrice = Number(input);

                // eslint-disable-next-line no-param-reassign
                delta = typeof delta !== 'undefined' ? Number(delta) : 0;

                // eslint-disable-next-line no-nested-ternary
                return this.isBaseCurrency()
                    ? inputPrice
                    // eslint-disable-next-line no-extra-parens
                    : (toBasePrice ? (inputPrice / rate) : ((inputPrice * rate) + delta));
            },

            /**
             * @public
             * @return {Number}
             */
            getHideDigitsAfterDot() {
                const value = +this.qs('[name="amshopby[price][]"]').getAttribute('data-digits-after-dot');
                return Number.isNaN(value) ? 0 : value;
            },

            /**
             * @public
             * @returns {Boolean}
             */
            isBaseCurrency() {
                return Number(this.options.curRate) === 1;
            },

            showButtonCounter(count) {
                let data = {
                    count:parseInt(count),
                    disabled: false
                };

                window.dispatchEvent(new CustomEvent('amApplyButtonData', { detail: data }));
            },

            pagerEvent(e) {
                let newUrl = e.currentTarget.getAttribute('href'),
                    updatedUrl = null,
                    urlPaths = newUrl.split('?'),
                    urlParams = urlPaths[1] ? urlPaths[1].split('&') : [];

                for (let i = 0; i < urlParams.length; i++) {
                    if (urlParams[i].indexOf("p=") === 0) {
                        let pageParam = urlParams[i].split('=');
                        updatedUrl = this.getNewClearUrl(pageParam[0], pageParam[1] > 1 ? pageParam[1] : '');
                        break;
                    }
                }

                if (!updatedUrl) {
                    updatedUrl = e.currentTarget.getAttribute('href');
                }
                updatedUrl = updatedUrl.replace('amp;', '');

                this.prepareTriggerAjax(document, updatedUrl, false, true);

                window.scrollTo({
                    top: this.qs(this.selectors.products_wrapper).offsetTop,
                    behavior: 'smooth'
                })
            },

            getUrlParams() {
                let decode = window.decodeURIComponent,
                    urlPaths = window.location.href.split('?'),
                    urlParams = urlPaths[1] ? urlPaths[1].split('&') : [],
                    params = {},
                    parameters, i;

                for (i = 0; i < urlParams.length; i++) {
                    parameters = urlParams[i].split('=');
                    params[decode(parameters[0])] = parameters[1] !== undefined ?
                        decode(parameters[1].replace(/\+/g, '%20')) :
                        '';
                }

                return params;
            },

            getCurrentLimit() {
                return this.getUrlParams()[this.toolbarOptions.limit] || this.toolbarOptions.limitDefault;
            },

            getCurrentPage() {
                return this.getUrlParams()[this.toolbarOptions.page] || 1;
            },

            changeUrl(param, paramValue) {
                let defaultValue = this.toolbarOptions[param + 'Default'],
                    paramName = this.toolbarOptions[param],
                    urlPaths = this.toolbarOptions.url.split('?'),
                    baseUrl = urlPaths[0],
                    paramData = this.getUrlParams(),
                    currentPage = this.getCurrentPage(),
                    newPage;

                /**
                 * calculates the page on which the first item of the current page will
                 * be with the new limit and sets that number as the new page
                 */
                if (currentPage > 1 && paramName === this.toolbarOptions.limit) {
                    newPage = Math.floor(this.getCurrentLimit() * (currentPage - 1) / paramValue) + 1;

                    if (newPage > 1) {
                        paramData[this.toolbarOptions.page] = newPage;
                    } else {
                        delete paramData[this.toolbarOptions.page];
                    }
                }

                paramData[paramName] = paramValue;

                if (paramValue === defaultValue.toString() && !this.toolbarOptions.post) {
                    delete paramData[paramName];
                }
                paramData = Object.keys(paramData).length === 0
                    ? ''
                    : '?' + (new URLSearchParams(paramData));

                if (this.isImrovedSorting && paramValue === 'asc') {
                    paramData = paramData ? paramData + '&product_list_dir=asc' : '';
                }

                if (this.isAjax) {
                    this.prepareTriggerAjax(document, baseUrl + paramData, false, true);
                } else {
                    location.href = baseUrl + paramData;
                }
            },

            getNewClearUrl(key, value, page) {
                var url = new URL(window.location.href),
                    params = new window.URLSearchParams(url.search);

                if (value !== '') {
                    params.set(key, value);
                } else {
                    params.delete(key);
                }

                if (page) {
                    params.set('p', page);
                } else if (key !== 'p') {
                    params.delete('p');
                }

                url.search = params;

                return window.decodeURIComponent(url.toString());
            },

            callAjax(clearUrl, data, pushState, cacheKey, isSorting) {
                const self = this;

                window.dispatchEvent(new CustomEvent('amClearButtonText', {}));

                if (pushState || isSorting) {
                    self.isLoading = true;
                }

                data.every((item, key) => {
                    if (item.name.indexOf('[cat]') != -1) {
                        if (item.value == self.options.currentCategoryId) {
                            data.splice(key, 1);
                        } else {
                            item.value.split(',').filter(element => {
                                return element != self.options.currentCategoryId
                            }).join(',');
                        }

                        return false;
                    }
                    return true;
                });

                const pricefilter = data.find(item => item.name === 'amshopby[price][]');
                if (pricefilter && pricefilter.value === 'NaN') {
                    data.map((item, index) => {
                        if (item.name === 'amshopby[price][]') {
                            data = data.splice(index, 1);
                        }
                    });
                }

                if (!this.submitByClick) {
                    this.qsa(this.selectors.removeAllItems).forEach(removeItem => {
                        const name = removeItem.getAttribute('data-container'),
                            value = removeItem.getAttribute('data-value');

                        data.map(item => {
                            if (!removeItem.classList.contains('radio-remove-item')) {
                                if (item.name === 'amshopby['+ name +'][]') {
                                    if (item.value.indexOf(value) === -1) {
                                        item.value += ',' + value;
                                    }
                                }
                            }
                        });

                        if (!data.find(item => item.name === 'amshopby['+ name +'][]')) {
                            data.push({
                                name: 'amshopby['+ name +'][]',
                                value: value
                            });
                        }
                    });
                }

                if (!isSorting && !clearUrl) {
                    const dir = 'product_list_dir',
                        sort = 'product_list_order',
                        params = new URL(location.href).searchParams;

                    if (params.get(dir)) {
                        data.push({name: dir, value: params.get(dir)});
                    }

                    if (params.get(sort)) {
                        data.push({name: sort, value: params.get(sort)});
                    }
                }

                data.push({name: 'shopbyAjax', value: 1});
                self.startAjax = true;

                if (!clearUrl) {
                    clearUrl = self.clearUrl;
                }
                clearUrl = clearUrl.replace(/amp;/g, '');
                self.clearUrl = clearUrl;

                return fetch(self.getClearUrlParams(clearUrl, data), {
                        headers: {
                            'Content-type': 'text/plain; charset=UTF-8',
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                        signal: this.signal
                    }
                ).then(response => {
                    return response.json()
                }).then(data => {
                    try {
                        self.startAjax = false;

                        if (data.isDisplayModePage && !self.submitByClick) {
                            const url = self.clearUrl ? self.clearUrl : self.options.clearUrl;
                            window.location = (this.url && this.url.indexOf('shopbyAjax') == -1) ? this.url : url;

                            return;
                        }

                        if (cacheKey) {
                            self.cached[cacheKey] = data;
                        }

                        self.response = data;

                        if (data.newClearUrl
                            && (data.newClearUrl.indexOf('?p=') == -1 && data.newClearUrl.indexOf('&p=') == -1)) {
                            self.options.clearUrl = data.newClearUrl;
                        }

                        if (pushState || self.showButtonClick || isSorting) {
                            window.history.pushState({url: data.url}, '', data.url);
                        }

                        if (self.submitByClick !== 1 || isSorting) {
                            self.reloadHtml(data);
                        }

                        if (self.showButtonClick) {
                            self.showButtonClick = false;
                            self.response = false;
                            self.reloadHtml(data);
                        }

                        if (this.submitByClick) {
                            self.showButtonCounter(self.response.productsCount);
                        }
                        /* update form_key in case cached */
                        hyva.initFormKey();
                    } catch (e) {
                        console.error(e);
                        self.options.clearUrl ? window.location = self.options.clearUrl : location.reload();
                    }

                    self.isLoading = false;
                }).catch((e) => {
                    console.error(e);
                    self.isLoading = false;
                });
            },

            getClearUrlParams(url, data) {
                let params = '';

                url = url.replace('#', '');

                data.map(param => {
                    if (params) {
                        params += '&';
                    }

                    params += param.name + '=' + param.value;
                });

                if (~url.indexOf('?')) {
                    url += '&';
                } else {
                    url += '?';
                }

                return url + params;
            },

            filterDataByProp(data, param, prop) {
                return data.find(obj => {
                    return obj[prop] === param[prop];
                });
            },

            reloadHtml(data) {
                let selectSidebarNavigation = '.sidebar.sidebar-main .block-filter',
                    selectTopNavigation = selectSidebarNavigation + '.amshopby-all-top-filters-append-left',
                    selectMainNavigation = '',
                    $productsWrapper = this.getProductBlock();

                this.options.currentCategoryId = data.currentCategoryId
                    ? data.currentCategoryId
                    : this.options.currentCategoryId;

                if (!!this.qs(selectTopNavigation)) {
                    selectMainNavigation = selectTopNavigation; //if all filters are top
                } else if (!!this.qsa(selectSidebarNavigation).length) {
                    selectMainNavigation = selectSidebarNavigation;
                }

                if (this.qs('.am_shopby_apply_filters')) {
                    this.qs('.am_shopby_apply_filters').remove();
                }

                if (!selectMainNavigation) {
                    if (!!this.qs(this.selectors_top_filters.sidebar)) {
                        let div = document.createElement('div');
                        div.className = 'block-filter';
                        this.qs(this.selectors_top_filters.sidebar).prepend(div);

                        selectMainNavigation = selectSidebarNavigation;
                    } else {
                        selectMainNavigation = '.block-filter';
                    }
                }

                if (this.qs(selectMainNavigation)) {
                    this.replaceWithUpdate(
                        data.navigation,
                        this.qs(selectMainNavigation),
                        'selectMainNavigation'
                    );
                }

                const mainContent  = data.categoryProducts || data.cmsPageData;
                if (mainContent) {
                    this.replaceWithUpdate(mainContent, $productsWrapper, 'mainContent');
                }

                if (data.h1) {
                    let newDiv = document.createElement('div');
                    newDiv.innerHTML = data.h1;

                    this.replaceWithUpdate(
                        newDiv.querySelector(this.selectors.title_head).parentElement.innerHTML,
                        this.qs(this.selectors.title_head),
                        'title_head'
                    );
                }

                this.replaceBlock('.breadcrumbs', 'breadcrumbs', data);
                this.replaceBlock('.switcher-currency', 'currency', data);
                this.replaceBlock('.switcher-language', 'store', data);
                this.replaceBlock('.switcher-store', 'store_switcher', data);

                this.replaceCategoryView(data);

                <?php if ($block->isGoogleTagManager()): ?>
                    window.dispatchEvent(new CustomEvent('googleTag', {}));
                <?php endif ?>

                this.topFiltersCheckOnMobile();

                const swatchesTooltip = this.qsa('.swatch-option-tooltip');
                if (swatchesTooltip.length) {
                    swatchesTooltip.forEach(item => {
                        item.style.display = 'none';
                    });
                }

                this.loading = false;

                this.scrollUpEvent();

                if (data.bottomCmsBlock) {
                    let productList = this.qsa(this.selectors.products_wrapper);
                    productList = productList[productList.length - 1];

                    if (this.qs('.amshopby-filters-bottom-cms')) {
                        this.qs('.amshopby-filters-bottom-cms').remove();
                    }

                    let div = document.createElement('div');
                    div.innerHTML = '<div class="amshopby-filters-bottom-cms"></div>';

                    productList.after(div);

                    this.replaceWithUpdate(
                        data.bottomCmsBlock,
                        this.qs('.amshopby-filters-bottom-cms'),
                        'amshopby-filters-bottom-cms'
                    );
                }

                //top nav already exist into categoryProducts
                if (!data.categoryProducts || data.categoryProducts.indexOf('block-filter-top') === -1) {
                    if (!this.qs(this.selectors.top_navigation)) {
                        const navNode = document.createElement('div'),
                            topNavNode = document.createElement('div'),
                            maincontent = this.qs('.column.main');
                        let childNode;

                        navNode.className = 'catalog-topnav amasty-catalog-topnav';
                        topNavNode.className = 'block-filter-top';
                        navNode.appendChild(topNavNode);

                        if (this.qs('.search.results')) {
                            childNode = this.qs('.search.results');
                        } else {
                            childNode = this.qs('#amasty-shopby-product-list');
                        }

                        if (childNode.parentElement !== maincontent) {
                            return;
                        }

                        maincontent.insertBefore(navNode, childNode);
                    }

                    this.replaceWithUpdate(
                        data.navigationTop,
                        this.qs(this.selectors.top_navigation),
                        'top_navigation'
                    );
                }
            },

            replaceWithUpdate(content, $element, className) {
                if (content && $element) {
                    const parent = $element.parentNode,
                        regex = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                        div = document.createElement('div'),
                        isTopNav = className === 'top_navigation';

                    if (this.qs('.scripts-' + className)) {
                        this.qs('.scripts-' + className).innerHTML = '';
                    } else {
                        let container = document.createElement('div');
                        container.className = 'scripts-' + className;
                        this.qs('body').appendChild(container);
                    }

                    div.innerHTML = content;

                    const scripts = div.getElementsByTagName('script');

                    for (let script of scripts) {
                        if (!script.hasAttribute('type')
                                && script.getAttribute('type') !== 'text/x-magento-init') {

                            let scriptTag = document.createElement('script');
                            scriptTag.innerHTML = script.innerHTML;
                            this.qs('.scripts-' + className).appendChild(scriptTag);
                        }
                    }

                    content = content.replaceAll(regex, '');

                    if (parent.classList.contains('one-column-wrapper')) {
                        content = content.substring(content.indexOf('>') + 1, content.lastIndexOf('<'));
                    }

                    if (isTopNav) {
                        const topFilterElement = this.qs(this.selectors.top_nav);
                        topFilterElement.remove();
                        parent.innerHTML = content;
                    } else {
                        const wrapperToInsert = document.createElement("div");
                        wrapperToInsert.innerHTML = content;
                        const elementToInsert = wrapperToInsert.firstElementChild;
                        $element.replaceWith(elementToInsert);
                    }
                }
            },

            topFiltersCheckOnMobile() {
                setTimeout(() => {
                    if (document.body.clientWidth < 768) {
                        this.moveTopFiltersToSidebar();
                    } else {
                        this.removeTopFiltersFromSidebar();
                    }
                }, 0);
            },

            replaceBlock(blockClass, dataName, data) {
                this.replaceWithUpdate(
                    data[dataName],
                    this.qs(blockClass),
                    dataName
                );
            },

            replaceCategoryView(data) {
                if (data.image || data.description || data.categoryData) {
                    let categoryViewSelector = '.category-view';
                    if (!this.qs(categoryViewSelector)) {
                        const catNode = document.createElement('div'),
                            maincontent = this.qs('#maincontent');
                        let childNode = this.qs('.page-main .columns');

                        catNode.className = 'category-view container';
                        catNode.setAttribute('id', 'category-view-container');

                        catNode.innerHTML = '<div class="category-image"></div>'+
                                            '<div class="category-description"></div>';

                        if (this.qs('.amwidget-children-categories')) {
                            childNode = this.qs('.amwidget-children-categories').parentNode;
                        }

                        maincontent.insertBefore(catNode, childNode);
                    }
                }

                const imageElement = this.qs('.category-image'),
                    descrElement = this.qs('.category-description');

                if (data.image) {
                    this.replaceWithUpdate(
                        data.image,
                        imageElement,
                        'category-image'
                    );
                } else {
                    if (imageElement) {
                        imageElement.innerHTML = '';
                    }
                }

                if (data.description) {
                    this.replaceWithUpdate(
                        data.description,
                        descrElement,
                        'category-description'
                    );
                } else {
                    if (descrElement) {
                        descrElement.innerHTML = '';
                    }
                }

                this.qs('title').innerHTML = data.title;

                if (data.categoryData) {
                    let categoryViewSelector = '.category-view';

                    const nodeT = document.createElement('div');
                    nodeT.innerHTML = data.categoryData;

                    //this.qs(categoryViewSelector).innerHTML = '<div></div>';

                    this.replaceWithUpdate(
                        nodeT.querySelector(categoryViewSelector).innerHTML,
                        this.qs(categoryViewSelector + ' div'),
                        'category-view-data'
                    );
                }
            },

            /**
             * @public
             * @return {Object}
             */
            getProductBlock() {
                let $productsWrappers = this.qsa(this.selectors.products_wrapper),
                    $productsWrapper = $productsWrappers[$productsWrappers.length - 1];

                if ($productsWrapper.closest('.search.results')) {
                    $productsWrapper = $productsWrapper.closest('.search.results');
                }

                return $productsWrapper;
            },

            scrollUpEvent() {
                const productList = this.qs(this.selectors.products_wrapper),
                    topNavBlock = this.qs(this.selectors.top_nav);

                if (this.scrollUp && productList) {
                    const top = this.scrollUp === 1
                        ? (topNavBlock ? topNavBlock.offsetTop : productList.offsetTop)
                        : 0;

                    window.scrollTo({
                        top: top,
                        behavior: 'smooth'
                    })
                }
            },

            moveTopFiltersToSidebar() {
                if (!this.qs(this.selectors_top_filters.sidebarList)) {
                    const blockClass = this.qs(this.selectors_top_filters.layeredFilter)
                            ? this.selectors_top_filters.layeredFilter
                            : this.selectors_top_filters.blockFilter,
                        $element = document.querySelector(this.selectors.topNav + ' ' + blockClass);

                    if ($element) {
                        const $sidebar = this.qs(this.selectors_top_filters.sidebar),
                            filterOptions = $element.querySelectorAll('.filter-option');

                        filterOptions.forEach(filter => {
                            const el = filter.cloneNode(true),
                                input = el.querySelector('[type="radio"], [type="checkbox"]');

                            if (input) {
                                const name = input.getAttribute('name');

                                el.classList.add('from-top');

                                if (!$sidebar.querySelectorAll('[name="'+ name +'"]').length) {
                                    $sidebar.querySelector('#layered-filter-block-content').append(el);
                                }
                            }
                        });
                    }

                    return;
                }
            },

            /**
             * @public
             * @returns {void}
             */
            removeTopFiltersFromSidebar() {
                const $sidebar = this.qs(this.selectors_top_filters.sidebar);

                if ($sidebar) {
                    $sidebar.querySelectorAll('.from-top').forEach(filter => {
                        filter.remove();
                    });
                }

            },

            qs(selector) {
                return document.querySelector(selector);
            },

            qsa(selector) {
                return document.querySelectorAll(selector);
            },

            stopEvents(e) {
                e.stopPropagation();
                e.preventDefault();
            },

            serializeForm(form) {
                const data = new FormData(form)
                let array = [];
                for (let [key, value] of data) {
                    array.push({
                        name: key,
                        value: value
                    });
                }

                return array;
            },

            excludingElement(elements, excluded) {
                let clearing = [],
                    excludedSelector = this.qs(excluded);
                elements.forEach(element => {
                    if (element !== excludedSelector) {
                        clearing.push(element);
                    }
                });

                return clearing;
            },

            /**
             * @public
             * @param {String} text
             * @return {String}
             */
            escapeHtml(text) {
                var map = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#039;'
                };

                return text.replace(/[&<>"']/g, (m) => {
                    return map[m];
                });
            },

            /**
             * @public
             * @param {String} link
             * @param {Boolean} [clearFilter]
             * @return {void}
             */
            apply(link, clearFilter) {
                let linkParam;

                try {
                    <?php if ($block->canShowBlock()): ?>
                        this.response = null;
                    <?php endif ?>

                    this.options.isAjax = <?= $block->canShowBlock() ? 'true' : 'false' ?>;

                    linkParam = clearFilter ? link : null;
                    link = this.element?.closest('.price-ranges') && link.includes('?')
                        ? link + '&price-ranges=1'
                        : link;

                    if (!this.options.collectFilters && this.options.isAjax === true) {
                        this.prepareTriggerAjax(this.element, linkParam, clearFilter);
                    } else {
                        // eslint-disable-next-line no-lonely-if
                        if (this.options.collectFilters === 1) {
                            this.prepareTriggerAjax(this.element, linkParam);
                        } else {
                            window.location = link;
                        }
                    }
                } catch (e) {
                    console.error(e);
                    window.location = link;
                }
            },

            fixDubbleValue(link) {
                let input = link.closest('div').querySelector('input');

                if (this.qsa('*[value="'+input.value+'"][name="'+input.getAttribute('name')+'"]').length > 1) {
                    this.qsa('*[value="'+input.value+'"][name="'+input.getAttribute('name')+'"]').forEach(filter => {
                        if (filter !== input) {
                            if (filter.getAttribute('type') === 'radio') {
                                filter.closest('.am-filter').querySelectorAll('input').forEach(option => {
                                    option.checked = false;
                                });
                            } else {
                                filter.checked = false;
                            }
                        }
                    });
                }

                if (input.getAttribute('type') !== 'radio' && !input.checked && !this.submitByClick) {
                    let value = input.getAttribute('value'),
                        name = input.getAttribute('name').replace('amshopby[','');

                    name = name.replace('][]', '');
                    let removeItem = this.qs('.amshopby-remove-item[data-container="'+ name +'"][data-value="'+ value +'"]'); <?php //phpcs:ignore ?>

                    if (removeItem) {
                        removeItem.remove();
                    }
                }
            },

            setSwatchToProducts(swatchId, attributeID) {
                setTimeout(() => {
                    const productList = document.getElementById('amasty-shopby-product-list');
                    if (productList) {
                        const swatches = productList.querySelectorAll('[name="super_attribute['+ attributeID +']"][value="' + swatchId + '"]'), <?php //phpcs:ignore ?>
                            event = new Event('change');

                        swatches.forEach(swatch => {
                            if (!swatch.checked) {
                                swatch.dispatchEvent(event);
                            }
                        });
                    }
                }, 1500);
            },

            setSwatchMassToProducts(swatchIds, attributeID) {
                const swatches = swatchIds.split(',');
                this.massSwatchSet(swatches, attributeID);

                window.onload = () => {
                    setTimeout(() => {
                        this.massSwatchSet(swatches, attributeID);
                    }, 1000);
                }
            },

            massSwatchSet(swatches, attributeID) {
                swatches.map(swatch => {
                    this.setSwatchToProducts(swatch, attributeID);
                });
            },

            insertAfter(newNode, existingNode) {
                existingNode.parentNode.insertBefore(newNode, existingNode.nextSibling);
            },

            removeInputByName(input) {
                let name = input.getAttribute('name').replace('amshopby[','');
                name = name.replace('][]', '');
                this.qs('.amshopby-remove-item[data-container="'+ name +'"]').remove();
            },

            eventListeners: {
                ['@amshopby-submit-filters.window'](event) {
                    let self = this,
                        data = event.detail.data,
                        clearUrl = self.options.clearUrl,
                        isSorting = event.detail.isSorting,
                        pushState = !self.submitByClick;

                    if (typeof data.clearUrl !== 'undefined') {
                        clearUrl = data.clearUrl;
                        delete data.clearUrl;
                    }

                    if (self.prevCall) {
                        this.controller.abort();
                        this.signal = null;
                        this.controller = new AbortController();
                        this.signal = this.controller.signal;
                    }

                    let dataAndUrl = data.slice(0);

                    dataAndUrl.push(clearUrl ? clearUrl : self.clearUrl);

                    const cacheKey = JSON.stringify(dataAndUrl);
                    self.cacheKey = cacheKey;

                    if (self.cached[cacheKey]) {
                        let response = self.cached[cacheKey];
                        if (pushState || isSorting) {
                            if (response.newClearUrl
                                && response.newClearUrl.indexOf('?p=') == -1
                                && response.newClearUrl.indexOf('&p=') == -1
                            ) {
                                self.options.clearUrl = response.newClearUrl;
                            }

                            window.history.pushState({url: response.url}, '', response.url);
                            self.reloadHtml(response);
                        } else {
                            window.dispatchEvent(new CustomEvent('amApplyButtonData', { detail: {
                                count: response.productsCount,
                                disabled: false
                            } }));
                        }

                        return;
                    }

                    self.prevCall = self.callAjax(clearUrl, data, pushState, cacheKey, isSorting);
                },
                ['@amSliderValuesUpdated.window'](event) {
                    this.apply(event.detail.searchParams);
                },
                ['@amApplyButton.window'](event) {
                    let valid = true,
                        element = event.detail.element,
                        navigationSelector = event.detail.navigationSelector,
                        navigation = element.closest(navigationSelector),
                        cachedValues = this.cached[this.cacheKey],
                        cachedKey = this.response,
                        response = cachedValues ? cachedValues : cachedKey;

                    if (!response) {
                        if (this.startAjax) {
                            this.showButtonClick = true;
                            this.isLoading = true;

                            window.dispatchEvent(new CustomEvent('amApplyButtonData', { detail: {
                                    count: '',
                                    disabled: true
                                } }));
                            return;
                        } else {
                            return;
                        }
                    }

                    if (response.isDisplayModePage ||
                            (!this.isAjaxSettingEnabled && this.submitByClick)) {
                        window.location.href = response.url;

                        return;
                    }

                    if (valid && response) {
                        window.dispatchEvent(new CustomEvent('amApplyButtonData', { detail: {
                            count: '',
                            disabled: false
                        } }));

                        window.history.pushState({url: response.url}, '', response.url);

                        this.reloadHtml(response);

                        this.response = false;
                        this.showButtonClick = false;
                    }

                    window.onpopstate = function () {
                        location.reload();
                    };
                },
                ['@amRemoveElement.window'](event) {
                    const link = event.detail.element;

                    if (this.isAjax) {
                        const currentFilterItem = link.closest('.amshopby-remove-item'),
                            filter = {
                                attribute: currentFilterItem.getAttribute('data-container'),
                                value: this.escapeHtml(currentFilterItem.getAttribute('data-value'))
                            };

                        this.currentFilters.push(filter);
                        this.element = undefined;

                        try {
                            window.dispatchEvent(
                                new CustomEvent(
                                    'amSetButtonPosition',
                                    {
                                        detail: {
                                            element: link
                                        }
                                    }
                                )
                            );

                            this.setDefault(filter.attribute, filter.value);
                            if (!this.submitByClick) {
                                link.closest('.amshopby-remove-item').remove();
                            }
                            this.prepareTriggerAjax(null, null, true);
                        } catch (e) {
                            console.error(e)
                            window.location = link.getAttribute('href');
                        }
                    } else {
                        window.location.href = link.getAttribute('href');
                    }
                },
                ['@amSwatchClick.window'](event) {
                    const link = event.detail.element;

                    if (this.isAjax) {
                        const href = link.getAttribute('href'),
                            input = link.closest('div').querySelector('input');
                        this.element = link;
                        input.checked = !input.checked;

                        if (input.getAttribute('type') === 'radio') {
                            let ring = link.closest('.am-shopby-form').querySelector('.ring');

                            if (ring) {
                                this.setSwatchBorder(ring);

                                if (!this.submitByClick) {
                                    this.removeInputByName(input);
                                }

                                if (input.checked) {
                                    this.setSwatchBorder(link);
                                }
                            } else {
                                this.setSwatchBorder(link);
                            }
                        } else {
                            this.setSwatchBorder(link);
                        }

                        if (this.isFinderAndCategory(link)) {
                            location.href = href;

                            return;
                        }

                        setTimeout(() => {
                            this.fixDubbleValue(link);

                            window.dispatchEvent(
                                new CustomEvent(
                                    'amSetButtonPosition',
                                    {
                                        detail: {
                                            element: link
                                        }
                                    }
                                )
                            );
                            this.apply(href);
                        }, 10);
                    } else {
                        window.location.href = link.getAttribute('href');
                    }
                },
                ['@amFilterElementClick.window'](event) {
                    let filterElement = event.detail.element;

                    if (!filterElement.classList.contains('item')) {
                        filterElement = filterElement.closest('div');
                    }

                    const checkbox = filterElement.querySelector('input'),
                        checkboxLink = filterElement.querySelector('a'),
                        href = checkboxLink.getAttribute('href');

                    if (!this.submitByClick &&
                            checkbox.checked &&
                            checkbox.getAttribute('type') === 'radio') {
                        this.removeInputByName(checkbox);
                    }

                    this.element = checkboxLink;

                    setTimeout(() => {
                        this.fixDubbleValue(checkboxLink);

                        window.dispatchEvent(
                            new CustomEvent(
                                'amSetButtonPosition',
                                {
                                    detail: {
                                        element: checkboxLink
                                    }
                                }
                            )
                        );

                        if (this.isFinderAndCategory(checkbox)) {
                            location.href = href;

                            return;
                        }

                        this.apply(href);
                    }, 10);
                },
                ['@googleTag.window'](e) {
                    <?php if ($block->isGoogleTagManager()): ?>
                        var tag = 'script',
                            layer = 'dataLayer',
                            containerId = "<?= $escaper->escapeHtml($block->getGtmAccountId()) ?>";

                        if (containerId) {
                            window[layer] = window[layer] || [];
                            window[layer].push({
                                'gtm.start': new Date().getTime(), event: 'gtm.js'
                            });
                            var f = document.getElementsByTagName(tag)[0],
                                j = document.createElement(tag), dl = layer != 'dataLayer' ? '&l=' + layer : '';
                            j.async = true;
                            j.src =
                                'https://www.googletagmanager.com/gtm.js?id=' + containerId + dl;
                            f.parentNode.insertBefore(j, f);
                            f.parentNode.removeChild(f.parentNode.querySelectorAll('script[src="' + j.src + '"]')[0]);
                        }
                    <?php endif ?>
                }
            }
        }
    }
</script>

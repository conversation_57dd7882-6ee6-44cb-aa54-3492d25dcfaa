<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Welcome to %store_name" store_name=$store.frontend_name}} @-->
<!--@vars {
"var store.frontend_name":"Store Name",
"var this.getUrl($store,'customer/account/',[_nosid:1])":"Customer Account URL",
"var this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])":"Password Reset URL",
"var customer.email":"Customer Email",
"var customer.name":"Customer Name"
} @-->

{{template config_path="design/email/header_template"}}

<h1>{{trans "Welcome to %store_name, " store_name=$store.frontend_name}}{{trans "%name" name=$customer.name}}</h1>
<p class="mt-20">
    {{trans
        'To sign in to our site, use these credentials during checkout or on the <a href="%customer_url">My Account</a> page:'

        customer_url=$this.getUrl($store,'customer/account/',[_nosid:1])
    |raw}}
</p>
<ul style="list-style: none; padding: 0; margin-top: 30px;">
    <li><strong>{{trans "Email:"}}</strong> {{var customer.email}}</li>
    <li><strong>{{trans "Password:"}}</strong> <em>{{trans "Password you set when creating account"}}</em></li>
</ul>
<p class="mt-30">
    {{trans
        'Forgot your account password?<br>
        <a href="%reset_url">Click here</a> to reset it.'

        reset_url="$this.getUrl($store,'customer/account/createPassword/',[_query:[id:$customer.id,token:$customer.rp_token],_nosid:1])"
    |raw}}
</p>
<p class="text-center mt-30"><strong>{{trans "When you sign in to your account, you will be able to:"}}</strong></p>

<table class="email-features">
    <tr>
        <td>
            <img src="{{view url='Magento_Customer/images/check-mark.png'}}" height="16" width="16" alt="{{trans 'Checkout faster'}}" />
            <p>{{trans "Checkout faster"}}</p>
        </td>
        <td>
            <img src="{{view url='Magento_Customer/images/check-mark.png'}}" height="16" width="16" alt="{{trans 'Check the order status'}}" />
            <p>{{trans "Check the order status"}}</p>
        </td>
    </tr>
    <tr>
        <td>
            <img src="{{view url='Magento_Customer/images/check-mark.png'}}" height="16" width="16" alt="{{trans 'Manage Addresses'}}" />
            <p>{{trans "Store alternative addresses"}}</p>
        </td>
        <td>
            <img src="{{view url='Magento_Customer/images/check-mark.png'}}" height="16" width="16" alt="{{trans 'View past orders'}}" />
            <p>{{trans "View past orders"}}</p>
        </td>
    </tr>
</table>
<!-- <ul>
    <li>{{trans "Proceed through checkout faster"}}</li>
    <li>{{trans "Check the status of orders"}}</li>
    <li>{{trans "View past orders"}}</li>
    <li>{{trans "Store alternative addresses (for shipping to multiple family members and friends)"}}</li>
</ul> -->

{{template config_path="design/email/footer_template"}}

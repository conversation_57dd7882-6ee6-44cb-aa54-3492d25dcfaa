<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Customer\Block\Widget\Telephone;
use Magento\Framework\Escaper;

/** @var Escaper $escaper */
/** @var Telephone $block */

$fieldIdFormat = ($block->getFieldIdFormat() === '%s') ? NULL : $block->getFieldIdFormat();
$fieldNameFormat = ($block->getFieldNameFormat() === '%s') ? NULL : $block->getFieldNameFormat();
?>

<div class="field field-reserved telephone <?= $block->isRequired() ? 'required' : '' ?>">
    <label for="telephone" class="label text-cgrey-90">
        <span>
            <?= $escaper->escapeHtml(__('Phone Number')) ?>
        </span>
    </label>
    <div class="control">
        <input type="tel"
               name="<?= $escaper->escapeHtmlAttr($fieldNameFormat ?? 'telephone') ?>"
               id="<?= $escaper->escapeHtmlAttr($fieldIdFormat ?? 'telephone') ?>"
               <?php if ($block->isRequired()): ?>required<?php endif; ?>
               @input.debounce="onChange"
               data-validate='{"<?= $escaper->escapeHtmlAttr($fieldNameFormat ?? 'telephone') ?>": true}'
               value="<?= $escaper->escapeHtmlAttr($block->getTelephone()) ?>"
               title="<?= $escaper->escapeHtmlAttr(__('Phone Number')) ?>"
               class="form-input w-full <?= $escaper->escapeHtmlAttr(
                   $block->getAttributeValidationClass('telephone')
               ) ?>"
        >
    </div>
</div>

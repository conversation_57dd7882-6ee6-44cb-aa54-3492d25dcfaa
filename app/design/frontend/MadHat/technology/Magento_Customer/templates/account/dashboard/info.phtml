<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Customer\Block\Account\Dashboard\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<h2 class="mb-6 text-xl block-title text-cgrey-90 font-semibold">
    <?= $escaper->escapeHtml(__('Account Information')) ?>
</h2>
<div class="flex flex-wrap justify-between -m-4">
    <div class="w-full p-4 lg:w-1/2">
        <div class="flex flex-col h-full p-8 sm:flex-row card">
            <div
                class="inline-flex items-center justify-center shrink-0 w-16 h-16 mb-4
                    rounded-full sm:mr-8 sm:mb-0 bg-container-darker text-secondary">
                <?= $heroicons->userHtml('', 32, 32, ['aria-hidden' => 'true']) ?>
            </div>
            <div class="grow">
                <h3 class="mb-3 text-xl font-bold text-gray-900 title-font">
                    <span><?= $escaper->escapeHtml(__('Contact Information')) ?></span>
                </h3>
                <p>
                    <?= $escaper->escapeHtml($block->getName()) ?><br>
                    <?= ''//$escaper->escapeHtml($block->getCustomer()->getCompany()) ?><br>
                    <?= $escaper->escapeHtml($block->getCustomer()->getEmail()) ?><br>
                </p>
                <?= $block->getChildHtml('customer.account.dashboard.info.extra'); ?>
                <a
                    class="inline-flex items-center w-full mt-3 mb-2 underline text-cgrey-90"
                    href="<?= $escaper->escapeUrl($block->getUrl('customer/account/edit')) ?>"
                    aria-label="<?= $escaper->escapeHtml(__('Edit contact information')) ?>"
                >
                    <span><?= $escaper->escapeHtml(__('Edit')) ?></span>
                    <!-- <?= $heroicons->arrowRightHtml('ml-2', 16, 16, ['aria-hidden' => 'true']); ?> -->
                </a>
                <a
                    class="inline-flex items-center w-full underline text-cgrey-90"
                    href="<?= $escaper->escapeUrl($block->getChangePasswordUrl()) ?>" class="action change-password"
                >
                    <?= $escaper->escapeHtml(__('Change Password')) ?>
                    <!-- <?= $heroicons->arrowRightHtml('ml-2', 16, 16, ['aria-hidden' => 'true']); ?> -->
                </a>
            </div>
        </div>
    </div>
    <?php if ($block->isNewsletterEnabled()): ?>
        <div class="w-full p-4 lg:w-1/2">
            <div class="flex flex-col h-full p-8 border border-gray-200 sm:flex-row card">
                <div
                    class="inline-flex items-center justify-center shrink-0 w-16 h-16 mb-4
                        rounded-full sm:mr-8 sm:mb-0 bg-container-darker text-secondary">
                    <?= $heroicons->mailHtml('', 32, 32, ['aria-hidden' => 'true']) ?>
                </div>
                <div class="grow">
                    <h3 class="mb-3 text-xl font-bold text-gray-900 title-font">
                        <?= $escaper->escapeHtml(__('Newsletters')) ?>
                    </h3>
                    <p>
                        <?php if ($block->getIsSubscribed()): ?>
                            <?= $escaper->escapeHtml(__('You are subscribed to "General Subscription".')) ?>
                        <?php else: ?>
                            <?= $escaper->escapeHtml(__('You aren\'t subscribed to our newsletter.')) ?>
                        <?php endif; ?>
                    </p>
                    <a
                        class="mt-3 w-full inline-flex items-center underline text-cgrey-90"
                        href="<?= $escaper->escapeUrl($block->getUrl('newsletter/manage')) ?>"
                        aria-label="<?= $escaper->escapeHtml(__('Edit newsletters')) ?>"
                    >
                        <span><?= $escaper->escapeHtml(__('Edit')) ?></span>
                        <!-- <?= $heroicons->arrowRightHtml('ml-2', 16, 16, ['aria-hidden' => 'true']); ?> -->
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?= $block->getChildHtml('additional_blocks'); ?>
</div>

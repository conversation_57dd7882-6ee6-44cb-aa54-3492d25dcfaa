<?php
/**
 * Copyright © MageWorx. All rights reserved.
 * See https://www.mageworx.com/terms-and-conditions for license details.
 */

/** @var \MageWorx\ReviewReminderBase\Block\Reminder\Popup $block */
/** @var \Hyva\Theme\Model\ViewModelRegistry $viewModels */
/** @var \Hyva\Theme\ViewModel\Modal $modelViewModel */
$modelViewModel = $viewModels->require(\Hyva\Theme\ViewModel\Modal::class);

/** @var \Hyva\Theme\ViewModel\HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(\Hyva\Theme\ViewModel\HeroiconsOutline::class);
?>
<?php if ($block->isCanBeDisplayed()): ?>
    <script>
        'use strict';

        function initMwReminder() {
            const jsonConfig = <?= /* @noEscape */ $block->getJsonConfig() ?>;
            const ajaxUrl = jsonConfig.ajaxUrl;

            return {
                init() {
                    if (!hyva.getCookie('mageworx_review_reminder_popup')) {
                        this.updateContent();
                    }
                },
                updateContent() {
                    const data = {
                        email: hyva.getCookie('mageworx_review_reminder_previous_email')
                    };

                    if (!data.email) {
                        return;
                    }

                    fetch(ajaxUrl, {
                        method: 'POST',
                        global: false,
                        body: JSON.stringify(data),
                        headers: {
                            "X-Requested-With": "XMLHttpRequest",
                        }
                    })
                        .then(response => {
                            if (! response.ok) console.warn('request failed');
                            return response.json();
                        })
                        .then(data => {
                            if (data.success) {
                                const content = data.content;

                                const scripts = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi
                                let formattedContent = content.replace(scripts, "");
                                formattedContent = formattedContent.replace('role="dialog"', "");

                                const link = document.createElement('link')
                                link.rel = 'stylesheet';
                                link.type = 'text/css';
                                link.href = '<?= $escaper->escapeUrl($block->getViewFileUrl('Hyva_MageWorxXReviewBase::css/popup.css')) ?>';
                                document.head.append(link);

                                this.$refs['mw-reminder-popup'].innerHTML = formattedContent;
                                this.show('mw-reminder-popup');

                                const $closeButton = document.querySelector("button.mwrv-modal__close");
                                $closeButton?.addEventListener('click', () => this.hide());

                                hyva.setCookie('mageworx_review_reminder_popup', Math.floor(Date.now() / 1000));
                            }
                        })
                        .catch(error => {
                            console.warn('reminder error');
                        })
                },
            }
        }
    </script>

    <div x-data="{...initMwReminder(), ...hyva.modal()}" x-cloak>
        <!--Modal window-->
        <div x-cloak x-spread="overlay('mw-reminder-popup')" x-bind="overlay('mw-reminder-popup')" class="fixed inset-0 z-30 flex items-center justify-center text-left bg-black bg-opacity-50">
            <div role="dialog" aria-labelledby="the-label"
                 class="w-full md:w-5/6 rounded-none md:rounded-lg bg-white max-w-3xl">
                <div x-ref="mw-reminder-popup"></div>
            </div>
        </div>
        <!--Modal window-->
    </div>
<?php endif; ?>

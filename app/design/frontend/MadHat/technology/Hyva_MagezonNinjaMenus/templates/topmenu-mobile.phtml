<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * @var TopMenu $block
 */
$html = $block->getMenuHtml();

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;
use Magezon\NinjaMenus\Block\TopMenu;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Navigation $viewModelNavigation */
/** @var HeroiconsOutline $heroicons */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$viewModelNavigation = $viewModels->require(Navigation::class, $block);
$uniqueId = '_' . uniqid();
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

/** @var StoreSwitcher $storeSwitcherViewModel */
$storeSwitcherViewModel = $viewModels->require(\Hyva\Theme\ViewModel\StoreSwitcher::class);

/** @var SwitcherUrlProvider $switcherUrlProvider */
$switcherUrlProvider = $viewModels->require(\Magento\Store\ViewModel\SwitcherUrlProvider::class);

$currentStore = $storeSwitcherViewModel->getStore();
?>
<nav
    x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
    @load.window="setActiveMenu($root)"
    @keydown.window.escape="closeMenu()"
    class="z-20 navigation lg:hidden h-[34px] pr-1"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation')) ?>"
    role="navigation"
>
    <!-- mobile -->
    <button
        x-ref="mobileMenuTrigger"
        @click="openMenu()"
        :class="{'-ml-2 overflow-x-hidden overflow-y-auto fixed top-0 -left-2 w-full' : open}"
        type="button"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Open menu')) ?>"
        aria-haspopup="menu"
        :aria-expanded="open"
        :hidden="open"
    >
        <?= $heroicons->menuHtml('', 36, 36, [":class" => "{ 'hidden' : open, 'block': !open }", "aria-hidden" => "true"]) ?>
    </button>
    <div
        x-ref="mobileMenuNavLinks"
        class="
            z-50 fixed top-0 right-0 w-full h-full flex
            flex-col border-t border-container bg-container-lighter
            overflow-y-auto overflow-x-hidden hidden
        "
        :class="{ 'hidden' : !open }"
        :aria-hidden="open ? 'false' : 'true'"
        role="dialog"
        aria-modal="true"
    >
        <div class="sections nav-sections border-t flex flex-col gap-y-1 mt-16"
            x-data="{
                selectedStore: '<?= $escaper->escapeHtml($currentStore->getId()) ?>',
                switchStore(storeId) {
                    if (storeId !== '<?= $escaper->escapeHtml($currentStore->getId()) ?>') {
                        const url = this.$refs[`store_${storeId}`].getAttribute('data-url');
                        window.location.href = url;
                    }
                }
            }"
        >
            <nav class="navigation" data-action="navigation">
                <?= $html ?>
                <div class="choose-customer flex text-sm font-semibold py-4 px-3 gap-5">
                    <?php foreach ($storeSwitcherViewModel->getGroups() as $group): ?>
                        <?php foreach ($group->getStores() as $store): ?>
                            <label class="flex items-center">
                                <input type="radio" name="store-type"
                                    value="<?= $escaper->escapeHtml($store->getId()) ?>"
                                    :checked="selectedStore === '<?= $escaper->escapeHtml($store->getId()) ?>'"
                                    @change="switchStore('<?= $escaper->escapeHtml($store->getId()) ?>')"
                                    class="form-radio text-cblue h-4 w-4"
                                    x-ref="store_<?= $escaper->escapeHtml($store->getId()) ?>"
                                    data-url="<?= $escaper->escapeUrl($switcherUrlProvider->getTargetStoreRedirectUrl($store)) ?>"
                                >
                                <span class="ml-2"><?= $escaper->escapeHtml($group->getName()) ?></span>
                            </label>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </div>
            </nav>
        </div>
        <button
            @click="closeMenu()"
            class="fixed top-0 right-0 flex justify-end w-16 self-end mb-1 w-full border-b z-[9999] bg-white"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
            type="button"
        >
            <?= $heroicons->xHtml('hidden p-4', 64, 64, [":class" => "{ 'hidden' : !open, 'block': open }", "aria-hidden" => "true"]) ?>
        </button>
    </div>
</nav>
<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            mobilePanelActiveId: null,
            open: false,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('underline');
                    item.closest('li.level-0') &&
                    item.closest('li.level-0').querySelector('a.level-0').classList.add('underline');
                });
            },
            openMenu() {
                this.open = true
                this.$nextTick(() => hyva.trapFocus(this.$refs['mobileMenuNavLinks']));
                // Prevent from body scrolling while mobile menu opened
                document.body.style.position = 'fixed';
            },
            closeMenu() {
                document.body.style.position = '';

                if (this.open) {
                    this.$nextTick(() => this.$refs['mobileMenuTrigger'].focus() || hyva.releaseFocus());
                }

                this.open = false
                this.mobilePanelActiveId = null
            },
            openSubcategory(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']')
                this.mobilePanelActiveId = this.mobilePanelActiveId === index ? 0 : index
                this.$nextTick(() => hyva.trapFocus(menuNodeRef))
            },
            backToMainCategories(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']')
                this.mobilePanelActiveId = 0
                this.$nextTick(() => {
                    hyva.trapFocus(this.$refs['mobileMenuNavLinks'])
                    menuNodeRef.querySelector('a').focus()
                })
            }
        }
    }
</script>

<script>
    loadScript('<?= $block->getViewFileUrl('Hyva_MagezonBuilder::js/jquery.min.js') ?>', 'jquery')
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Hyva_MagezonNinjaMenus::js/menu-combine.js') ?>', 'menu');
        })
        .then(() => {
            setTimeout(function() {
                //$("#div1").hide();
                const menuElement = $(".ninjamenus");
                const options = {
                    "mobileBreakpoint": 1024,
                    "stick": true,
                };
                let ninjaMenus = new NinjaMenus(menuElement, options);
            }, 500);
        })
</script>

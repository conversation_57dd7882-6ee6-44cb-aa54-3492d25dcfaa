<?php
/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category  BSS
 * @package   Hyva_BssSimpledetailconfigurable
 * <AUTHOR> Team
 * @copyright Copyright (c) 2023 BSS Commerce Co. ( http://bsscommerce.com )
 * @license   http://bsscommerce.com/Bss-Commerce-License.txt
 */

use Bss\Simpledetailconfigurable\Block\ConfigurableControl as Template;
use Magento\Framework\Escaper;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\BssSimpledetailconfigurable\ViewModel\Helper as ModuleViewModel;
use Hyva\Theme\ViewModel\ProductPage;

/** @var $escaper Escaper */
/** @var $block Template */
/** @var ViewModelRegistry $viewModels */

$moduleViewModel = $viewModels->require(ModuleViewModel::class);
$moduleConfig = $moduleViewModel->getModuleConfig();
$uniqueId = uniqid();

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

if ($preSelectCustom = $block->getRequest()->getParam('pre_select_custom')) {
    $preselectChildId = $preSelectCustom;
} else {
    $preselectChildId = $productViewModel->getProduct()->getData('preselect_data');
}

$jsonChildProductData = $block->getJsonChildProductData();

$childProductData = json_decode($jsonChildProductData, true);

?>
<script>
    'use strict';

    function initSimpleDetailData<?= $escaper->escapeJs($uniqueId) ?> () {
        return {
            pageLoad: false,
            jsonChildProduct: <?= /* @noEscape */ $jsonChildProductData; ?>,
            jsonModuleConfig: <?= /* @noEscape */ $block->getJsonModuleConfig(); ?>,
            ajaxOptionUrl: "<?= /* @noEscape */ $block->getUrl('bss_sdcp/ajax/option'); ?>",
            baseUrl: "<?= /* @noEscape */ $block->getBaseUrl(); ?>",
            originalLogic: false,
            configChildOption: "<?= /* @noEscape */ $moduleConfig->isEnableChildOption() ?>",
            jsonConfig: <?= /* @noEscape */ $block->getJsonConfig(); ?>,
            starFragment: 0,
            yellowHex: '#f6e05e',
            greyHex:'#cbd5e0',
            eventListeners: {
                ['@configurable-selection-changed.window'] ($event) {
                    try {
                        var self = this;
                        if (this.configChildOption) {
                            fetch(this.ajaxOptionUrl, {
                                headers: {
                                    contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                                },
                                body: new URLSearchParams({
                                    form_key: hyva.getFormKey(),
                                    product_id: $event.detail.productIndex
                                }),
                                method: "POST",
                                mode: "cors",
                                credentials: "include",
                            }).then(response => {
                                    return response.json()
                                }
                            ).then(data => {
                                let optionProduct = document.getElementById('bss-custom-option');
                                optionProduct.textContent = "";
                                if (data) {
                                    this.appendDataToElement(optionProduct,data);
                                    var formElement = document.getElementById('product_addtocart_form');
                                    formElement.setAttribute("enctype", "multipart/form-data");
                                }
                            });
                        }

                        if (!this.originalLogic) {
                            let productIndex = $event.detail.productIndex;
                            let selectedValues = $event.detail.selectedValues;
                            let jsonProductData = this.jsonConfig.index[productIndex];
                            var product = this.jsonChildProduct.child[productIndex];

                            if (!product) {
                                productIndex = this.findMatchingIndex(this.jsonConfig.index, selectedValues);
                                product = this.jsonChildProduct.child[productIndex];
                            }

                            var event_product_data = {
                                stock_status: product?.stock_status || false,
                                inventory_status: product?.inventory_status || '',
                                inventory_status_flag: product?.inventory_status_flag || 0,
                                availability: productIndex || false
                            };

                            if (productIndex == undefined) {
                                var product_availability = {
                                    availability: productIndex || false,
                                    stock_status: false
                                };
                                window.dispatchEvent(new CustomEvent('check-product-availability', {detail: {product_availability}}));
                            }

                            for (const option in selectedValues) {
                                if (selectedValues.hasOwnProperty(option) && jsonProductData?.hasOwnProperty(option) && selectedValues[option] !== jsonProductData[option]) {
                                    event_product_data = {
                                        stock_status: false,
                                        inventory_status: '',
                                        inventory_status_flag: 0
                                    };
                                    break;
                                }
                            }
                        }

                        if (this.jsonModuleConfig.additional_info != 0) {
                            let reviewListProduct = document.getElementById('customer-review-list');
                            var customerReview = document.querySelector('.bss-customer-review');
                            var yellowHex = '#f6e05e';
                            var greyHex = '#cbd5e0';
                            if (product && product.review_count > 0){
                                fetch(BASE_URL + '/bss_sdcp/ajax_product/reviewStars/id/' + product.entity, {
                                    headers: {
                                        contentType: "application/x-www-form-urlencoded; charset=UTF-8",
                                    },
                                    method: "GET",
                                    mode: "cors",
                                    credentials: "include",
                                }).then(response => {
                                        return response.json()
                                    }
                                ).then(data => {
                                    reviewListProduct.classList.remove('hidden');
                                    reviewListProduct.textContent = "";
                                    if (data.reviewsHtml) {
                                        this.appendDataToElement(reviewListProduct, data.reviewsHtml);

                                        var ratingValues = reviewListProduct.querySelectorAll('[itemprop="ratingValue"]');
                                        var ratingCount = ratingValues.length;
                                        var ratingValue = 0;
                                        ratingValues.forEach(function (rating) {
                                            ratingValue += parseInt(rating.innerHTML);
                                        });
                                        var averageRating = ratingValue/ratingCount;
                                        var starElements = document.querySelectorAll('.bss-customer-review svg');
                                        if (starElements.length > averageRating && (averageRating - Math.floor(averageRating) > 0)) {
                                            this.starFragment = averageRating - Math.floor(averageRating);
                                        }
                                        var starSummary = this.$el.querySelector('.bss-summary-star');
                                        var starCrescent = this.$el.querySelector('.bss-crescent-star');
                                        var cloneStarCrescent= starCrescent.cloneNode(true);
                                        var stopElements = cloneStarCrescent.querySelectorAll('stop');
                                        stopElements.forEach(function (element) {
                                            element.removeAttribute(":stop-color");
                                            element.removeAttribute(":offset");
                                        });

                                        starElements.forEach(function (star, index) {
                                            if (index <  Math.floor(averageRating)) {
                                                var clone = starSummary.cloneNode(true);
                                                clone.classList.remove('hidden');
                                                clone.style.color = yellowHex;
                                                star.parentNode.replaceChild(clone, star);
                                            } else {
                                                if (averageRating > index) {
                                                    cloneStarCrescent.classList.remove('hidden');
                                                    star.parentNode.replaceChild(cloneStarCrescent, star);
                                                } else {
                                                    var clone = starSummary.cloneNode(true);
                                                    clone.classList.remove('hidden');
                                                    clone.style.color = greyHex;
                                                    if (star.parentNode) {
                                                        star.parentNode.replaceChild(clone, star);
                                                    }
                                                }
                                            }
                                        });
                                    }

                                });
                            } else {
                                if (reviewListProduct) {
                                    reviewListProduct.textContent = "";
                                    reviewListProduct.classList.add('hidden');
                                }

                                var starSummary = this.$el.querySelector('.bss-summary-star');

                                var starElements = customerReview.querySelectorAll('svg');
                                starElements.forEach(function (star) {
                                    var clone = starSummary.cloneNode(true);
                                    clone.classList.remove('hidden');
                                    clone.style.color = greyHex;
                                    star.parentNode.replaceChild(clone, star);
                                });
                            }
                        }

                        if (product) {
                            // Replace custom url
                            <?php if ($block->getRequest()->getFullActionName() !== 'checkout_cart_configure') :?>
                            if (product.custom_url) {
                                if (this.originalLogic) {
                                    let $customUrl = product.custom_url;
                                    $customUrl=$customUrl.replace(/[`!@#$%^&*()|\;:'".<>\{\}\[\]\\\/]/g,'')
                                    $customUrl = $customUrl.trim($customUrl);
                                    while ($customUrl.indexOf(' ') >= 0) {
                                        $customUrl = $customUrl.replace(" ", "~");
                                    }
                                    let $parentUrl = this.jsonChildProduct.url,
                                        $suffix = this.jsonModuleConfig.url_suffix,
                                        suffixPos = $parentUrl.indexOf($suffix);

                                    if (suffixPos > 0) {
                                        $parentUrl = $parentUrl.substring(0, suffixPos);
                                    }

                                    window.history.replaceState('SDCP', 'SCDP', $parentUrl + $customUrl);
                                } else {
                                    window.history.replaceState('SDCP', 'SCDP', this.baseUrl + product.custom_url);
                                }
                            }
                            <?php endif ?>
                            this.setActiveSimpleProduct(product, event_product_data);
                        }
                    } catch (e) {
                        console.log(e)
                    }
                },
                ['@fix-configurable-option-selection.window']($event) {
                    let showSwatches = $event.detail.showSwatches;
                    this.fixSelectionOptionNotSelected(showSwatches);
                }
            },
            findMatchingIndex(indexes, selectedValues) {
                return Object.keys(indexes).find(key =>
                    Object.entries(selectedValues).every(([k, v]) => indexes[key][k] === v)
                ) || null;
            },
            updateMetaData (product) {
                let metaDescription = document.querySelector('head meta[name="description"]');
                if (product?.meta_data?.meta_description) {
                    metaDescription && metaDescription.setAttribute("content", product?.meta_data?.meta_description);
                } else {
                    metaDescription && metaDescription.setAttribute("content", this.jsonChildProduct?.meta_data?.meta_description);
                }
                let metaTitle = document.querySelector('head meta[name="title"]');
                if (product?.meta_data?.meta_title) {
                    metaTitle && metaTitle.setAttribute("content", product?.meta_data?.meta_title);
                    document.title = metaTitle.content;
                } else {
                    metaTitle && metaTitle.setAttribute("content", this.jsonChildProduct?.meta_data?.meta_title);
                }
                let metaKeywords = document.querySelector('head meta[name="keywords"]');
                if (product?.meta_data?.meta_keyword) {
                    metaKeywords && metaKeywords.setAttribute("content", product?.meta_data?.meta_keyword);
                } else {
                    metaKeywords && metaKeywords.setAttribute("content", this.jsonChildProduct?.meta_data?.meta_keyword);
                }
            },
            setActiveSimpleProduct (product, event_product_data) {
                if (!this.pageLoad) {
                    if (this.jsonModuleConfig?.meta_data === '1') {
                        this.updateMetaData(product);
                    }
                    window.dispatchEvent(new CustomEvent('simple-detail-product-active', {detail: {product, event_product_data}}));
                    window.dispatchEvent(new CustomEvent('update-gallery-simple-detail',{
                        detail: {
                            images: product.image,
                            parentImage :this.jsonChildProduct.image
                        }
                    }));
                } else {
                    this.pageLoad = false;
                }
            },
            prepareMetaData () {
                let metaDescription = document.querySelector('head meta[name="description"]');
                if (metaDescription && !this.jsonChildProduct?.meta_data?.meta_description) {
                    this.jsonChildProduct.meta_data.meta_description = metaDescription.getAttribute("content");
                }
                let metaTitle = document.querySelector('head meta[name="title"]');
                if (metaTitle && !this.jsonChildProduct?.meta_data?.meta_title) {
                    this.jsonChildProduct.meta_data.meta_title = metaTitle.getAttribute("content");
                }
                let metaKeywords = document.querySelector('head meta[name="keywords"]');
                if (metaKeywords && !this.jsonChildProduct?.meta_data?.meta_keyword) {
                    this.jsonChildProduct.meta_data.meta_keyword = metaKeywords.getAttribute("content");
                }
            },
            onPageload () {
                this.pageLoad = true;
                var url = location.href;
                if (url.split('+').pop() === 'sdcp-redirect') {
                    var paramRedirect = url.split('+').slice(0, -1);
                } else {
                    var paramRedirect = url.split('+');
                }

                paramRedirect.shift();
                var customChildUrl = "";
                for (const key in paramRedirect) {
                    customChildUrl += '+' + paramRedirect[key];
                }

                for (const key in this.jsonChildProduct.child) {
                    if (this.jsonChildProduct.child[key].custom_url == customChildUrl) {
                        var childProductId = this.jsonChildProduct.child[key].entity;
                    }
                }
                if (childProductId) {
                    var selectData = this.jsonConfig.index[childProductId];
                }
                this.setActiveSimpleProduct(this.jsonChildProduct);
                this.selectChildProduct(selectData);
                if (this.jsonModuleConfig?.meta_data === '1') {
                    this.prepareMetaData();
                }

                <?php if ($preselectChildId) : ?>
                var preselectData = <?= /* @noEscape */ $moduleViewModel->serialize($preselectChildId) ?>;
                if (this.jsonModuleConfig?.preselect == '1' && !selectData) {
                    this.selectChildProduct(preselectData)
                }
                <?php endif; ?>
            },

            fixSelectionOptionNotSelected(showSwatches) {
                <?php if ($preselectChildId) : ?>
                let selectData = false;
                var preselectData = <?= /* @noEscape */ $moduleViewModel->serialize($preselectChildId) ?>;
                if (this.jsonModuleConfig?.preselect == '1' && !selectData) {
                    this.selectChildProduct(preselectData);
                }

                <?php endif; ?>
            },
            async selectChildProduct(preselectData) {
                async function waitForElements(selector) {
                    return new Promise((resolve, reject) => {
                        const interval = setInterval(() => {
                            const elements = document.querySelectorAll(selector);
                            if (elements.length > 0) {
                                clearInterval(interval);
                                resolve(elements);
                            }
                        }, 100); // Kiểm tra mỗi 100ms
                    });
                }

                for (const superAttribute in preselectData) {
                    try {
                        let listChildAttributeElements = await waitForElements(`[name="super_attribute[${superAttribute}]"]`);
                        listChildAttributeElements.forEach(function (childElement) {
                            if (childElement.value !== undefined) {
                                if (childElement.value == preselectData[superAttribute]) {
                                    childElement.click();
                                } else if (childElement.nodeName === 'SELECT') {
                                    for (let childrenKey in childElement.children) {
                                        var childOption = childElement.children[childrenKey];
                                        if (childOption.value !== undefined && childOption.value == preselectData[superAttribute]) {
                                            childOption.setAttribute("selected", "selected");
                                            childElement.dispatchEvent(new Event("change", {
                                                'detail': {
                                                    attributeId: superAttribute,
                                                    value: childOption.value
                                                }
                                            }));
                                        }
                                    }
                                }
                            }
                        });
                    } catch (error) {
                        console.error(error.message);
                    }
                }
            },
            appendDataToElement(target,data) {
                target.insertAdjacentHTML("beforeend", data);
                this.handleScriptTags(target);
            },
            handleScriptTags(target) {
                const scripts = target.querySelectorAll("script");
                scripts.forEach((script) => {
                    script.remove();
                    const newScript = document.createElement("script");
                    if (script.hasAttribute("src")) {
                        newScript.src = script.src;
                    } else {
                        newScript.textContent = script.textContent;
                    }
                    document.body.appendChild(newScript);
                });
            }
        }
    }
</script>

<div id="simple-detail-control-data" @load.window="onPageload()" x-data="initSimpleDetailData<?= $escaper->escapeJs($uniqueId) ?>()"
     x-bind="eventListeners">
    <svg xmlns="http://www.w3.org/2000/svg" class="fill-current w-6 h-6 hidden bss-summary-star" viewBox="3 0 20 20"
         fill="currentColor">
        <path
            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371
                        1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54
                        1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1
                        1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
    </svg>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="3 0 20 20" fill="currentColor" class="w-6 h-6 bss-crescent-star hidden">
        <defs>
            <linearGradient id="partialFill<?= /* @noEscape */ $uniqueId ?>">
                <stop offset="0%" :stop-color="yellowHex"/>
                <stop :offset="(starFragment * 100) + '%'" :stop-color="yellowHex"/>
                <stop :offset="(starFragment * 100) + '%'   " :stop-color="greyHex"/>
                <stop offset="100%" :stop-color="greyHex"/>
            </linearGradient>
        </defs>
        <g fill="url(#partialFill<?= $uniqueId ?>)">
            <path
                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969
                            0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54
                            1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1
                            0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
        </g>
    </svg>
</div>

<!-- rich content -->
<?php if (isset($childProductData['child'])) : ?>
    <?php
        $hasVariant = [];
        foreach ($childProductData['child'] as $productData) {
            $hasVariant[] = $productData['rich_content'];
        }

    ?>
    <script type="application/ld+json">
        [
            {
                "@context": "https://schema.org/",
                "@type": "ProductGroup",
                "name": "<?= $childProductData['name'] ?>",
                "description": "<?= strip_tags($childProductData['sdesc']) ?>",
                "brand": {
                    "@type": "Brand",
                    "name": "<?= $childProductData['madhat_brand_value'] ?>"
                },
                "ProductGroupID": "<?= $childProductData['sku'] ?>",
                "url": "<?= $childProductData['url'] ?>",
                "variesBy": <?= json_encode($childProductData['varies_by']); ?>,
                "hasVariant": <?= json_encode($hasVariant); ?>
            }
        ]
    </script>

<?php endif; ?>

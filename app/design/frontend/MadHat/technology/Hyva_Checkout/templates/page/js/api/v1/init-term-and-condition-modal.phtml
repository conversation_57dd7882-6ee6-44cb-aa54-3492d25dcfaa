<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

?>

<!-- term and conditions modal implementation  -->
<script>
    function initTermAgreement() {
        return {
            ...hyva.modal(),
            showAgreementContent($event) {
                let targetElementId = $event.target.id;
                let targetTitle = $event.target.title;
                let agreementId = this.$root.dataset.agreementId;
                Array.from(this.$root.querySelectorAll('.agreement-modal-content')).map(item => {
                    item.classList.add("hidden");
                });
                this.$root.querySelector('#' + targetElementId + '-content').classList.remove("hidden");
                this.$root.querySelector('#agreement_' + agreementId + '-label').innerText = targetTitle;

                this.show("agreement_" + agreementId, $event);
            },
        }
    }
</script>
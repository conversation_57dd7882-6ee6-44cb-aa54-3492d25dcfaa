<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Magewire\Checkout\CustomerComment;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;

/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var  CustomerComment $magewire */

$iconsViewModel = $viewModels->require(HeroiconsOutline::class);
$viewModel = $viewModels->require(Hyva\Checkout\ViewModel\Checkout\CustomerComment::class);
?>
<div class="pt-8">
    <div x-data="{
            content: $wire.comment ?? '',
            limit: $el.dataset.limit,
            get remaining() {
                return this.limit - this.content.length
            }
         }"
         x-init="$watch('limit')"
         data-limit="40"
    >
        <div class="flex justify-between text-sm font-medium">
            <label class="block text-gray-700 mb-3" for="order-comment">
                <?= $escaper->escapeHtml(__('Order Comment')) ?> (<?= /* @noEscape */ mb_strtolower($escaper->escapeHtml(__('Optional'), 'UTF-8')) ?>)
            </label>

            <div class="text-green-500 space-x-4">
                <span class="hidden"
                      wire:loading
                      wire:target="comment"
                      wire:loading.class.remove="hidden"
                >
                    ...<?= $escaper->escapeHtml(__('saving')) ?>
                </span>

                <?php if ($magewire->saved): ?>
                    <span wire:target="comment" class="flex"
                          wire:loading.class="hidden"
                    >
                        <?= $escaper->escapeHtml(__('saved successfully')) ?>
                    </span>
                <?php endif ?>
            </div>
        </div>

        <textarea id="order-comment"
                  wire:model.delay.750ms="comment"
                  x-ref="content"
                  x-bind:maxlength="limit"
                  x-model="content"
                  type="text"
                  rows="3"
                  class="block w-full
                         shadow-sm
                         border-gray-300 rounded-md
                         resize-y"
                  <?php if ($viewModel->hasPlaceholderText()): ?>
                  placeholder="<?= $escaper->escapeHtmlAttr($viewModel->getPlaceholderText()) ?>"
                  <?php endif ?>
        ></textarea>

        <div class="w-full flex flex-row-reverse mt-1 font" x-ref="remaining" x-cloak>
            <span x-text="(remaining + '/' + limit)"
                  x-show="remaining < 10 && remaining !== 0"
                  x-bind:class="{
                      25: 'text-green-700',
                      50: 'text-purple-700 font-medium',
                      75: 'text-yellow-600 font-semibold',
                      100: 'text-red-600 font-bold'
                  }[(100 - (Math.round(remaining / 25) * 25))]"
            ></span>

            <div x-ref="remaining">
                <p class="text-red-700 text-sm" x-show="remaining === 0">
                    <?= $escaper->escapeHtml(__('You\'ve reached the maximum amount of characters')) ?>
                </p>
            </div>
        </div>
    </div>
</div>

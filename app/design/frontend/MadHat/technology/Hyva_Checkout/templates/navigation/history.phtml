<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Checkout\Model\Navigation\Checkout;
use Hyva\Checkout\ViewModel\Navigation;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */
/** @var Navigation $viewModel */
/** @var Checkout $checkout */

$viewModel = $viewModels->require(Navigation::class);
$navigator = $viewModel->getNavigator();
$checkout  = $navigator->getActiveCheckout();
?>
<?php if ($checkout->hasSteps() && $checkout->isMultiStepper()): ?>
    <?php $previous = $checkout->getStepBefore($navigator->getActiveStep()) ?>
    <?php $next = $checkout->getStepAfter($navigator->getActiveStep()) ?>

    <?php if ($previous && $viewModel->getSystemConfig()->showNavigationBackButton()): ?>
        <button type="button"
                rel="prev"
                class="btn btn-secondary"
                x-spread="buttonPrevious('<?= $escaper->escapeJs($previous->getRoute()) ?>')"
                x-bind="buttonPrevious('<?= $escaper->escapeJs($previous->getRoute()) ?>')"
        >
            <?= $escaper->escapeHtml(__('Back to %1', [mb_strtolower((string) __($previous->getLabel('previous step')), 'UTF-8')])) ?>
        </button>
    <?php endif ?>

    <?php if ($next): ?>
        <button type="button"
                rel="next"
                class="btn btn-primary"
                x-spread="buttonNext('<?= $escaper->escapeJs($next->getRoute()) ?>')"
                x-bind="buttonNext('<?= $escaper->escapeJs($next->getRoute()) ?>')"
        >
            <?= $escaper->escapeHtml(__('Proceed to %1', [mb_strtolower((string) __($next->getLabel('next step')), 'UTF-8')])) ?>
        </button>
    <?php endif ?>
<?php endif ?>

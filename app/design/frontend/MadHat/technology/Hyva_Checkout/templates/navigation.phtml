<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2022-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\View\Element\Template;

/** @var Template $block */

$navHtmlLeft = $block->getChildHtml('hyva.checkout.navigation.left');
$navHtmlRight = $block->getChildHtml('hyva.checkout.navigation.right');
?>
<nav x-data="Object.assign(initCheckoutNavigation(), initNavigation())"
     x-init="initialize()"
     class="nav-main md:mb-0 overflow-hidden rounded-lg bg-gray-100 p-2"
>
    <div class="flex flex-col-reverse md:flex-row gap-y-2 md:gap-y-0 md:gap-x-0">
        <?php if (!empty(trim($navHtmlLeft))): ?>
            <div class="flex flex-col-reverse md:flex-row
                        gap-y-2 md:gap-x-2
                        w-full
                        mr-4
                        md:items-center"
            >
                <?= /* @noEscape */ $navHtmlLeft ?>
            </div>
        <?php endif ?>

        <div class="flex flex-col-reverse md:flex-row
                    gap-y-2 md:gap-x-2
                    w-full
                    justify-end
                    md:items-center"
        >
            <?= /* @noEscape */ $navHtmlRight ?>
        </div>
    </div>
</nav>

<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SwatchRenderer;
use MadHat\Catalog\ViewModel\SwatchIcons;
use Magento\Framework\Escaper;
use Magento\Swatches\Block\Product\Renderer\Configurable;
use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Configurable $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

/** @var SwatchRenderer $swatchRendererViewModel */
$swatchRendererViewModel = $viewModels->require(SwatchRenderer::class);

/** @var SwatchIcons $swatchIcons */
$swatchIcons = $viewModels->require(SwatchIcons::class);

$product = $block->getProduct();
$productId = $product->getId();

$attributes = $block->decorateArray($block->getAllowAttributes());

$layout = $block->getLayout();
$swatchItemBlock = $layout->getBlock('product.swatch.item');
$swatchItemBlock->setData('product_id', $productId);

$tooltipBlockHtml = $block->getBlockHtml('product.swatch.tooltip');
?>
<?php if (count($attributes)): ?>
    <script>
        function initConfigurableSwatchOptions_<?= (int) $productId ?>() {
            const configurableOptionsComponent = initConfigurableOptions(
                '<?= (int) $productId ?>',
                <?= /* @noEscape */ $block->getJsonConfig() ?>
            );
            const swatchOptionsComponent = initSwatchOptions(
                <?= /* @noEscape */ $block->getJsonSwatchConfig() ?>
            );

            const showMoreColorComponent = {
                showText: true,
                elementLengthToShow: 8,
                colorSwatchSelector: ".swatch-attribute.madhat_color .swatch-attribute-options > div",
                checkVisibleShowMoreButton(showSwatches) {
                    if (showSwatches) {
                        let colorSwatchElementsLength = this.$root.querySelectorAll(this.colorSwatchSelector).length;
                        if (colorSwatchElementsLength >= this.elementLengthToShow) {
                            return true;
                        }
                    }

                    return false;
                },
                toggleColorSwatchContent() {
                    let colorSwatchElements = this.$root.querySelectorAll(this.colorSwatchSelector);
                    colorSwatchElements.forEach((colorSwatchElement, index) => {
                        if (!this.isColorSwatchOptionVisible(index + 1)) {
                            colorSwatchElement.classList.toggle('hidden');
                        }
                    });
                    this.showText = !this.showText;
                },
                isColorSwatchOptionVisible(filterOptionIndex) {
                    if (filterOptionIndex > this.elementLengthToShow) {
                        return false;
                    } else {
                        return true;
                    }
                },
            };

            const initFixConfigurableOptionSelectionComponent = {
                dispatchFixConfigurableOptionSelectionEvent($dispatch, showSwatches) {
                    $dispatch('fix-configurable-option-selection',
                        {
                            showSwatches: showSwatches
                        }
                    );
                }
            };

            return Object.assign(
                configurableOptionsComponent,
                swatchOptionsComponent,
                showMoreColorComponent,
                initFixConfigurableOptionSelectionComponent
            );
        }
    </script>

    <div x-data="initConfigurableSwatchOptions_<?= (int) $productId ?>()"
         x-init="init(); initShowSwatchesIntersect();"
         @private-content-loaded.window="onGetCartData($event.detail.data)"
         class="relative"
    >
        <div class="overflow-hidden">
            <?php foreach ($attributes as $attribute): ?>
                <?php $attributeId = $attribute->getAttributeId(); ?>
                <?php $productAttribute = $attribute->getProductAttribute();  ?>
                <?php if ($swatchRendererViewModel->isSwatchAttribute($productAttribute)): ?>
                    <div class="swatch-attribute border-b items-center flex min-h-14 border-cgrey-75
                            <?= $escaper->escapeHtmlAttr($productAttribute->getAttributeCode()) ?>">
                        <template x-if="showSwatches">
                            <div class="pdp-swatch-row ">

                                <label
                                    class="pdp-swatch-left-col product-option-label"
                                    id="attribute-label-<?= $escaper->escapeHtmlAttr($productId . '-' . $attributeId) ?>"
                                    aria-hidden="true"
                                >
                                    <?php $attributeCode = $productAttribute->getAttributeCode(); ?>
                                    <span class="pdp-swatch-icon <?= $attributeCode ?>">
                                        <?= $swatchIcons->getSwatchIcons($attributeCode) ?>
                                    </span>
                                    <span class="whitespace-nowrap"><?= $escaper->escapeHtml($productAttribute->getStoreLabel()) ?></span>
                                    <span
                                        class="text-gray-900 ml-2 font-bold label-truncate"
                                        x-text="getSwatchText(<?= (int)$attributeId ?>, selectedValues[<?= (int)$attributeId ?>])"
                                    ></span>
                                </label>
                                <div class="pdp-swatch-right-col product-option-values">
                                    <div
                                         class="flex items-center -mx-2 lg:-mx-[10px] space-x-2 lg:space-x-[10px] gap-y-2 lg:gap-y-[10px] swatch-attribute-options"
                                         role="radiogroup"
                                         aria-label="<?= $escaper->escapeHtmlAttr($productAttribute->getStoreLabel()) ?>"
                                    >
                                        <template
                                            x-for="(item, index) in optionConfig.attributes[<?= (int) $attributeId ?>].options"
                                            :key="item.id"
                                        >
                                            <?= /* @noEscape */ $swatchItemBlock->setData('attribute_id', $attributeId)->toHtml(); ?>
                                        </template>
                                    </div>

                                    <!-- TODO For Developer -->
                                    <?php if ($productAttribute->getAttributeCode() == 'madhat_color') : ?>
                                        <div class="show-more-variants">
                                            <a
                                                href="javascript:void(0)"
                                                class="inline-flex justify-center px-4 py-2 bg-white focus:outline-none"
                                                @click="toggleColorSwatchContent()"
                                                x-show="checkVisibleShowMoreButton(showSwatches)"
                                            >
                                                <span x-text="showText ? 'Show less variants' : 'Show more variants'"><?= __('Show more variants') ?></span>
                                                <i
                                                    class="far"
                                                    :class="{
                                                        'fa-chevron-up' : showText,
                                                        'fa-chevron-down' : !showText
                                                    }"
                                                ></i>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                    <div x-init="dispatchFixConfigurableOptionSelectionEvent($dispatch, showSwatches)" class="hidden"></div>
                                </div>
                            </div>
                        </template>
                    </div>
                <?php else: ?>
                    <div class="flex flex-col sm:flex-row items-center py-2 w-full border-t border-gray-300">
                        <label class="w-full sm:w-1/2 text-left text-gray-700 label"
                               for="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                        >
                            <span>
                                <?= $escaper->escapeHtml($attribute->getProductAttribute()->getStoreLabel()) ?>
                            </span>
                        </label>
                        <div class="w-full sm:ml-2 sm:w-1/2 text-left text-gray-900">
                            <select name="super_attribute[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>]"
                                    id="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                                    class="form-select super-attribute-select max-w-full"
                                    x-on:change="changeOption(<?= (int) $attribute->getAttributeId() ?>, event.target.value)"
                                    required="required">
                                <option value="">
                                    <?= $escaper->escapeHtml(__('Choose an Option...')) ?>
                                </option>
                                <template
                                    x-for="(item, index) in getAllowedAttributeOptions(<?= (int) $attribute->getAttributeId() ?>)"
                                    :key="item.id"
                                >
                                    <option
                                        :value="item.id"
                                        x-html="item.label"
                                        :selected="selectedValues[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>] ===
                                item.id">
                                    </option>
                                </template>
                            </select>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        <?= /* @noEscape */ $tooltipBlockHtml ?>
    </div>

<?php endif;?>


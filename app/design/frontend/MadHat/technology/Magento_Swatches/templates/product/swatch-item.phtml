<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Swatches\ViewModel\Product\Renderer\Configurable as ConfigurableViewModel;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ConfigurableViewModel $configurableViewModel */
$configurableViewModel = $viewModels->require(ConfigurableViewModel::class);

$productId = $block->getProductId();
$attributeId = $block->getAttributeId();

if (!$productId || !$attributeId) {
    return '';
}
?>

<div x-id="['attribute-option-<?= (int) $productId ?>-'+item.id]">
    <template x-if="optionIsEnabled(<?= (int) $attributeId ?>, item.id) && optionIsActive(<?= (int) $attributeId ?>, item.id)">
        <label
            :for="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
            class="swatch-option relative shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label"
            :class="{
                'border-primary shadow-md shadow-cyan-500/50':
                    (selectedValues[<?= (int)$attributeId ?>] === item.id),
                'border-container-darker':
                    (selectedValues[<?= (int)$attributeId ?>] !== item.id),
                'w-6 h-6' : !isTextSwatch(<?= (int) $attributeId ?>, item.id),
                'border-container-lighter' : focusedLabel === item.id
            }"
            :style="getSwatchBackgroundStyle('<?= (int) $attributeId ?>',item.id)"
            <?php if ($configurableViewModel->getShowSwatchTooltip()): ?>
                @mouseenter.self="activeTooltipItem = {
                    attribute: '<?= (int) $attributeId ?>',
                    item: item.id,
                    index
                }; tooltipPositionElement = $event.target;"
                @mouseleave.self="activeTooltipItem = false"
            <?php endif; ?>
        >
            <input
                :id="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
                :value="item.id"
                name="super_attribute[<?= (int) $attributeId ?>]"
                type="radio"
                class="inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input"
                style="z-index:-1"
                x-on:focus="focusLabel(item.id)"
                x-on:blur="blurLabel()"
                x-on:change="changeOption(<?= (int) $attributeId ?>, $event.target.value)"
                x-on:click="changeOption(<?= (int) $attributeId ?>, item.id)"
                x-model="selectedValues[<?= (int) $attributeId ?>]"
                :required="getAllowedAttributeOptions(<?= (int) $attributeId ?>).filter(
                    attributeOption => selectedValues[attributeOption]
                ).length === 0"
                :aria-label="getSwatchText(<?= $escaper->escapeHtmlAttr($attributeId) ?>, item.id)"
                aria-describedby="attribute-label-<?= $escaper->escapeHtmlAttr($productId . '-' . $attributeId) ?>"
            >
            <template x-if="isTextSwatch(<?= (int) $attributeId ?>, item.id)">
                <div
                    x-html="getSwatchText(<?= (int) $attributeId ?>, item.id)"
                    class="pointer-events-none select-none whitespace-nowrap"
                    aria-hidden="true"
                ></div>
            </template>
        </label>
    </template>

    <template x-if="optionIsEnabled(<?= (int) $attributeId ?>, item.id) && !optionIsActive(<?= (int) $attributeId ?>, item.id)">
        <label
            :for="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
            class="swatch-option relative shadow-sm cursor-pointer select-none bg-container-lighter product-option-value-label"
            :class="{
                'border-primary shadow-md shadow-cyan-500/50':
                    (selectedValues[<?= (int)$attributeId ?>] === item.id),
                'border-container-darker':
                    (selectedValues[<?= (int)$attributeId ?>] !== item.id),
                'w-6 h-6' : !isTextSwatch(<?= (int) $attributeId ?>, item.id),
                'border-container-lighter' : focusedLabel === item.id
            }"
            :style="getSwatchBackgroundStyle('<?= (int) $attributeId ?>',item.id)"
            <?php if ($configurableViewModel->getShowSwatchTooltip()): ?>
                @mouseenter.self="activeTooltipItem = {
                    attribute: '<?= (int) $attributeId ?>',
                    item: item.id,
                    index
                }; tooltipPositionElement = $event.target;"
                @mouseleave.self="activeTooltipItem = false"
            <?php endif; ?>
        >
            <input
                :id="$id('attribute-option-<?= (int) $productId ?>-'+item.id)"
                :value="item.id"
                name="super_attribute[<?= (int) $attributeId ?>]"
                type="radio"
                class="inline-block absolute p-0 border-0 focus:border-0 focus:ring-0 product-option-value-input"
                style="z-index:-1"
                x-on:focus="focusLabel(item.id)"
                x-on:blur="blurLabel()"
                x-on:change="changeOption(<?= (int) $attributeId ?>, $event.target.value)"
                x-model="selectedValues[<?= (int) $attributeId ?>]"
                :required="getAllowedAttributeOptions(<?= (int) $attributeId ?>).filter(
                    attributeOption => selectedValues[attributeOption]
                ).length === 0"
                :aria-label="getSwatchText(<?= $escaper->escapeHtmlAttr($attributeId) ?>, item.id)"
                aria-describedby="attribute-label-<?= $escaper->escapeHtmlAttr($productId . '-' . $attributeId) ?>"
            >
            <template x-if="isTextSwatch(<?= (int) $attributeId ?>, item.id)">
                <div
                    x-html="getSwatchText(<?= (int) $attributeId ?>, item.id)"
                    class="pointer-events-none select-none whitespace-nowrap"
                    aria-hidden="true"
                ></div>
            </template>
            <svg class="absolute inset-0 w-full h-full text-gray-500 bg-white/25">
                <line x1="0" y1="100%" x2="100%" y2="0" class="stroke-current stroke-1"></line>
            </svg>
        </label>
    </template>
</div>

<?php
declare(strict_types=1);

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop By Brand Compatibility with Hyva for Magento 2
 */

use Amasty\ShopbyBrand\Block\BrandsPopup as BrandsPopupBlock;
use Amasty\ShopByBrandHyvaCompatibility\ViewModel\BrandsPopup;
use Hyva\Theme\ViewModel\Store as StoreViewModel;
use Hyva\GraphqlViewModel\ViewModel\GraphqlViewModel;
use Amasty\ShopByBrandHyvaCompatibility\ViewModel\BrandsPopupGraphQlQuery;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;

/**
 * @var BrandsPopupBlock $block
 * @var Escaper $escaper
 * @var ViewModelRegistry $viewModels
 */

/** @var BrandsPopupGraphQlQuery $popupGraphQLQueryViewModel */
$popupGraphQLQueryViewModel = $viewModels->require(BrandsPopupGraphQlQuery::class);
/** @var GraphqlViewModel $viewModelGraphQl */
$viewModelGraphQl = $viewModels->require(GraphqlViewModel::class);
/** @var StoreViewModel $viewModelStore */
$viewModelStore = $viewModels->require(StoreViewModel::class);
/** @var BrandsPopup $brandsPopupViewModel */
$brandsPopupViewModel = $viewModels->require(BrandsPopup::class);
// phpcs:disable Generic.Files.LineLength.TooLong
?>
<script>
    'use strict';

    function amBrandsPopup() {
        let cached = false;

        const displayError = (error) => {
            console.error(error);
            typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                [{
                    type: "error",
                    text: "<?= $escaper->escapeJs(__("Something went wrong. Please try again.")) ?>"
                }], 10000
            );
        };

        let letters = [
            "A","B","C","D",
            "E","F","G","H","I","J","K","L","M",
            "N","O","P","Q","R","S","T","U","V","W","X","Y","Z","#"
        ];

        return {
            hoverPanelActiveId: null,
            selectedLetter: null,
            items: [],
            letters: [],
            showCount: null,
            showFilter: null,
            showImages: true,
            showSearch: null,
            loading: false,
            label: '<?= $escaper->escapeJs($brandsPopupViewModel->getLabel()) ?>',
            isPopupEnabled: false,
            isTopMenuItemEnabled: false,
            cache: false,
            isMobile: false,

            initErrorMessages(errors) {
                const messages = [];
                for (let error in Object.keys(errors)) {
                    messages.push({type: 'error', text: errors[error].message});
                }
                typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(messages, 10000);
            },

            chooseLetter(letter) {
                this.selectedLetter = this.selectedLetter === letter ? null : letter;
            },

            getQuery() {
                return <?= /** @noEscape */ json_encode($viewModelGraphQl->query(
                    'brands_popup_query',
                    "query BrandsPopupQuery { {$popupGraphQLQueryViewModel->queryString()} }"
                )); ?>
            },

            /**
             * Retrieve media url for given path
             *
             * @param path
             * @returns {string}
             */
            getMediaUrl(path) {
                let stringToRemove = 'index.php/';
                let baseUrl = BASE_URL.replace(stringToRemove, '');
                return path.includes(baseUrl) ? path : baseUrl + path;
            },

            checkIsMobileResolution() {
                this.isMobile = window.matchMedia('(max-width: 1023px)').matches;
            },

            getBrandUrl(url){
                return BASE_URL + url;
            },

            popupInit() {
                this.checkIsMobileResolution();

                if (this.isMobile) {
                    return;
                }

                this.loading = true;

                if (cached) {
                    this.loading = false;
                    return;
                }

                const req = this.sendRequest();
                req.then(result => {
                    if (result && result.errors) {
                        this.initErrorMessages(result.errors);
                        return;
                    }

                    const data = result.data?.ambrandlist;
                    const mapper = this.settingsMapper(result)
                    for (let config in mapper) {
                        this[config] = mapper[config];
                    }

                    if (!this.filterDisplayAll) {
                        letters = data.all_letters.split(',');
                    }

                    const items = data.items.map(brand => {
                        if (brand.img && (brand.img.indexOf("http://") === 0 || brand.img.indexOf("https://") === 0)) {
                            brand.image = brand.img;
                            return brand;
                        } else if (brand.img) {
                            brand.image = this.getMediaUrl(brand.img);
                            return brand;
                        } else if (brand.image) {
                            brand.image = this.getMediaUrl(brand.image);
                            return brand;
                        } else {
                            return brand;
                        }
                    });

                    this.letters = letters.map(letter => ({
                        letter: letter,
                        brands: this.getLetterItems(items, letter),
                    }));

                    this.items = this.letters.filter(row => row.brands && row.brands.length > 0);

                    cached = true;
                }).catch(displayError).finally(() => {
                    this.loading = false;
                });
            },

            /**
             * Get brands by letter
             *
             * @param result
             * @returns {{imageWidth: *, isTopMenuItemEnabled: *, showCount: *, displayAll: *, showFilter: *, showImages: *, isPopupEnabled: *, label: *, imageHeight: *, filterDisplayAll: *, brandsPage: *}}
             */
            settingsMapper(result) {
                const config = result.data.storeConfig?.amshopby_brand_general_brands_popup_config;
                return {
                    showImages: config.show_images,
                    showCount: config.show_count,
                    showFilter: config.show_filter,
                    imageWidth: config.image_width,
                    imageHeight: config.image_height,
                    filterDisplayAll: config.filter_display_all,
                    displayAll: config.display_zero,
                    isPopupEnabled: result.data.storeConfig?.amshopby_brand_general_brands_brands_popup,
                    label: result.data.storeConfig?.amshopby_brand_general_menu_item_label,
                    isTopMenuItemEnabled: result.data.storeConfig?.amshopby_brand_general_topmenu_enabled,
                    brandsPage: result.data.storeConfig?.amshopby_brand_general_brands_page,
                }
            },

            /**
             * Send request to graphql
             *
             * @returns {Promise<any>}
             */
            sendRequest() {
                return fetch('<?= $escaper->escapeUrl($block->getBaseUrl()) ?>graphql', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Store': '<?= /* @noEscape */ $viewModelStore->getStoreCode() ?>'
                    },
                    credentials: 'include',
                    body: JSON.stringify({query: this.getQuery(), variables: {}})
                }).then(
                    response => response.json()
                );
            },

            /**
             * Get brands by letter
             *
             * @param items
             * @param letter
             * @returns {*}
             */
            getLetterItems(items, letter) {
                return items.filter(row => row.letter === letter);
            },

            convertHtml(text) {
                let textField = document.createElement("textarea");
                textField.innerHTML = text;
                return textField.value;
            }
        }
    }
</script>

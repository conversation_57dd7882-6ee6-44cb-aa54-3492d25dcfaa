<?php
declare(strict_types=1);

/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Shop By Brand Compatibility with Hyva for Magento 2
 */

use Amasty\ShopbyBrand\Block\BrandsPopup as BrandsPopupBlock;
use Amasty\ShopByBrandHyvaCompatibility\ViewModel\BrandsPopup;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;

/**
 * @var BrandsPopupBlock $block
 * @var Escaper $escaper
 * @var ViewModelRegistry $viewModels
 */

/** @var BrandsPopup $brandsPopupViewModel */
$brandsPopupViewModel = $viewModels->require(BrandsPopup::class);
// phpcs:disable Generic.Files.LineLength.TooLong
?>
<div class="brand-overlay flex flex-row sm:flex-column overflow-hidden h-full w-full bg-cgrey-40" x-show="!loading">
    <div class="w-3/12 p-5 pr-0" x-cloak x-show="showFilter">
        <div class="show-all-filter-btn flex justify-center">
            <a href="<?= $escaper->escapeUrl($brandsPopupViewModel->getBrandsPageUrl()) ?>"
            class="py-1 inline-flex justify-center items-center box-content border border-white bg-white text-sm font-semibold text-cgrey-65 mb-1 px-2  hover:border-cblue"
            title="<?= $escaper->escapeHtmlAttr(__('All Brands')) ?>">
                <?= $escaper->escapeHtml(__('All Brands')) ?>
            </a>
        </div>
        <div class="w-full mt-4 flex gap-1 space-between flex-wrap">
            <template x-for="letter in letters">
                <button
                        @click="chooseLetter(letter.letter)"
                        class="bg-white cursor-pointer w-7 h-7 hover:text-blue-400 text-sm font-semibold hover:border-blue-400 justify-center items-center align-center inline border"
                        :class="{'border-blue-400': selectedLetter === letter.letter,
                        'bg-cgrey-50 border-cgrey-50 text-cgrey-65 cursor-default pointer-events-none': !letter.brands || letter.brands.length === 0}"
                        x-text="letter.letter">
                </button>
            </template>
        </div>
    </div>
    <div class="w-8/12 overflow-y-auto h-full p-5 box-content flex-grow brand-right-content">
        <template x-for="letter in items" :key="letter.letter">
            <section class="ambrands-letters-list">
                <div class="ambrands-letter mb-7"
                     x-show="!selectedLetter || selectedLetter === letter.letter">
                    <h3 class="ambrands-title text-2xl mb-2 font-bold" x-text="letter.letter"></h3>
                    <div class="ambrands-content flex flex-wrap gap-5">
                        <template x-for="brand in letter.brands" :key="brand.brandId">
                            <a :href="getBrandUrl(brand.url)"
                               class="ambrands-inner cursor-pointer ambrands-brand-popup-item border border-gray-100 hover:border-blue-400 rounded-md text-center flex flex-col items-center justify-center bg-white w-[120px] h-28 overflow-x-hidden"
                               :title="brand.label">
                                <?php if ($brandsPopupViewModel->isShowImages()): ?>
                                    <template x-if="brand.image">
                                        <img class="ambrands-image mx-auto py-3"
                                             :style="`max-width: ${imageWidth ? imageWidth + 'px' : 'unset' }; max-height: ${imageHeight ? imageHeight + 'px' : 'unset'}`"
                                             :src="brand.image"
                                             :alt="brand.label"
                                             loading="lazy"/>
                                    </template>
                                    <template x-if="!brand.image">
                                            <span class="block mx-auto py-3 uppercase text-sm text-gray-300"
                                                  x-text="brand.label"></span>
                                    </template>
                                <?php else: ?>
                                    <span class="ambrands-empty" :title="brand.label"></span>
                                <?php endif; ?>

                                <span class="hidden ambrands-label mt-auto block" :style="`min-width: ${imageWidth}px`">
                                        <?php if ($brandsPopupViewModel->isShowCount()): ?>
                                            <span class="ambrands-count text-gray-400" x-text="'(' + brand.cnt + ')'"></span>
                                        <?php endif; ?>
                                    </span>
                            </a>
                        </template>
                    </div>
                </div>
            </section>
        </template>
    </div>
    <div x-show="false">
        <?= $escaper->escapeHtml(
            __('Please select brand attribute in Stores -> Configuration ->
                Amasty Extensions -> Improved Layered Navigation: Brands.')
        ) ?>
    </div>
</div>
<div x-cloak x-show="loading" class="flex flex-row sm:flex-column overflow-hidden w-full bg-white">
    <?= $escaper->escapeHtml(__('Loading...')) ?>
</div>

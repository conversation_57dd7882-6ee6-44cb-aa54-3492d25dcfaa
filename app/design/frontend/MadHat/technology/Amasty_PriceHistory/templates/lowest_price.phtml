<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) Amasty (https://www.amasty.com)
 * @package Price History Hyva Compatibility
 */

use Amasty\PriceHistory\Block\LowestPrice;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Escaper;

/** @var LowestPrice $block */
/** @var Escaper $escaper */

$configurableViewModel = $block->getData('view_model');
$product = $block->getProduct();
?>

<div x-data="amOmnibusPrice_<?= (int)$product->getId() ?>" x-show="currentOmnibusPrice">
    <div class="info-icon inline-block relative">
        <i class="fa-regular fa-circle-info"></i>
        <div class="tooltip-container amprice-history-container technology-template">
            <span class="omnibus-price"
                <?php if ($product->getTypeId() === Configurable::TYPE_CODE): ?>
                    x-bind="eventListeners"
                    x-text="currentOmnibusPrice"
                <?php endif; ?>
            >
                <?= $escaper->escapeHtml($configurableViewModel->getDisplayText($product), ['span', 'i', 'u', 'b']) ?>
            </span>
        </div>
    </div>
</div>

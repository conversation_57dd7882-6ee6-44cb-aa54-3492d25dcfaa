<?php

namespace Makewebo\CacheAnalyst\Ui\Component\Listing\Columns;

use Magento\Eav\Model\Entity\Attribute\Source\SourceInterface;
use Magento\Framework\Data\OptionSourceInterface;

class YesNo implements SourceInterface, OptionSourceInterface
{
    public function toOptionArray()
    {
        return $this->getAllOptions();
    }

    public function getAllOptions()
    {
        $result = [];

        foreach (self::getOptionArray() as $index => $value) {
            $result[] = ['value' => $index, 'label' => $value];
        }

        return $result;
    }

    public function getOptionText($value)
    {
        $options = self::getOptionArray();
        return $options[$value] ?? null;
    }
    public static function getOptionArray()
    {
        return [0 => __('No'), 1 => __('Yes')];
    }
}

<?php

namespace Makewebo\CacheAnalyst\Plugin;

use Magento\Framework\App\Cache\Tag\Resolver;
use Magento\Framework\Event\Observer;
use Makewebo\CacheAnalyst\Helper\AnalystConfig;
use Makewebo\CacheAnalyst\Model\CacheAnalyst;
use Makewebo\CacheAnalyst\Model\CacheAnalystFactory;
use Psr\Log\LoggerInterface;

class InvalidateVarnishObserver
{
    /**
     * Invalidation tags resolver.
     *
     * @var Resolver
     */
    private $tagResolver;

    /** @var CacheAnalyst */
    private $cacheAnalystFactory;

    /** @var AnalystConfig */
    private $analystConfig;

    /** @var LoggerInterface */
    private $logger;

    public function __construct(
        Resolver $tagResolver,
        CacheAnalystFactory $cacheAnalystFactory,
        AnalystConfig $analystConfig,
        LoggerInterface $logger
    ) {
        $this->tagResolver = $tagResolver;
        $this->cacheAnalystFactory = $cacheAnalystFactory;
        $this->analystConfig = $analystConfig;
        $this->logger = $logger;
    }

    /**
     * If Varnish caching is enabled it collects array of tags of incoming object and asks to clean cache.
     */
    public function aroundExecute(
        \Magento\CacheInvalidate\Observer\InvalidateVarnishObserver $subject,
        callable $proceed,
        Observer $observer
    ) {
        if ($this->analystConfig->isEnabled()) {
            $runByCron = $this->isRunByCron();
            $hasTags = false;
            $object = $observer->getEvent()->getObject();
            if (is_object($object)) {
                $bareTags = $this->tagResolver->getTags($object);
                if (!empty($bareTags)) {
                    try {
                        $hasTags = true;
                        $eventName = $observer->getEvent()->getName();
                        $initiator = get_class($object);
                        $tags = json_encode($bareTags);
                        $tagsCount = count($bareTags);
                        $analysisRecord = $this->cacheAnalystFactory->create();
                        $analysisRecord->setData(
                            [
                                'event_name' => $eventName,
                                'class_name' => $initiator,
                                'tags' => $tags,
                                'tags_count' => $tagsCount,
                                'need_sync' => $runByCron,
                            ]
                        )->save();
                    } catch (\Exception $ex) {
                        $this->logger->error('Invalidate Varnish :'.$ex->getMessage());
                    }
                }
            }
            // if clean tags by cron then skip invalidate varnish request
            if ($hasTags && $this->analystConfig->isCleanByCron() && $runByCron) {
                return;
            }
        }
        $proceed($observer);
    }

    /**
     * @return bool
     */
    protected function isRunByCron()
    {
        $startTime = $this->analystConfig->startTime();
        $endTime = $this->analystConfig->endTime();
        $currentTime = $this->analystConfig->currentTime();

        return $currentTime >= $startTime && $currentTime <= $endTime;
    }
}

<?php

namespace Makewebo\CacheAnalyst\Cron;

use Magento\CacheInvalidate\Model\PurgeCache;
use Magento\Framework\App\ResourceConnection;
use Magento\PageCache\Model\Config as PageCacheConfig;
use Makewebo\CacheAnalyst\Helper\AnalystConfig;
use Makewebo\CacheAnalyst\Model\ResourceModel\CacheAnalyst\Collection;
use Makewebo\CacheAnalyst\Model\ResourceModel\CacheAnalyst\CollectionFactory;

class CleanTags
{
    protected $purgeCache;
    protected $pageCacheConfig;
    protected $analystConfig;

    protected $collectionFactory;
    protected $resourceConnection;
    protected $_logger;

    protected $fetchedTagsIds = [];

    public function __construct(
        PurgeCache $purgeCache,
        PageCacheConfig $pageCacheConfig,
        AnalystConfig $analystConfig,
        CollectionFactory $collectionFactory,
        ResourceConnection $resourceConnection
    ) {
        $this->purgeCache = $purgeCache;
        $this->pageCacheConfig = $pageCacheConfig;
        $this->analystConfig = $analystConfig;
        $this->collectionFactory = $collectionFactory;
        $this->resourceConnection = $resourceConnection;
        $this->_logger = $this->analystConfig->getLogger();
    }

    /**
     * Cronjob Description.
     */
    public function execute(): void
    {
        $bareTags = $this->getTags();
        $tags = [];
        $pattern = '((^|,)%s(,|$))';
        foreach ($bareTags as $tag) {
            $tags[] = sprintf($pattern, $tag);
        }
        if (!empty($tags)) {
            $this->purgeCache->sendPurgeRequest(array_unique($tags));
        }
        $this->updateRecords();
    }

    protected function getTags()
    {
        $tags = [];
        $tagsCollection = $this->getSyncTagCollection();
        if ($tagsCollection->getSize() > 0) {
            foreach ($tagsCollection as $tag) {
                $tagJson = $tag->getTags();
                if (!empty($tagJson)) {
                    $this->fetchedTagsIds[] = $tag->getId();
                    $tagList = json_decode($tagJson, true);
                    $tags = array_merge($tags, $tagList);
                }
            }
            if (!empty($tags)) {
                $beforeCount = count($tags);
                $tags = array_unique($tags);
                $afterCount = count($tags);
                $this->_logger->info('Total Tags :'.$beforeCount.' Unique:'.$afterCount);
            }
        }

        return $tags;
    }

    /**
     * @return Collection
     */
    protected function getSyncTagCollection()
    {
        /** @var Collection $collection */
        $collection = $this->collectionFactory->create();
        $collection->addFieldToFilter('need_sync', ['eq' => 1]);

        return $collection;
    }

    protected function updateRecords()
    {
        if ($this->fetchedTagsIds) {
            $connection = $this->resourceConnection->getConnection();
            $idList = implode(',', array_map('intval', $this->fetchedTagsIds)); // Sanitize the IDs
            $sql = "UPDATE makewebo_cache_analyst SET need_sync = 0 WHERE id IN ($idList)";
            $connection->query($sql);
        }
    }
}

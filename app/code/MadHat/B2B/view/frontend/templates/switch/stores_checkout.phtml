<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\StoreSwitcher;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Store\ViewModel\SwitcherUrlProvider;

// phpcs:disable Generic.Files.LineLength.TooLong

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

/** @var SwitcherUrlProvider $switcherUrlProvider */
$switcherUrlProvider = $viewModels->require(SwitcherUrlProvider::class);

/** @var Store $storeViewModel */
$storeViewModel = $viewModels->require(Store::class);

/** @var StoreSwitcher $storeSwitcherViewModel */
$storeSwitcherViewModel = $viewModels->require(StoreSwitcher::class);

$currentStore = $storeSwitcherViewModel->getStore();
?>
<?php if (count($storeSwitcherViewModel->getGroups()) > 1): ?>
    <div class="w-full">
        <?php foreach ($storeSwitcherViewModel->getGroups() as $group): ?>
            <?php if ($group->getId() !== $storeSwitcherViewModel->getCurrentGroupId()): ?>
                <a href="<?= $escaper->escapeUrl($switcherUrlProvider->getTargetStoreRedirectUrlCheckout($group->getDefaultStore())) ?>"
                   class="block text-center py-1 px-2 mb-4 bg-white border border-white hover:border-cblue rounded-full font-semibold text-sm w-full"
                >
                    <?=  /* @noEscape */  __('If you are a ') ?>
                    <?= $escaper->escapeHtml($group->getName()) ?>
                    <?=  /* @noEscape */  __('- CLICK HERE') ?>
                </a>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
<?php endif; ?>

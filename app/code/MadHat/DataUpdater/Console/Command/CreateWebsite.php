<?php

declare(strict_types=1);

namespace MadHat\DataUpdater\Console\Command;

use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use Magento\Framework\Validation\ValidationException;
use Magento\Store\Model\ResourceModel\Website;
use Magento\Store\Model\WebsiteFactory;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Question\Question;

class CreateWebsite extends Command
{
    /**
     * @var Magento\Framework\App\State
     */
    protected $appState;

    /**
     * @var Magento\Store\Model\ResourceModel\Website
     */
    protected $websiteResourceModel;

    /**
     * @var Magento\Store\Model\WebsiteFactory
     */
    protected $websiteFactory;

    /**
     * @param State $appState
     * @param Website $websiteResourceModel
     * @param WebsiteFactory $websiteFactory
     * @param string|null $name
     */
    public function __construct(
        State $appState,
        Website $websiteResourceModel,
        WebsiteFactory $websiteFactory,
        string $name = null
    ) {
        $this->appState = $appState;
        $this->websiteFactory = $websiteFactory;
        $this->websiteResourceModel = $websiteResourceModel;
        parent::__construct($name);
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName("madhat:create:website");
        $this->setDescription("Create Website");
        $this->setDefinition($this->getOptionsList());
        parent::configure();
    }

    /**
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $this->appState->setAreaCode('adminhtml');

            $website = $this->websiteFactory->create();
            $website->load($input->getOption("website-code"));

            if ($website->getId()) {
                $output->writeln(__('Website with code "%1" already exists.', $input->getOption("website-code")));
                return Cli::RETURN_FAILURE;
            }

            $website->setName($input->getOption("website-name"));
            $website->setCode($input->getOption("website-code"));
            $this->websiteResourceModel->save($website);

            $output->writeln('Website created successfully!');
            return Cli::RETURN_SUCCESS;
        } catch (\Exception $e) {
            $output->writeln('Error: ' . $e->getMessage());
            return Cli::RETURN_FAILURE;
        }
    }

    /**
     * Add options list for command
     *
     * @param string $mode
     * @return InputOption[]
     */
    public function getOptionsList($mode = InputOption::VALUE_REQUIRED)
    {
        $requiredStr = ($mode === InputOption::VALUE_REQUIRED ? '(Required) ' : '');

        return [
            new InputOption(
                "website-name",
                null,
                $mode,
                $requiredStr . 'Website Name'
            ),
            new InputOption(
                "website-code",
                null,
                $mode,
                $requiredStr . 'Website Code'
            ),
        ];
    }

    /**
     * @inheritdoc
     */
    protected function interact(InputInterface $input, OutputInterface $output)
    {
        $questionHelper = $this->getHelper('question');

        if (!$input->getOption("website-name")) {
            $question = new Question('<question>Website Name:</question> ', '');
            $this->addNotEmptyValidator($question);

            $input->setOption(
                "website-name",
                $questionHelper->ask($input, $output, $question)
            );
        }

        if (!$input->getOption("website-code")) {
            $question = new Question('<question>Website Code:</question> ', '');
            $this->addNotEmptyValidator($question);

            $input->setOption(
                "website-code",
                $questionHelper->ask($input, $output, $question)
            );
        }
    }

    /**
     * Validation for required fields
     *
     * @param Question $question
     * @return void
     */
    protected function addNotEmptyValidator(Question $question)
    {
        $question->setValidator(function ($value) {
            if (trim($value) == '') {
                throw new ValidationException(__('The value cannot be empty'));
            }

            return $value;
        });
    }
}

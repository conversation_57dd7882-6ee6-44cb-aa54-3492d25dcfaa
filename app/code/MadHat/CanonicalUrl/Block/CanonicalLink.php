<?php
declare(strict_types=1);

namespace MadHat\CanonicalUrl\Block;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Template;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\App\ObjectManager;

class CanonicalLink extends Template
{
    /**
     * @var UrlInterface
     */
    protected UrlInterface $urlInterface;

    /**
     * @var RequestInterface
     */
    protected RequestInterface $request;

    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @var ProductRepositoryInterface
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * @var Configurable
     */
    protected Configurable $configurableProduct;

    /**
     * @param Template\Context $context
     * @param UrlInterface $urlInterface
     * @param RequestInterface $request
     * @param StoreManagerInterface $storeManager
     * @param array $data
     * @param ProductRepositoryInterface|null $productRepository
     * @param Configurable|null $configurableProduct
     */
    public function __construct(
        Template\Context $context,
        UrlInterface $urlInterface,
        RequestInterface $request,
        StoreManagerInterface $storeManager,
        array $data = [],
        ?ProductRepositoryInterface $productRepository = null,
        ?Configurable $configurableProduct = null
    ) {
        $this->urlInterface = $urlInterface;
        $this->request = $request;
        $this->storeManager = $storeManager;
        $this->productRepository = $productRepository ?: ObjectManager::getInstance()->get(ProductRepositoryInterface::class);
        $this->configurableProduct = $configurableProduct ?: ObjectManager::getInstance()->get(Configurable::class);
        parent::__construct($context, $data);
    }

    /**
     * Add canonical url
     *
     * @return string|false
     * @throws NoSuchEntityException
     */
    public function getCanonicalUrl(): string|false
    {
        $allowedActions = [
            'cms_page_view',
            'contact_index_index',
            'cms_index_index',
            'customer_account_login',
            'customer_account_create',
            'catalog_product_view'
        ];

        $fullActionName = $this->getRequest()->getFullActionName();
        if (!in_array($fullActionName, $allowedActions)) {
            return false;
        }

        // Handle product pages with special logic
        if ($fullActionName === 'catalog_product_view') {
            return $this->getProductCanonicalUrl();
        }

        $currentUrl = $this->urlInterface->getCurrentUrl();

        if ($fullActionName === 'cms_index_index') {
            $currentStoreCode = $this->storeManager->getStore()->getCode();
            if ($currentStoreCode == 'b2b') {
                return $this->storeManager->getDefaultStoreView()->getBaseUrl();
            }
            return $currentUrl;
        }

        if ($fullActionName === 'customer_account_login') {
            $currentUrl = preg_replace('/\/referer\/[^\/]+\//', '', $currentUrl);
        }

        $defaultStoreBaseUrl = $this->storeManager->getDefaultStoreView()->getBaseUrl();
        $currentBaseUrl = $this->storeManager->getStore()->getBaseUrl();
        $canonicalUrl = str_replace($currentBaseUrl, $defaultStoreBaseUrl, $currentUrl);

        return $canonicalUrl;
    }

    /**
     * Get canonical URL for product pages
     *
     * @return string|false
     * @throws NoSuchEntityException
     */
    protected function getProductCanonicalUrl(): string|false
    {
        $productId = (int)$this->getRequest()->getParam('id');
        if (!$productId) {
            return false;
        }

        try {
            $product = $this->productRepository->getById($productId);

            // Check if this is a variant (simple product with configurable parent)
            if ($product->getTypeId() === 'simple') {
                $parentIds = $this->configurableProduct->getParentIdsByChild($productId);
                if (!empty($parentIds)) {
                    // This is a variant - return self-canonical URL
                    return $this->getCurrentProductUrl();
                }
            }

            // This is a base product (configurable or standalone simple) - no canonical link
            return false;

        } catch (NoSuchEntityException $e) {
            return false;
        }
    }

    /**
     * Get current product URL for canonical link
     *
     * @return string
     * @throws NoSuchEntityException
     */
    protected function getCurrentProductUrl(): string
    {
        $currentUrl = $this->urlInterface->getCurrentUrl();

        // Apply store URL transformation if needed (same logic as other pages)
        $defaultStoreBaseUrl = $this->storeManager->getDefaultStoreView()->getBaseUrl();
        $currentBaseUrl = $this->storeManager->getStore()->getBaseUrl();
        $canonicalUrl = str_replace($currentBaseUrl, $defaultStoreBaseUrl, $currentUrl);

        return $canonicalUrl;
    }
}

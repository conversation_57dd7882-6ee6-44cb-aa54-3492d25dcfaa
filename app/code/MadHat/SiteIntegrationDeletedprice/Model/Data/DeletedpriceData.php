<?php

namespace MadHat\SiteIntegrationDeletedprice\Model\Data;

use MadHat\SiteIntegrationDeletedprice\Api\Data\DeletedpriceDataInterface;

class DeletedpriceData implements DeletedpriceDataInterface
{
    protected ?string $productNo;
    protected ?string $externalProductNo;
    protected ?string $currencyCode;
    protected ?string $countryCode;
    protected ?float $numberOfUnits;
    protected ?int $portalNumber;
    protected ?int $interval;
    protected ?int $recordLineNo;
    protected ?\DateTimeInterface $timestamp;
    protected ?int $marketingType;

    public function getProductNo(): ?string
    {
        return $this->productNo;
    }

    public function setProductNo(?string $productNo): void
    {
        $this->productNo = $productNo;
    }

    public function getExternalProductNo(): ?string
    {
        return $this->externalProductNo;
    }

    public function setExternalProductNo(?string $externalProductNo): void
    {
        $this->externalProductNo = $externalProductNo;
    }

    public function getCurrencyCode(): ?string
    {
        return $this->currencyCode;
    }

    public function setCurrencyCode(?string $currencyCode): void
    {
        $this->currencyCode = $currencyCode;
    }

    public function getCountryCode(): ?string
    {
        return $this->countryCode;
    }

    public function setCountryCode(?string $countryCode): void
    {
        $this->countryCode = $countryCode;
    }

    public function getNumberOfUnits(): ?float
    {
        return $this->numberOfUnits;
    }

    public function setNumberOfUnits(?float $numberOfUnits): void
    {
        $this->numberOfUnits = $numberOfUnits;
    }

    public function getPortalNumber(): ?int
    {
        return $this->portalNumber;
    }

    public function setPortalNumber(?int $portalNumber): void
    {
        $this->portalNumber = $portalNumber;
    }

    public function getInterval(): ?int
    {
        return $this->interval;
    }

    public function setInterval(?int $interval): void
    {
        $this->interval = $interval;
    }

    public function getRecordLineNo(): ?int
    {
        return $this->recordLineNo;
    }

    public function setRecordLineNo(?int $recordLineNo): void
    {
        $this->recordLineNo = $recordLineNo;
    }

    public function getTimestamp(): ?\DateTimeInterface
    {
        return $this->timestamp;
    }

    public function setTimestamp(?\DateTimeInterface $timestamp): void
    {
        $this->timestamp = $timestamp;
    }

    public function getMarketingType(): ?int
    {
        return $this->marketingType;
    }

    public function setMarketingType(?int $marketingType): void
    {
        $this->marketingType = $marketingType;
    }
}

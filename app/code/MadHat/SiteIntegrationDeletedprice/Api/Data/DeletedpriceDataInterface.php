<?php

namespace MadHat\SiteIntegrationDeletedprice\Api\Data;

/**
 * Interface DeletedpriceDataInterface
 */
interface DeletedpriceDataInterface
{
    /**
     * @return string|null
     */
    public function getProductNo(): ?string;

    /**
     * @param string|null $productNo
     * @return void
     */
    public function setProductNo(?string $productNo): void;

    /**
     * @return string|null
     */
    public function getExternalProductNo(): ?string;

    /**
     * @param string|null $externalProductNo
     * @return void
     */
    public function setExternalProductNo(?string $externalProductNo): void;

    /**
     * @return string|null
     */
    public function getCurrencyCode(): ?string;

    /**
     * @param string|null $currencyCode
     * @return void
     */
    public function setCurrencyCode(?string $currencyCode): void;

    /**
     * @return string|null
     */
    public function getCountryCode(): ?string;

    /**
     * @param string|null $countryCode
     * @return void
     */
    public function setCountryCode(?string $countryCode): void;

    /**
     * @return float|null
     */
    public function getNumberOfUnits(): ?float;

    /**
     * @param float|null $numberOfUnits
     * @return void
     */
    public function setNumberOfUnits(?float $numberOfUnits): void;

    /**
     * @return int|null
     */
    public function getPortalNumber(): ?int;

    /**
     * @param int|null $portalNumber
     * @return void
     */
    public function setPortalNumber(?int $portalNumber): void;

    /**
     * @return int|null
     */
    public function getInterval(): ?int;

    /**
     * @param int|null $interval
     * @return void
     */
    public function setInterval(?int $interval): void;

    /**
     * @return int|null
     */
    public function getRecordLineNo(): ?int;

    /**
     * @param int|null $recordLineNo
     * @return void
     */
    public function setRecordLineNo(?int $recordLineNo): void;

    /**
     * @return \DateTimeInterface|null
     */
    public function getTimestamp(): ?\DateTimeInterface;

    /**
     * @param \DateTimeInterface|null $timestamp
     * @return void
     */
    public function setTimestamp(?\DateTimeInterface $timestamp): void;

    /**
     * @return int|null
     */
    public function getMarketingType(): ?int;

    /**
     * @param int|null $marketingType
     * @return void
     */
    public function setMarketingType(?int $marketingType): void;
}

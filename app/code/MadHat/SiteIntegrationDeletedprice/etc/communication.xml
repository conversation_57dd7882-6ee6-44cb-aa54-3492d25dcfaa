<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Communication/etc/communication.xsd">
    <topic name="site.techoutlet.deletedprice" request="MadHat\SiteIntegrationDeletedprice\Api\Data\DeletedpriceDataInterface[]">
        <handler name="madhat.siteintegrationdeletedprice.consumer" type="MadHat\SiteIntegrationDeletedprice\Model\RabbitMQ\DeletedpriceConsumer" method="processMessage"/>
    </topic>

    <!-- For Developer Only -->
<!--    <topic name="site.techoutlet-dev.deletedprice.olegh" request="MadHat\SiteIntegrationDeletedprice\Api\Data\DeletedpriceDataInterface[]">-->
<!--        <handler name="madhat.siteintegrationdeletedprice.consumer" type="MadHat\SiteIntegrationDeletedprice\Model\RabbitMQ\DeletedpriceConsumer" method="processMessage"/>-->
<!--    </topic>-->
</config>

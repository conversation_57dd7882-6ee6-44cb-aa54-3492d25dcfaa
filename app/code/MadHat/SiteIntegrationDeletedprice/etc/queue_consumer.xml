<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework-message-queue:etc/consumer.xsd">
    <consumer name="madhat.siteintegrationdeletedprice.consumer" queue="site.techoutlet.deletedprice" connection="amqpSITE"
              handler="MadHat\SiteIntegrationDeletedprice\Model\RabbitMQ\DeletedpriceConsumer::processMessage"/>

    <!-- For Developer Only  -->
<!--    <consumer name="madhat.siteintegrationdeletedprice.consumer" queue="site.techoutlet-dev.deletedprice.olegh" connection="amqpSITE"-->
<!--              handler="MadHat\SiteIntegrationDeletedprice\Model\RabbitMQ\DeletedpriceConsumer::processMessage"/>-->
</config>

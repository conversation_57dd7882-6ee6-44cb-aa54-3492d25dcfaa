<?php

namespace MadHat\SiteIntegrationCancelorder\Model\Data;

use MadHat\SiteIntegrationCancelorder\Api\Data\PaymentMethodInterface;
use Magento\Framework\DataObject;

class PaymentMethod extends DataObject implements PaymentMethodInterface
{
    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->getData('name');
    }

    /**
     * @param string|null $name
     * @return void
     */
    public function setName(?string $name)
    {
        $this->setData('name', $name);
    }

    /**
     * @return int|null
     */
    public function getValue()
    {
        return $this->getData('Value');
    }

    /**
     * @param int|null $value
     * @return void
     */
    public function setValue(?int $value)
    {
        $this->setData('Value', $value);
    }
}

<?php

namespace MadHat\Catalog\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Serialize\SerializerInterface as JsonSerializer;

/**
 * MadHatCatalogConfig configuration.
 *
 */
class MadHatCatalogConfig implements ArgumentInterface
{
    const PDP_PRODUCT_ATTRIBUTE_SET_MAPPING = 'site/pdp/product_attribute_set_mapping';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var JsonSerializer
     */
    private $jsonSerializer;

    /**
     * Data constructor.
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        JsonSerializer $jsonSerializer
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->jsonSerializer = $jsonSerializer;
    }

    /**
     * @return array
     */
    public function getPdpProductAttributeSetMapping(): array
    {
        $value = (string) $this->scopeConfig->getValue(
            self::PDP_PRODUCT_ATTRIBUTE_SET_MAPPING,
            ScopeInterface::SCOPE_STORE
        );

        try {
            $value = $this->jsonSerializer->unserialize($value);
        } catch (\Exception $e) {
            $value = [];
        }

        return $value;
    }

    /**
     * Get Tax Display Type for store view  
     */
    public function getTaxDisplayType(): ?string
    {
        return $this->scopeConfig->getValue(
            'tax/display/type',
            ScopeInterface::SCOPE_STORE
        );
    }
}

<?php
declare(strict_types=1);

namespace MadHat\Catalog\ViewModel;

use Magento\Catalog\Model\Product;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Eav\Api\AttributeSetRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class VariantAttributes implements ArgumentInterface
{
    /**
     * Config Path for Product Attribute Set Mapping
     */
    protected const CONFIG_PRODUCT_ATTRIBUTE_MAPPING_PATH = 'site/pdp/product_attribute_set_mapping';

    /**
     * @var array
     */
    protected array $attributeSetMapping = [];

    /**
     * @var Configurable
     */
    protected Configurable $configurable;

    /**
     * @var AttributeSetRepositoryInterface
     */
    protected AttributeSetRepositoryInterface $attributeRepository;

    /**
     * @var ScopeConfigInterface
     */
    protected ScopeConfigInterface $scopeConfig;

    /**
     * @var Json
     */
    protected Json $jsonSerializer;

    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @param Configurable $configurable
     * @param AttributeSetRepositoryInterface $attributeSetRepository
     * @param ScopeConfigInterface $scopeConfig
     * @param Json $jsonSerializer
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        Configurable $configurable,
        AttributeSetRepositoryInterface $attributeSetRepository,
        ScopeConfigInterface $scopeConfig,
        Json $jsonSerializer,
        StoreManagerInterface $storeManager
    ) {
        $this->configurable = $configurable;
        $this->attributeRepository = $attributeSetRepository;
        $this->scopeConfig = $scopeConfig;
        $this->jsonSerializer = $jsonSerializer;
        $this->storeManager = $storeManager;
    }

    /**
     * Return array of product attribute set mapping.
     *
     * @return array
     * @throws NoSuchEntityException
     */
    public function getProductAttributeSetMapping(): array
    {
        if (!empty($this->attributeSetMapping)) {
            return $this->attributeSetMapping;
        }

        $currentWebsiteId = $this->storeManager->getStore()->getWebsiteId();
        $this->attributeSetMapping = $this->jsonSerializer->unserialize(
            $this->scopeConfig->getValue(
                self::CONFIG_PRODUCT_ATTRIBUTE_MAPPING_PATH,
                ScopeInterface::SCOPE_WEBSITE,
                $currentWebsiteId
            )
        );

        return $this->attributeSetMapping;
    }

    /**
     * Return array of configurable attribute's code for product.
     *
     * @param Product $product
     * @return array
     * @throws NoSuchEntityException
     */
    public function getVariantAttributes(Product $product): array
    {
        if ($product->getTypeId() != 'simple') {
            return [];
        }

        if (empty($this->configurable->getParentIdsByChild($product->getId()))) {
            return [];
        }

        $attributeSetName = $this->attributeRepository->get(
            $product->getAttributeSetId()
        )->getAttributeSetName();

        $attributeSetMapping = $this->getProductAttributeSetMapping();

        return $attributeSetMapping[$attributeSetName] ?? [];
    }

    /**
     * Return Attribute Value text for Product
     *
     * @param Product $product
     * @param string $attributeCode
     * @return string
     */
    public function getAttributeValue(Product $product, string $attributeCode): string
    {
        return (string)$product->getAttributeText($attributeCode);
    }
}

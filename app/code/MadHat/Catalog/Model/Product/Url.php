<?php
namespace MadHat\Catalog\Model\Product;

use Magento\UrlRewrite\Model\UrlFinderInterface;
use Magento\UrlRewrite\Service\V1\Data\UrlRewrite;
use Magento\Framework\App\Config\ScopeConfigInterface;

/**
 * Madhat Product Url model
 *
 */
class Url extends \Magento\Catalog\Model\Product\Url
{
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @param \Magento\Framework\UrlFactory $urlFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Framework\Filter\FilterManager $filter
     * @param \Magento\Framework\Session\SidResolverInterface $sidResolver
     * @param UrlFinderInterface $urlFinder
     * @param array $data
     * @param ScopeConfigInterface|null $scopeConfig
     */
    public function __construct(
        \Magento\Framework\UrlFactory $urlFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Filter\FilterManager $filter,
        \Magento\Framework\Session\SidResolverInterface $sidResolver,
        UrlFinderInterface $urlFinder,
        array $data = [],
        ScopeConfigInterface $scopeConfig = null
    ) {
        parent::__construct($urlFactory, $storeManager, $filter, $sidResolver, $urlFinder, $data, $scopeConfig);
        $this->scopeConfig = $scopeConfig ?:
            \Magento\Framework\App\ObjectManager::getInstance()->get(ScopeConfigInterface::class);
    }

    /**
     * Retrieve Product URL using UrlDataObject
     *
     * @param \Magento\Catalog\Model\Product $product
     * @param array $params
     * @return string
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function getUrl(\Magento\Catalog\Model\Product $product, $params = [])
    {
        $routePath = '';
        $routeParams = $params;

        $storeId = $product->getStoreId();

        $categoryId = null;

        if (!isset($params['_ignore_category']) && $product->getCategoryId() && !$product->getDoNotUseCategoryId()) {
            $categoryId = $product->getCategoryId();
        }

        if ($product->hasUrlDataObject()) {
            $requestPath = $product->getUrlDataObject()->getUrlRewrite();
            $routeParams['_scope'] = $product->getUrlDataObject()->getStoreId();
        } else {
            $requestPath = $product->getRequestPath();
            if (empty($requestPath) && $requestPath !== false) {
                $filterData = [
                    UrlRewrite::ENTITY_ID => $product->getId(),
                    UrlRewrite::ENTITY_TYPE => \Magento\CatalogUrlRewrite\Model\ProductUrlRewriteGenerator::ENTITY_TYPE,
                    UrlRewrite::STORE_ID => $storeId,
                    UrlRewrite::REDIRECT_TYPE => 0
                ];
                $useCategories = $this->scopeConfig->getValue(
                    \Magento\Catalog\Helper\Product::XML_PATH_PRODUCT_URL_USE_CATEGORY,
                    \Magento\Store\Model\ScopeInterface::SCOPE_STORE
                );

                $filterData[UrlRewrite::METADATA]['category_id']
                    = $categoryId && $useCategories ? $categoryId : '';

                $rewrite = $this->urlFinder->findOneByData($filterData);

                if ($rewrite) {
                    $requestPath = $rewrite->getRequestPath();
                    $product->setRequestPath($requestPath);
                } else {
                    $product->setRequestPath(false);
                }
            }
        }

        if (isset($routeParams['_scope'])) {
            $storeId = $this->storeManager->getStore($routeParams['_scope'])->getId();
        }

        if ($storeId != $this->storeManager->getStore()->getId()) {
            $routeParams['_scope_to_url'] = true;
        }

        if (!empty($requestPath)) {
            $routeParams['_direct'] = $requestPath;
        } else {
            $routePath = 'catalog/product/view';
            $routeParams['id'] = $product->getId();
            $routeParams['s'] = $product->getUrlKey();
            if ($categoryId) {
                $routeParams['category'] = $categoryId;
            }
        }

        // reset cached URL instance GET query params
        if (!isset($routeParams['_query'])) {
            $routeParams['_query'] = [];
        }

        $routeParams = $this->updateRouteParamsForAddBrandUrl($product, $routeParams);

        $url = $this->urlFactory->create()->setScope($storeId);
        return $url->getUrl($routePath, $routeParams);
    }

    /**
     * @param \Magento\Catalog\Model\Product $product
     * @param array $routeParams
     *
     * @return array
     */
    protected function updateRouteParamsForAddBrandUrl(\Magento\Catalog\Model\Product $product, array $routeParams)
    {
        if (isset($routeParams['_direct'])) {
            $productBrand = $productBrandUrl ='';
            if ($product->getMadhatBrand()) {
                $productBrand = $product->getResource()->getAttribute('madhat_brand')
                    ->getFrontend()->getValue($product);
                $productBrandUrl = \MadHat\Catalog\Model\BrandAttributeOptionsProvider::getMadhatBrandNameForUrl($productBrand);
                if ($productBrandUrl) {
                    $urlParts = explode('/', $routeParams['_direct']);
                    $length = count($urlParts);
                    if ($length > 1) {
                        $urlParts[$length] = $urlParts[$length-1];
                        $urlParts[$length-1] = $productBrandUrl;
                        $urlParts = implode('/', $urlParts);
                        $routeParams['_direct'] = $urlParts;
                    } else {
                        $routeParams['_direct'] = $productBrandUrl . '/' . $routeParams['_direct'];
                    }
                }
            }
        }

        return $routeParams;
    }
}


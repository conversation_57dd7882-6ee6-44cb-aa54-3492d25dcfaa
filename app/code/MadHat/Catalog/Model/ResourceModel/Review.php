<?php

namespace MadHat\Catalog\Model\ResourceModel;

class Review extends \MageWorx\XReviewBase\Model\ResourceModel\Review
{
    /**
     * @param \Magento\XReviewBase\Model\Review $review
     */
    public function saveReviewDetailFields(\Magento\Review\Model\Review $review): void
    {
        $fields = $this->fieldsConfig->getFieldsForReviewDetail();
        $detail = [];

        $madhatAttributes = array_filter($review->getData(), function($key) {
            return strpos($key, 'madhat_') === 0;
        }, ARRAY_FILTER_USE_KEY);

        $variantDataForJson = $this->getVariantForJsonData($madhatAttributes);
        $madhatAttributesValues = array_values($variantDataForJson);

        foreach ($fields as $field) {
            if ($field == 'variant_info' && count($madhatAttributesValues) > 0) {
                $detail['variant_info'] =  json_encode($madhatAttributesValues);
            } else {
                $detail[$field] = $review->getData($field);
            }
        }

        $connection = $this->getConnection();

        $select = $connection->select()
            ->from(
                $this->getTable('review_detail'),
                'detail_id'
            )
            ->where('review_id = :review_id');

        $detailId = $connection->fetchOne($select, [':review_id' => $review->getId()]);

        if ($detailId) {
            $condition = ["detail_id = ?" => $detailId];
            $connection->update($this->getTable('review_detail'), $detail, $condition);
        }
    }

    public function getVariantForJsonData($madhatAttributes): array
    {
        $tempData = [];
        if (is_array($madhatAttributes) && count($madhatAttributes)) {
            $tempData = $madhatAttributes;
            foreach ($madhatAttributes as $key => $val) {
                if (!$val) {
                    unset($tempData[$key]);
                }
            }
        }
        return $tempData;
    }
}

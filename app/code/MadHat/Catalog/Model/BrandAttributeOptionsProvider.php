<?php

namespace MadHat\Catalog\Model;

use Magento\Store\Model\ScopeInterface;
use Magento\Eav\Model\AttributeRepository;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Cache\Type\Collection as CollectionCache;

class BrandAttributeOptionsProvider
{
    const BRAND_ATTRIBUTE_CODE = 'madhat_brand';

    const ENTITY_TYPE = 'catalog_product';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var AttributeRepository
     */
    private $eavAttributeRepository;

    /**
     * @var CollectionCache
     */
    protected $collectionCache;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var SerializerInterface
     */
    protected $serializer;

    public function __construct(
        ScopeConfigInterface $scopeConfig,
        AttributeRepository $eavAttributeRepository,
        CollectionCache $collectionCache,
        StoreManagerInterface $storeManager,
        SerializerInterface $serializer,
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->eavAttributeRepository = $eavAttributeRepository;
        $this->collectionCache = $collectionCache;
        $this->storeManager = $storeManager;
        $this->serializer = $serializer;
    }

    /**
     * @return array
     */
    public function getMadhatBrandAttributeOptions(): array
    {
        $identifier = $this->getPreparedBrandOptionCacheKey($this->storeManager->getStore()->getId());
        $cacheData = $this->collectionCache->load($identifier);

        if ($cacheData !== false) {
            return $this->serializer->unserialize($cacheData);
        }

        /** @var \Magento\Eav\Model\Entity\Attribute $attribute */
        $attribute = $this->eavAttributeRepository->get(self::ENTITY_TYPE, self::BRAND_ATTRIBUTE_CODE);
        /* here we are using optimized way and avoid the circular dependency during PathInfoProcessor */
        $options = $attribute->getSource()->getAllOptions(false, true);

        $prepareOptions = [];
        foreach ($options as $option) {
            $label = $this->getMadhatBrandNameForUrl($option['label']);
            $prepareOptions[$label] = $option['label'];
        }

        unset($options);

        $cacheTags = [];
        $cacheTags[] = 'EAV_ATTRIBUTE' . '_' . $attribute->getId();
        $this->collectionCache->save(
            $this->serializer->serialize($prepareOptions),
            $identifier,
            $cacheTags,
            86400
        );

        return $prepareOptions;
    }

    public static function getMadhatBrandNameForUrl($name)
    {
        return strtolower(str_replace(' ', '-', trim($name)));
    }

    protected function getPreparedBrandOptionCacheKey($storeId)
    {
        return 'madhat_brand_prepare_option-store' . $storeId;
    }

    protected function getCacheTags() {
        ['EAV_ATTRIBUTE' . '_' . $this->getId()];
    }
}

<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd"
>
    <body>
        <!-- Remove itemtype="https://schema.org/Product" -->
        <attribute name="itemtype" value=""/>
        <referenceBlock name="product.info.details">
            <block
                class="MadHat\Catalog\Block\Product\View"
                name="product-technical-data"
                template="MadHat_Catalog::product/view/technical-data.phtml"
                group="detailed_info"
            >
                <arguments>
                    <argument name="title" xsi:type="string" translate="true">Technical Data</argument>
                    <argument name="sort_order" xsi:type="number">0</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="product.attributes" remove="true" />
    </body>
</page>

<?php

namespace MadHat\Catalog\Plugin;

use MadHat\Catalog\ViewModel\MadHatCatalogConfig;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Controller\Product\View;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Eav\Api\AttributeSetRepositoryInterface;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Exception\NoSuchEntityException;

class ProductViewPlugin
{
    protected $productRepository;
    protected $attributeSetRepository;
    protected $productAttributeRepository;
    protected $configurableProduct;

    /**
     * @var MadHatCatalogConfig
     */
    protected $madHatCatalogConfig;

    /**
     * Constructor
     * @param ProductRepositoryInterface|null $productRepository
     * @param AttributeSetRepositoryInterface|null $attributeSetRepository
     * @param ProductAttributeRepositoryInterface|null $productAttributeRepository
     * @param Configurable|null $configurableProduct
     */
    public function __construct(
        MadHatCatalogConfig $madHatCatalogConfig,
        ?ProductRepositoryInterface $productRepository = null,
        ?AttributeSetRepositoryInterface $attributeSetRepository = null,
        ?ProductAttributeRepositoryInterface $productAttributeRepository = null,
        ?Configurable $configurableProduct = null
    ) {
        $this->productRepository = $productRepository ?: ObjectManager::getInstance()
            ->get(ProductRepositoryInterface::class);
        $this->attributeSetRepository = $attributeSetRepository ?: ObjectManager::getInstance()
            ->get(AttributeSetRepositoryInterface::class);
        $this->productAttributeRepository = $productAttributeRepository ?: ObjectManager::getInstance()
            ->get(ProductAttributeRepositoryInterface::class);
        $this->configurableProduct = $configurableProduct ?: ObjectManager::getInstance()
            ->get(Configurable::class);
        $this->madHatCatalogConfig = $madHatCatalogConfig;

    }

    /**
     * Before plugin for the execute method of Product View Controller
     *
     * @param View $subject
     * @return array
     */
    public function beforeExecute(View $subject)
    {
        /****** Custom Start ********/
        $productId = (int)$subject->getRequest()->getParam('id');
        $subject->getRequest()->setParam('request_child_product_id', $productId);
        // $attributeSetJson = '{"Laser Engraver & Cutter":["madhat_x_tool_bundle","madhat_x_tool_ra"],
        // "Printing Material":["madhat_color","madhat_weight","madhat_filament_size"],
        // "Accessories Upgrades":["madhat_voltage","madhat_e3d_temperature_sensor","madhat_e3d_hotside_kit"],
        // "Accessories Upgrades (Nozzle Diameter)":["madhat_nozzle_diameter"]}';
        $attributeSetDetails = $this->madHatCatalogConfig->getPdpProductAttributeSetMapping();
        try {
            $product = $this->productRepository->getById($productId);
            $productType = $product->getTypeId();
            if ($productType == 'simple') {
                $parentIds = $this->configurableProduct->getParentIdsByChild($productId);
                $parentId = array_shift($parentIds);
                if ($parentId) {
                    $attrParams = $this->getParamsDataForPreselect($product, $attributeSetDetails);
                    if ($attrParams && isset($attrParams)) {
                        $this->setParamsDataForPreselect($subject, $attrParams, $parentId);
                    }
                }
            }
        } catch (NoSuchEntityException $e) {
            echo "Product with SKU does not exist.\n";
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }
        /****** Custom END ********/

        return [];
    }

    /**
     * @throws NoSuchEntityException
     */
    public function getParamsDataForPreselect($product, $attributeSetDetails): bool|array
    {
        try {
            $attributeSetId = $product->getAttributeSetId();
            $attributeSet = $this->attributeSetRepository->get($attributeSetId);
            $attributeSetName = $attributeSet->getAttributeSetName();

            if (isset($attributeSetDetails[$attributeSetName])) {
                $attributeData = $attributeSetDetails[$attributeSetName];
                $attrParams = [];
                foreach ($attributeData as $key => $attribute) {
                    $optionId = $product->getData($attribute);
                    $attributeObj = $this->productAttributeRepository->get($attribute);
                    $id = $attributeObj->getAttributeId();
                    $attrParams[$id] = $optionId;
                }
                return $attrParams;
            }
        } catch (NoSuchEntityException $e) {
            echo "Product with SKU does not exist.\n";
            echo $e->getMessage();
        } catch (\Exception $e) {
            echo $e->getMessage();
        }

        return false;
    }

    public function setParamsDataForPreselect($subject, $attributeOptionsAsParams, $parentId): bool
    {
        if ($parentId) {
            $subject->getRequest()->setParam('id', $parentId);
        }
        $subject->getRequest()->setParam('pre_select_custom', $attributeOptionsAsParams);
        return true;
    }
}

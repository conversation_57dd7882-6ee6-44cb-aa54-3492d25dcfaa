<?php


namespace MadHat\Catalog\Plugin\Hyva\BssSimpledetailconfigurable;

use Bss\Simpledetailconfigurable\Helper\ModuleConfig;
use Bss\Simpledetailconfigurable\Helper\ProductData;
use Magento\Catalog\Model\ResourceModel\Product;
use Magento\Catalog\Model\ProductFactory;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use \Magento\Framework\App\Request\Http;
use Magento\Framework\Currency\Data\Currency as CurrencyData;
use Magento\Directory\Model\Currency;
use MadHat\InventoryImport\Model\Source\ProductStatus;

class UpdateCustomUrl
{
    /**
     * Simple detail module config
     *
     * @var ModuleConfig
     */
    protected ModuleConfig $moduleConfig;

    /**
     * Product resource
     *
     * @var Product
     */
    protected Product $productResource;

    /**
     * Product factory
     *
     * @var ProductFactory
     */
    protected ProductFactory $productLoader;

    /**
     * Configurable resource
     *
     * @var Configurable
     */
    protected Configurable $configurableResource;

    /**
     * @var PriceCurrencyInterface
     */
    protected $priceCurrency;

    /**
     *
     * @var string
     */
    protected $priceCurrencyCode;

    /**
     * array
     */
    protected $configureAttributeCodes = [];

    /**
     * @var Http
     */
    protected $httpRequest;

    /**
     * @var Currency
     */
    public $currency;

    /**
     * @param ModuleConfig $moduleConfig
     * @param Product $productResource
     * @param ProductFactory $productLoader
     * @param Configurable $configurableResource
     * @param PriceCurrencyInterface $priceCurrency
     * @param Http $request
     * @param Currency $currency
     */
    public function __construct(
        ModuleConfig           $moduleConfig,
        Product                $productResource,
        ProductFactory         $productLoader,
        Configurable           $configurableResource,
        PriceCurrencyInterface $priceCurrency,
        Http                   $request,
        Currency               $currency
    )
    {
        $this->moduleConfig = $moduleConfig;
        $this->productResource = $productResource;
        $this->productLoader = $productLoader;
        $this->configurableResource = $configurableResource;
        $this->priceCurrency = $priceCurrency;
        $this->httpRequest = $request;
        $this->currency = $currency;
    }

    /**
     * Plugin after get detail data
     *
     * @param mixed $subject
     * @param string $result
     * @return string
     * @throws NoSuchEntityException
     */
    public function afterGetDetailData(ProductData $subject, $result, $product)
    {
        if (!$this->moduleConfig->customUrl()) {
            return $result;
        }

        //$result['custom_url'] = $this->processCustomUrl($product);
        $result['custom_url'] = $product->getUrlKey();
        $result['madhat_brand_url_key'] = '';
        $madhatBrandValue = '';
        if ($product->getMadhatBrand()) {
            $madhatBrandValue = $product->getAttributeText('madhat_brand');
            $result['madhat_brand_url_key'] = \MadHat\Catalog\Model\BrandAttributeOptionsProvider::getMadhatBrandNameForUrl($madhatBrandValue);
            $result['custom_url'] = $result['madhat_brand_url_key'] . '/' . $product->getUrlKey();
        }

        $result['madhat_brand_value'] = $madhatBrandValue;
        $result['madhat_ean'] = $product->getMadhatEan() ?? '';
        $result['madhat_manufacturer_number'] = $product->getMadhatManufacturerNumber() ?? '';

        // rich Content start
        $canConfigure = $product->canConfigure();
        if ($canConfigure) {
            $variesBy = [];
            $configurableAttributes = $product->getData('_cache_instance_configurable_attributes');
            if ($configurableAttributes === null) {
                $configurableAttributes = [];
            }
            foreach ($configurableAttributes as $configurableAttribute) {
                if (strtolower($configurableAttribute->getLabel()) == 'filament size') {
                    $variesBy[] = 'https://schema.org/' . 'size';
                } else {
                    $variesBy[] = 'https://schema.org/' . strtolower($configurableAttribute->getLabel());
                }

                $productAttribute = $configurableAttribute->getProductAttribute();
                $this->configureAttributeCodes[] = [
                    'label' => $configurableAttribute->getLabel(),
                    'value' => $productAttribute->getAttributeCode()
                ];
            }
            $richContent = [
                'productGroupID' => $product->getSku(),
                "variesBy" => $variesBy,
            ];
            $result['varies_by'] = $variesBy;
        } else {
            $productUrl = $product->getProductUrl();
            $productName = $product->getName();
            $additionalRichContent = [];
            if (!empty($this->configureAttributeCodes)) {
                $availableCode = [];
                foreach ($this->configureAttributeCodes as $row) {
                    $availableCode[] = $row['value'];
                }
                $attributes = $product->getAttributes();
                if (!empty($attributes) && !empty($availableCode)) {
                    foreach ($attributes as $a) {
                        if (in_array($a->getAttributeCode(), $availableCode)) {

                            $rKey = strtolower(trim($a->getStoreLabel()));
                            if ($rKey == 'filament size') {
                                $rKey = 'size';
                            }
                            $additionalRichContent[$rKey] = $product->getAttributeText($a->getAttributeCode());

                            $productName .= '-' . $product->getAttributeText($a->getAttributeCode());
                        }
                    }
                }
            }

            $price = $this->currency->format(
                $this->priceCurrency->format(
                    $product->getPriceInfo()->getPrice('final_price')->getAmount()->getValue(), false
                ),
                ['display' => CurrencyData::NO_SYMBOL],
                false
            );

            $price = (float) str_replace(',', '', $price);

            $richContent = [
                "@type" => "Product",
                "sku" => $product->getSku(),
                "gtin" => $product->getMadhatEan(),
                "image" => isset($result['image'][0]) ? $result['image'][0]['img'] : '',
                "name" => $productName,
                "offers" => [
                    "@type" => "Offer",
                    "url" => $productUrl,
                    "priceCurrency" => $this->getPriceCurrency(),
                    "price" => $price,
                    "availability" => ($result['is_in_stock']) ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
                ]
            ];
            $richContent = array_merge($richContent, $additionalRichContent);
        }

        $result['rich_content'] = $richContent;
        // rich Content end

        return $result;
    }

    protected function getPriceCurrency()
    {
        if ($this->priceCurrencyCode === null) {
            $this->priceCurrencyCode = $this->priceCurrency->getCurrency()->getCurrencyCode();
        }

        return $this->priceCurrencyCode;
    }

    /**
     * After plugin for GetAllData method.
     *
     * @param ProductData $subject
     * @param $result
     * @param string|\Magento\Catalog\Model\Product $child
     * @return array|bool
     */
    public function afterGetChildDetail(ProductData $subject, $result, string|\Magento\Catalog\Model\Product $child): bool|array
    {
        if (is_bool($result)) {
            return $result;
        }

        $product = $this->productLoader->create()->load($result['entity']);
        $result['stock_status'] = $product->getIsSalable();

        $inventoryStatus = (string)$product->getAttributeText('madhat_inventory_status');
        $inventoryStatusFlag = 0;

        if (in_array($inventoryStatus, [ProductStatus::LABEL_IN_STOCK]) && $product->getIsSalable()) {
            $inventoryStatusFlag = 1;
        } elseif (in_array($inventoryStatus, [ProductStatus::LABEL_IN_STOCK]) && !$product->getIsSalable()) {
            $inventoryStatusFlag = 0;
            $inventoryStatus = ProductStatus::LABEL_OUT_OF_STOCK;
        } elseif (in_array(
            $inventoryStatus, [
            ProductStatus::LABEL_AVAILABLE_ON_DEMAND,
            ProductStatus::LABEL_AVAILABLE_ON_PREORDER,
            ProductStatus::LABEL_ETA_2_DAYS,
            ProductStatus::LABEL_ETA_3_DAYS,
            ProductStatus::LABEL_ETA_7_DAYS,
            ProductStatus::LABEL_ETA_14_DAYS,
            ProductStatus::LABEL_ETA_30_DAYS
        ])) {
            $inventoryStatusFlag = 2;
        }

        $result['inventory_status'] = $inventoryStatus;
        $result['inventory_status_flag'] = $inventoryStatusFlag;

        return $result;
    }

}

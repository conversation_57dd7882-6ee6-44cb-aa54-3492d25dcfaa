<?php

namespace MadHat\Catalog\Plugin;

use MageWorx\XReviewBase\Model\Review\AdditionalDetailsFields\Config;

class ConfigAfterPlugin
{
    /**
     * @param Config $subject
     * @param array $result
     *
     * @return array
     */
    public function afterGetFieldsForReviewDetail(Config $subject, $result): array
    {
        return [
            'location',
            'region',
            'answer',
            'is_recommend',
            'is_verified',
            'pros',
            'cons',
            'variant_info',
            'variant_sku'
        ];
    }
}

<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="default">
        <job name="madhat_siteintegrationorder_exportOrders" instance="MadHat\SiteIntegrationOrder\Cron\ExportOrders" method="execute">
            <schedule>*/15 * * * *</schedule>
        </job>
        <job name="madhat_siteintegrationorder_syncOrders" instance="MadHat\SiteIntegrationOrder\Cron\SyncOrders" method="execute">
            <schedule>*/15 * * * *</schedule>
        </job>
        <job name="madhat_siteintegrationorder_capturePayment" instance="MadHat\SiteIntegrationOrder\Cron\CapturePayment" method="execute">
            <schedule>*/15 * * * *</schedule>
        </job>
        <job name="madhat_siteintegrationorder_updatePaymentForOrders" instance="MadHat\SiteIntegrationOrder\Cron\UpdatePaymentForOrders" method="execute">
            <schedule>*/15 * * * *</schedule>
        </job>
        <job name="madhat_siteintegrationorder_updateFcDataForOrders" instance="MadHat\SiteIntegrationOrder\Cron\UpdateFcDataForOrders" method="execute">
            <schedule>*/15 * * * *</schedule>
        </job>
        <job name="madhat_siteintegrationorder_notifySiteExportReport" instance="MadHat\SiteIntegrationOrder\Cron\NotifySiteExportReport" method="execute">
            <schedule>*/60 * * * *</schedule>
        </job>
    </group>
</config>

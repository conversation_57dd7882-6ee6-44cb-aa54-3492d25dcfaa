<?php

namespace MadHat\SiteIntegrationOrder\Block\Adminhtml\Sales\Order\Totals;

use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Template;
use Magento\Sales\Model\Order;
use Magento\Tax\Model\Config;

class Round extends \Magento\Framework\View\Element\Template
{
    const CODE = 'madhat_site_integration_order_totals_round';
    const LABLE = 'Round';

    /**
     * @param MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository
     * @param Config $taxConfig
     * @param Template\Context $context
     * @param array $data
     */
    public function __construct(
        MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository,
        Config $taxConfig,
        Template\Context $context,
        array $data = []
    )
    {
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->_config = $taxConfig;
        parent::__construct($context, $data);
    }

    /**
     * Check if we nedd display full tax total info
     *
     * @return bool
     */
    public function displayFullSummary(): bool
    {
        return true;
    }

    /**
     * Get data (totals) source model
     *
     * @return \Magento\Framework\DataObject
     */
    public function getSource()
    {
        return $this->_source;
    }

    public function getStore()
    {
        return $this->_order->getStore();
    }

    /**
     * @return Order
     */
    public function getOrder(): Order
    {
        return $this->_order;
    }

    /**
     * Initialize all order totals relates with tax
     *
     * @return Round
     * @throws NoSuchEntityException
     */
    public function initTotals(): static
    {
        try {
            $parent = $this->getParentBlock();
            $this->_order = $parent->getOrder();
            $this->_source = $parent->getSource();
            $value = (float)$this->madhatOrderInfoRepository->getByOrderId($this->_order->getId())->getSiteRound();
            if ($value == 0) { // Do not display if ROUND is 0
                return $this;
            }
            $round = new \Magento\Framework\DataObject(
                [
                    'code' => self::CODE,
                    'strong' => true,
                    'value' => $value,
                    'label' => __(self::LABLE),
                ]
            );
            $parent->addTotal($round, self::CODE);
        } catch (NoSuchEntityException $e) {
            $this->_logger->debug($e->getMessage());
        }

        return $this;
    }

    public function getValue()
    {
    }
}

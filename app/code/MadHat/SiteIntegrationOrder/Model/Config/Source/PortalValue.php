<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Model\Config\Source;

use Magento\Framework\Option\ArrayInterface;

class PortalValue implements ArrayInterface
{
    protected const TECHOUTLET = 'Item302';
    protected const PRIMA3D = 'Item300';

    /**
     * @inheritDoc
     */
    public function toOptionArray(): array
    {
        return [
            [
                'value' => self::PRIMA3D,
                'label' => __('3DPrima')
            ],
            [
                'value' => self::TECHOUTLET,
                'label' => __('TechOutlet')
            ]
        ];
    }
}

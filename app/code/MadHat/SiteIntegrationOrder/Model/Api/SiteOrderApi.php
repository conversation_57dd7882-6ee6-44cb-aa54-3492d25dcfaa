<?php
/**
 * DEPRICATED
 * All function split and moved to app/code/MadHat/SiteIntegrationCore/Model/Api/SiteOrderApi
 * If change something please search for same functions in MadHat/SiteIntegrationCore/Model/Api/SiteOrderApi
 */
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Model\Api;

use Adyen\Payment\Model\ResourceModel\Notification\CollectionFactory as AdyenNotificationCollectionFactory;
use Exception;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\OrderIntegration\Api\MadhatCustomerInfoRepositoryInterface;
use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use MadHat\OrderIntegration\Model\MadhatCustomerInfoFactory;
use MadHat\OrderIntegration\Model\MadhatOrderInfoFactory;
use MadHat\OrderIntegration\Model\OrderGridRefresher;
use MadHat\OrderIntegration\Model\ResourceModel\MadhatOrderInfo\CollectionFactory as MadhatOrderInfoCollectionFactory;
use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;
use MadHat\SiteIntegrationCore\Logger\Logger as SiteCoreLogger;
use MadHat\SiteIntegrationCore\Model\Api\SiteApi;
use MadHat\SiteIntegrationOrder\Helper\Data as SiteOrderHelper;
use MadHat\SiteIntegrationOrder\Logger\Logger as SiteOrderLogger;
use MadHat\SiteIntegrationOrder\Model\Config\Source\CustomerType;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableType;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Directory\Model\CurrencyFactory;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteria;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\QuoteFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Sales\Api\OrderManagementInterface;
use Magento\Sales\Api\OrderStatusHistoryRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Item;
use Magento\Sales\Model\OrderFactory;
use Magento\Sales\Model\OrderRepository;
use Magento\Sales\Model\ResourceModel\Order\Collection;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as MagentoOrderCollectionFactory;
use Magento\SalesRule\Api\RuleRepositoryInterface;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;
use Mirasvit\OrderEditor\Section\ItemsSection;
use Psr\Log\LoggerInterface;

class SiteOrderApi extends SiteApi
{
    /**
     * Cancellation reason
     */
    const CANCEL_REASON = 'Direct DB order cancellation due to Visma integration data (cancelled in Visma)';
    protected const SHIPPING_PRODUCT_NO = '8b';

    protected const WEBSITE_NAME_TECHOUTLET_UK = 'TechnologyOutlet';

    protected const WEBSITE_NAME_PPN = '3DPrima';
    protected const SHIPPING_METHOD_ROYAL_MAIL = 'Royal Mail';

    protected const SHIPPING_METHOD_DPD_2DAY_SERVICE = 'DPD - 2 Day service';

    protected const SHIPPING_METHOD_DPD_NEXT_DAY_SERVICE = 'DPD - Next Day';

    protected const SHIPPING_METHOD_FREE_SHIPPING = 'Free - Free';

    protected const PAYMENT_METHOD_STRIPE_PAYMENTS = 'stripe_payments';

    protected const PAYMENT_METHOD_ADYEN_KLARNA_PAYMENTS = 'adyen_klarna';

    protected const PAYMENT_METHOD_ADYEN_CLEARPAY_PAYMENTS = 'adyen_clearpay';

    protected const PAYMENT_METHOD_ADYEN_PAYPAL_PAYMENTS = 'adyen_paypal';

    protected const PAYMENT_METHOD_ADYEN_CC_PAYMENTS = 'adyen_cc';

    protected const DISCOUNT_PRODUCT_NO = 'RABATT20';

    protected const INFO_PRODUCT_NO = 'INFO20';

    protected const PICKED_STATUS = 'Picked';

    protected const INVOICED_STATUS = 'Invoiced';

    protected const CANCELED_STATUS = 'Cancelled';

    protected array $shippingMethodsArray = [];

    protected array $paymentMethodsArray = [];

    protected array $vatMappingArray = [];

    /**
     * @var AdyenNotificationCollectionFactory
     */
    protected AdyenNotificationCollectionFactory $adyenNotificationCollectionFactory;

    /**
     * @var DbLoggerSaver
     */
    protected DbLoggerSaver $dbLoggerSaver;

    /**
     * @var MadhatCustomerInfoRepositoryInterface
     */
    protected MadhatCustomerInfoRepositoryInterface $madhatCustomerInfoRepository;

    /**
     * @var MadhatCustomerInfoFactory
     */
    protected MadhatCustomerInfoFactory $madhatCustomerInfoFactory;

    /**
     * @var SiteCoreHelper
     */
    protected SiteCoreHelper $siteCoreHelper;

    /**
     * @var SiteCoreLogger
     */
    protected SiteCoreLogger $siteCoreLogger;

    /**
     * @var MadhatOrderInfoRepositoryInterface
     */
    protected MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository;

    /**
     * @var OrderGridRefresher
     */
    protected OrderGridRefresher $orderGridRefresher;

    /**
     * @var SiteOrderHelper
     */
    protected SiteOrderHelper $siteOrderHelper;

    /**
     * @var SiteOrderLogger
     */
    protected SiteOrderLogger $siteOrderLogger;

    /**
     * @var FilterBuilder
     */
    protected FilterBuilder $filterBuilder;

    /**
     * @var MadhatOrderInfoFactory
     */
    protected MadhatOrderInfoFactory $madhatOrderInfoFactory;

    /**
     * @var MadhatOrderInfoCollectionFactory
     */
    protected MadhatOrderInfoCollectionFactory $madhatOrderInfoCollectionFactory;

    /**
     * @var ProductRepositoryInterface
     */
    protected ProductRepositoryInterface $productRepository;

    /**
     * @var StockRegistryInterface
     */
    protected StockRegistryInterface $stockRegistry;

    /**
     * @var AddressRepositoryInterface
     */
    protected AddressRepositoryInterface $addressRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    protected CustomerRepositoryInterface $customerRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    protected SearchCriteriaBuilder $searchCriteriaBuilder;

    /**
     * @var Curl
     */
    protected Curl $curl;

    /**
     * @var OrderFactory
     */
    protected OrderFactory $magentoOrderFactory;

    /**
     * @var OrderRepository
     */
    protected OrderRepository $magentoOrderRepository;

    /**
     * @var MagentoOrderCollectionFactory
     */
    protected MagentoOrderCollectionFactory $magentoOrderCollectionFactory;

    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @var Json
     */
    protected Json $json;

    /**
     * @var Emulation
     */
    protected Emulation $emulation;

    /**
     * @var ItemsSection
     */
    protected ItemsSection $itemsSection;

    /**
     * @var OrderManagementInterface
     */
    protected OrderManagementInterface $orderManagement;

    /**
     * @var QuoteFactory
     */
    protected QuoteFactory $quoteFactory;

    /**
     * @var ManagerInterface
     */
    protected ManagerInterface $eventManager;

    /**
     * @var OrderStatusHistoryRepositoryInterface
     */
    private OrderStatusHistoryRepositoryInterface $orderStatusHistoryRepository;

    /**
     * @var SortOrderBuilder
     */
    private SortOrderBuilder $sortOrderBuilder;
    private LoggerInterface $logger;
    private ResourceConnection $resourceConnection;
    private CurrencyFactory $currencyFactory;
    private RuleRepositoryInterface $ruleRepository;
    private ConfigurableType $configurableType;
    private ScopeConfigInterface $scopeConfig;
    private CartRepositoryInterface $cartRepositoryInterface;

    /**
     * @param CartRepositoryInterface $cartRepositoryInterface
     * @param AdyenNotificationCollectionFactory $adyenNotificationCollectionFactory
     * @param ConfigurableType $configurableType
     * @param RuleRepositoryInterface $ruleRepository
     * @param CurrencyFactory $currencyFactory
     * @param ResourceConnection $resourceConnection
     * @param SortOrderBuilder $sortOrderBuilder
     * @param OrderStatusHistoryRepositoryInterface $orderStatusHistoryRepository
     * @param DbLoggerSaver $dbLoggerSaver
     * @param MadhatCustomerInfoRepositoryInterface $madhatCustomerInfoRepository
     * @param MadhatCustomerInfoFactory $madhatCustomerInfoFactory
     * @param SiteCoreHelper $siteCoreHelper
     * @param SiteCoreLogger $siteCoreLogger
     * @param MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository
     * @param OrderGridRefresher $orderGridRefresher
     * @param SiteOrderHelper $siteOrderHelper
     * @param SiteOrderLogger $siteOrderLogger
     * @param FilterBuilder $filterBuilder
     * @param MadhatOrderInfoFactory $madhatOrderInfoFactory
     * @param MadhatOrderInfoCollectionFactory $madhatOrderInfoCollectionFactory
     * @param ProductRepositoryInterface $productRepository
     * @param StockRegistryInterface $stockRegistry
     * @param AddressRepositoryInterface $addressRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param Curl $curl
     * @param OrderManagementInterface $orderManagement
     * @param OrderFactory $magentoOrderFactory
     * @param OrderRepository $magentoOrderRepository
     * @param MagentoOrderCollectionFactory $magentoOrderCollectionFactory
     * @param Emulation $emulation
     * @param StoreManagerInterface $storeManager
     * @param Json $json
     * @param ItemsSection $itemsSection
     * @param QuoteFactory $quoteFactory
     * @param ManagerInterface $eventManager
     * @param LoggerInterface $logger
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        cartRepositoryInterface               $cartRepositoryInterface,
        AdyenNotificationCollectionFactory    $adyenNotificationCollectionFactory,
        ConfigurableType                      $configurableType,
        RuleRepositoryInterface               $ruleRepository,
        CurrencyFactory                       $currencyFactory,
        ResourceConnection                    $resourceConnection,
        SortOrderBuilder                      $sortOrderBuilder,
        OrderStatusHistoryRepositoryInterface $orderStatusHistoryRepository,
        DbLoggerSaver                         $dbLoggerSaver,
        MadhatCustomerInfoRepositoryInterface $madhatCustomerInfoRepository,
        MadhatCustomerInfoFactory             $madhatCustomerInfoFactory,
        SiteCoreHelper                        $siteCoreHelper,
        SiteCoreLogger                        $siteCoreLogger,
        MadhatOrderInfoRepositoryInterface    $madhatOrderInfoRepository,
        OrderGridRefresher                    $orderGridRefresher,
        SiteOrderHelper                       $siteOrderHelper,
        SiteOrderLogger                       $siteOrderLogger,
        FilterBuilder                         $filterBuilder,
        MadhatOrderInfoFactory                $madhatOrderInfoFactory,
        MadhatOrderInfoCollectionFactory      $madhatOrderInfoCollectionFactory,
        ProductRepositoryInterface            $productRepository,
        StockRegistryInterface                $stockRegistry,
        AddressRepositoryInterface            $addressRepository,
        CustomerRepositoryInterface           $customerRepository,
        SearchCriteriaBuilder                 $searchCriteriaBuilder,
        Curl                                  $curl,
        OrderManagementInterface              $orderManagement,
        OrderFactory                          $magentoOrderFactory,
        OrderRepository                       $magentoOrderRepository,
        MagentoOrderCollectionFactory         $magentoOrderCollectionFactory,
        Emulation                             $emulation,
        StoreManagerInterface                 $storeManager,
        Json                                  $json,
        ItemsSection                          $itemsSection,
        QuoteFactory                          $quoteFactory,
        ManagerInterface                      $eventManager,
        LoggerInterface                       $logger,
        ScopeConfigInterface                  $scopeConfig
    ) {
        parent::__construct(
            $siteCoreHelper,
            $siteCoreLogger,
            $curl,
            $storeManager,
            $json
        );
        $this->cartRepositoryInterface = $cartRepositoryInterface;
        $this->adyenNotificationCollectionFactory = $adyenNotificationCollectionFactory;
        $this->configurableType = $configurableType;
        $this->ruleRepository = $ruleRepository;
        $this->currencyFactory = $currencyFactory;
        $this->resourceConnection = $resourceConnection;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->orderStatusHistoryRepository = $orderStatusHistoryRepository;
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->madhatCustomerInfoRepository = $madhatCustomerInfoRepository;
        $this->madhatCustomerInfoFactory = $madhatCustomerInfoFactory;
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->orderGridRefresher = $orderGridRefresher;
        $this->siteOrderHelper = $siteOrderHelper;
        $this->siteOrderLogger = $siteOrderLogger;
        $this->filterBuilder = $filterBuilder;
        $this->madhatOrderInfoFactory = $madhatOrderInfoFactory;
        $this->madhatOrderInfoCollectionFactory = $madhatOrderInfoCollectionFactory;
        $this->productRepository = $productRepository;
        $this->stockRegistry = $stockRegistry;
        $this->addressRepository = $addressRepository;
        $this->customerRepository = $customerRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->orderManagement = $orderManagement;
        $this->magentoOrderFactory = $magentoOrderFactory;
        $this->magentoOrderRepository = $magentoOrderRepository;
        $this->magentoOrderCollectionFactory = $magentoOrderCollectionFactory;
        $this->emulation = $emulation;
        $this->itemsSection = $itemsSection;
        $this->quoteFactory = $quoteFactory;
        $this->eventManager = $eventManager;
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Export Magento Orders to SITE/ERP
     *
     * @param array $orderIncrementIds
     * @return array
     * @throws CouldNotSaveException
     */
    public function exportSiteOrders(array $orderIncrementIds = []): array
    {
        $orderCollection = $this->filterOrderCollectionForSiteOrderExport($orderIncrementIds);

        $order = $orderCollection->getFirstItem();
        $websiteId = $order->getStore()->getWebsiteId();

        $shippingMethodsMapping = $this->siteOrderHelper->getShippingMethodsMapping($websiteId);
        $this->shippingMethodsArray = $this->json->unserialize($shippingMethodsMapping);

        $paymentMethodsMapping = $this->siteOrderHelper->getPaymentMethodsMapping($websiteId);
        $this->paymentMethodsArray = $this->json->unserialize($paymentMethodsMapping);

        $this->vatMappingArray = $this->siteOrderHelper->getOrderVatMapping($websiteId);

        $result['success'] = false;
        $result['total_orders'] = $orderCollection->getSize();
        $result['success_orders'] = 0;
        $result['failed_orders'] = 0;
        $result['error_messages'] = [];

        if ($orderCollection->getSize()) {
            $this->siteOrderLogger->info(
                __(
                    'SITE Orders export started for {%1}.',
                    implode(', ', $orderCollection->getColumnValues('entity_id'))
                )
            );

            $orderIds = $orderCollection->getColumnValues('entity_id');
            $orderCollection = $this->magentoOrderCollectionFactory->create();
            $orderCollection->addFieldToFilter('entity_id', ['in' => $orderIds]);

            foreach ($orderCollection as $order) {
                try {
                    $siteOrderParams = $this->createSiteOrderParams($order);
                    $this->logger->debug(__('%1, %2 Site Order Params: %3', __LINE__, __CLASS__, var_export($siteOrderParams, true)));
                    $response = $this->orderCustomerEnhancedMessage($siteOrderParams);

                    if (isset($response['ResponseResult']) && $response['ResponseResult']['Code'] == 'SUCCESS') {
                        $this->saveSiteOrderId($order, $response);
                        $result['success_orders']++;
                    } else {
                        $errorMessage = $response['ResponseResult']['Description'];
                        if (isset($response['ResponseResult']['SystemMessage'])) {
                            $errorMessage = $response['ResponseResult']['SystemMessage'];
                        }
                        $result['error_messages'][] = [
                            'Order Id' => $order->getEntityId(),
                            'Order Increment Id' => $order->getIncrementId(),
                            'Error Message' => $errorMessage
                        ];
                        $this->siteOrderLogger->error(
                            __(
                                "Order ID : %1[%2] failed to export in SITE. Error Response : %3",
                                $order->getEntityId(),
                                $order->getIncrementId(),
                                $this->json->serialize($response)
                            )
                        );
                        $result['failed_orders']++;
                        $this->logFailOrderExport($order);
                    }
                } catch (Exception $e) {
                    $result['error_messages'][] = [
                        'Order Id' => $order->getEntityId(),
                        'Order Increment Id' => $order->getIncrementId(),
                        'Error Message' => $e->getMessage()
                    ];
                    $this->siteOrderLogger->error(
                        __(
                            "Order Id : %1[%2] => Exception : %3",
                            $order->getEntityId(),
                            $order->getIncrementId(),
                            $e->getMessage()
                        )
                    );
                    $result['failed_orders']++;
                    $this->logFailOrderExport($order);
                }
            }
            $result['message'] = __(
                '%1 out of %2 order\'s exported to SITE.',
                $result['success_orders'],
                $result['total_orders']
            );
        } else {
            $result['message'] = __(
                'No orders were found to export.'
            );
        }

        if ($result['success_orders'] > 0) {
            $result['success'] = true;
        }

        if ($result['total_orders'] > 0) {
            $this->logReportToDbLogger($result, 'SITE Order Export Report');
        }

        return $result;
    }

    /**
     * Filter Order Collection for Site Order Export Process
     *
     * @param array $orderIncrementIds
     * @return Collection
     */
    protected function filterOrderCollectionForSiteOrderExport(array $orderIncrementIds = []): Collection
    {
        $orderCollection = $this->magentoOrderCollectionFactory->create();
        $maxAttempts = $this->siteOrderHelper->getExportMaxAttempts('1');
        $allowedOfflinePaymentMethods = $this->siteOrderHelper->getAllowedOfflinePaymentMethodsFromConfig('1');

        $orderCollection->addFieldToSelect(['increment_id', 'status'])
            ->getSelect()
            ->join(
                ['sop' => $orderCollection->getTable('sales_order_payment')], // Join sales_order_payment table
                'main_table.entity_id = sop.parent_id',
                ['cc_trans_id', 'method'] // Added 'method' to select payment method
            )
            ->joinLeft(
                ['an' => $orderCollection->getTable('adyen_notification')], // Join adyen_notification table
                'an.pspreference = sop.cc_trans_id AND an.merchant_reference = main_table.increment_id',
                ['event_code', 'success', 'done']
            )
            ->joinLeft(
                ['moi' => $orderCollection->getTable('madhat_order_info')],
                'moi.order_id = main_table.entity_id',
                ['site_order_id', 'site_order_export_attempts']
            );

        $orderCollection->addFieldToFilter('status', ['nin' => ['canceled']])
            ->addFieldToFilter('moi.site_order_id', ['null' => true])
            ->addFieldToFilter(
                ['moi.site_order_export_attempts', 'moi.site_order_export_attempts'],
                [
                    ['null' => true], ['lt' => $maxAttempts]
                ]
            );

        // Add conditional logic for strict Adyen checks and allowed offline payment methods
        $connection = $orderCollection->getConnection();
        $select = $orderCollection->getSelect();

        $select->where(
            '(' .
            $connection->quoteInto('sop.method IN (?)', $allowedOfflinePaymentMethods) .
            ' OR (' .
            'sop.cc_trans_id IS NOT NULL AND ' .
            $connection->quoteInto('an.event_code = ?', 'AUTHORISATION') . ' AND ' .
            $connection->quoteInto('an.success = ?', 'true') . ' AND ' .
            $connection->quoteInto('an.done = ?', '1') .
            ')' .
            ')'
        );

        if (!empty($orderIncrementIds)) {
            $orderCollection->addFieldToFilter('increment_id', ['in' => $orderIncrementIds]);
        }

        // To log site order export sql query (dev debug)
        // Need to test issues on stage01, need to be removed before prod.
        $this->siteOrderLogger->info(
            __(
                'SITE Orders export sql query [%1].',
                $orderCollection->getSelect()->__toString()
            )
        );

        return $orderCollection;
    }

    /**
     * Sync Magento Orders as "Picked|Invoiced" as per SITE/ERP status
     *
     * @param array $orderIncrementIds
     * @return array
     */
    public function syncSiteOrders(array $orderIncrementIds = []): array
    {
        $orderCollection = $this->filterOrderCollectionForSiteOrderSync($orderIncrementIds);

        $result['success'] = false;
        $result['total_orders'] = $orderCollection->getSize();
        $result['success_orders'] = 0;
        $result['failed_orders'] = 0;
        $result['error_messages'] = [];

        if ($orderCollection->getSize()) {
            foreach ($orderCollection as $order) {
                $magentoOrderId = (int)$order->getEntityId();
                $magentoOrder = $this->magentoOrderFactory->create()->load($magentoOrderId);
                try {
                    $siteOrderParams = ['WebOrderNo' => $order->getEntityId()];
                    $response = $this->orderstatus($siteOrderParams);
                    if (isset($response['ResponseResult']) && $response['ResponseResult']['Code'] == 'SUCCESS') {
                        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId($magentoOrderId);
                        if (isset($response['OrderStatusData']['Status'])) {
                            if ($response['OrderStatusData']['Status'] == self::PICKED_STATUS) {
                                if (!$madhatOrderInfo->getSiteIsPicked()) {
                                    $this->editOrderItems($magentoOrderId, $response['OrderStatusData']);
                                    $this->editOrderTotals($magentoOrderId, $response['OrderStatusData']);
                                    $this->savePickedStatus($magentoOrderId, $response);
                                    $this->eventManager->dispatch(
                                        'madhat_order_sync_to_erp',
                                        ['order_id' => $magentoOrderId, 'response' => $response]
                                    );
                                    // Save FulfilmentWorkflow value if present in response
                                    if (isset($response['OrderStatusData']['FulfilmentWorkflow'])) {
                                        if ($response['OrderStatusData']['FulfilmentWorkflow'] === 'Default') {
                                            // Get default fulfilment workflow from store configuration
                                            $defaultFulfilmentWorkflow = $this->scopeConfig->getValue(
                                                'site_order/fulfillment_crowd/default_fulfilment_workflow',
                                                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
                                            );
                                            $madhatOrderInfo->setFulfilmentWorkflow($defaultFulfilmentWorkflow);
                                        } else {
                                            $madhatOrderInfo->setFulfilmentWorkflow($response['OrderStatusData']['FulfilmentWorkflow']);
                                        }
                                        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
                                    }
                                }
                                $result['success_orders']++;
                            }
                            if ($response['OrderStatusData']['Status'] == self::INVOICED_STATUS) {
                                if (!$madhatOrderInfo->getSiteIsInvoiced() && !empty($response['OrderStatusData']['Billings'])) {
                                    $this->editOrderTotals($magentoOrderId, $response['OrderStatusData']);
                                    $this->saveInvoicedStatus($magentoOrderId, $response);
                                    $this->eventManager->dispatch(
                                        'madhat_order_sync_invoiced',
                                        ['order_id' => $magentoOrderId, 'response' => $response]
                                    );
                                    $this->eventManager->dispatch(
                                        'madhat_order_sync_invoiced_billing',
                                        ['order_id' => $magentoOrderId, 'response' => $response]
                                    );
                                }
                                $result['success_orders']++;
                            }
                            if ($response['OrderStatusData']['Status'] == self::CANCELED_STATUS) {
                                $this->logger->debug(__('Cancel order %1[#%2]', $order->getId(), $order->getIncrementId()));
                                //                                $this->orderManagement->cancel($magentoOrderId);
                                //                                $order->cancel();
                                //                                $this->magentoOrderRepository->save($order);
                                // Direct DB cancel order function should be here
                                $this->directCancelOrder($magentoOrder);
                                $result['success_orders']++;
                            }
                        }
                    } elseif (isset($response['ResponseStatus']['Message'])) {
                        $result['error_messages'][] = [
                            'Order Id' => $magentoOrderId,
                            'Error Message' => $response['ResponseStatus']['Message']
                        ];
                        $result['failed_orders']++;
                    }
                } catch (Exception $e) {
                    $this->siteOrderLogger->error(
                        __(
                            "Error occurred while syncing Order Status for Order Id : %1, Exception : %2",
                            $magentoOrderId,
                            $e->getMessage()
                        )
                    );
                    $result['error_messages'][] = [
                        'Order Id' => $magentoOrderId,
                        'Error Message' => __('Failed to sync Order. Check log for details.')
                    ];
                    $result['failed_orders']++;
                }
            }
            $result['message'] = __(
                '%1 out of %2 order\'s synced from SITE.',
                $result['success_orders'],
                $result['total_orders']
            );
        } else {
            $result['message'] = __(
                'No orders were found to export.'
            );
        }

        if ($result['success_orders'] > 0) {
            $result['success'] = true;
        }

        if ($result['total_orders'] > 0) {
            $this->logReportToDbLogger($result, 'SITE Order Sync Report');
        }

        return $result;

    }

    /**
     * Filter Order Collection for Site Order Sync Process
     *
     * @param array $incrementIds
     * @return Collection
     */
    protected function filterOrderCollectionForSiteOrderSync(array $incrementIds = []): Collection
    {
        $orderCollection = $this->magentoOrderCollectionFactory->create();

        $orderCollection->addFieldToSelect(['increment_id', 'status'])
            ->getSelect()
            ->join(
                ['moi' => $orderCollection->getTable('madhat_order_info')],
                'moi.order_id = main_table.entity_id',
                ['site_order_id', 'site_is_picked', 'site_is_invoiced']
            );

        $orderCollection
            ->addFieldToFilter('status', ['nin' => ['canceled']])
            ->addFieldToFilter('moi.site_order_id', ['notnull' => true]);

        $orderCollection->getSelect()->where('moi.site_is_picked IS NULL OR moi.site_is_invoiced IS NULL');

        if (!empty($incrementIds)) {
            $orderCollection->addFieldToFilter('increment_id', ['in' => $incrementIds]);
        }

        return $orderCollection;
    }

    /**
     * Export order payment transaction details to SITE
     *
     * @param array $incrementIds
     * @return array
     */
    public function exportSiteOrderPayments(array $incrementIds = []): array
    {
        $orderCollection = $this->filterOrderCollectionForSiteOrderPayments($incrementIds);

        if (!$orderCollection->getSize()) {
            $result['message'] = __(
                'No orders were found to export transaction details.'
            );
        }

        $order = $orderCollection->getFirstItem();
        $websiteId = $order->getStore()->getWebsiteId();

        $paymentMethodsMapping = $this->siteOrderHelper->getPaymentMethodsMapping($websiteId);
        $this->paymentMethodsArray = $this->json->unserialize($paymentMethodsMapping);

        $result['success'] = false;
        $result['total_orders'] = $orderCollection->getSize();
        $result['success_orders'] = 0;
        $result['failed_orders'] = 0;
        $result['error_messages'] = [];

        if ($orderCollection->getSize()) {

            $orderIds = $orderCollection->getColumnValues('entity_id');
            $orderCollection = $this->magentoOrderCollectionFactory->create();
            $orderCollection->addFieldToFilter('entity_id', ['in' => $orderIds]);

            foreach ($orderCollection as $order) {
                $magentoOrderId = (int)$order->getEntityId();
                try {
                    $response = $this->processSitePayment($order);
                    if (isset($response['ResponseResult']) && $response['ResponseResult']['Code'] == 'SUCCESS') {
                        $this->saveCapturedStatus($magentoOrderId, $response);
                        $result['success_orders']++;
                    } elseif (isset($response['ResponseStatus']['Message'])) {
                        $result['error_messages'][] = [
                            'Order Id' => $magentoOrderId,
                            'Error Message' => $response['ResponseStatus']['Message']
                        ];
                        $result['failed_orders']++;
                    }
                } catch (Exception $e) {
                    $this->siteOrderLogger->error(
                        __(
                            "Error occurred while exporting transaction details for Order Id : %1, Exception : %2",
                            $magentoOrderId,
                            $e->getMessage()
                        )
                    );
                    $result['error_messages'][] = [
                        'Order Id' => $magentoOrderId,
                        'Error Message' => __('Failed to export transaction details. Check log for details.')
                    ];
                    $result['failed_orders']++;
                }
            }
            $result['message'] = __(
                '%1 out of %2 order\'s transaction details exported to SITE.',
                $result['success_orders'],
                $result['total_orders']
            );
        }

        if ($result['success_orders'] > 0) {
            $result['success'] = true;
        }

        if ($result['total_orders'] > 0) {
            $this->logReportToDbLogger($result, 'SITE Order Transaction Details Export Report');
        }

        return $result;
    }

    /**
     * Filter Order Collection for export order payment transaction details to SITE
     *
     * @param array $incrementIds
     * @return Collection
     */
    protected function filterOrderCollectionForSiteOrderPayments(array $incrementIds = []): Collection
    {
        $orderCollection = $this->magentoOrderCollectionFactory->create();

        $orderCollection->addFieldToSelect(['increment_id', 'status'])
            ->getSelect()
            ->join(
                ['sop' => $orderCollection->getTable('sales_order_payment')], // Join sales_order_payment table
                'main_table.entity_id = sop.parent_id',
                ['cc_trans_id']
            )
            ->join(
                ['an' => $orderCollection->getTable('adyen_notification')], // Join adyen_notification table
                'an.original_reference = sop.cc_trans_id AND an.merchant_reference = main_table.increment_id',
                ['event_code', 'success', 'done']
            )
            ->join(
                ['moi' => $orderCollection->getTable('madhat_order_info')], // Join madhat_order_info table
                'moi.order_id = main_table.entity_id',
                ['site_order_id', 'site_is_captured']
            );

        $orderCollection
            ->addFieldToFilter('status', ['nin' => ['canceled']])
            ->addFieldToFilter('sop.cc_trans_id', ['notnull' => true])
            ->addFieldToFilter('an.event_code', 'CAPTURE')
            ->addFieldToFilter('an.success', 'true')
            ->addFieldToFilter('an.done', '1')
            ->addFieldToFilter('moi.site_order_id', ['notnull' => true])
            ->addFieldToFilter('moi.site_is_captured', ['null' => true]);

        if (!empty($incrementIds)) {
            $orderCollection->addFieldToFilter('increment_id', ['in' => $incrementIds]);
        }

        return $orderCollection;
    }

    /**
     * Log Report to DbLogger
     *
     * @param array $result
     * @param string $title
     * @return void
     */
    public function logReportToDbLogger(array $result, string $title): void
    {
        $message = "Total Order's : " . $result['total_orders'];
        $message .= " | Success Order's : " . $result['success_orders'];
        $message .= " | Failed Order's : " . $result['failed_orders'];

        if (!empty($result['error_messages'])) {
            $message .= " | Errors : " . $this->json->serialize($result['error_messages']);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            $title,
            $message,
            'NOTICE',
            LogIdentifierProvider::ORDER_EXPORT
        );
        $this->emulation->stopEnvironmentEmulation();
    }

    /**
     * Create data for SITE Order Export process
     *
     *  <p>
     *  OrderContainer
     *  All fields
     *  --------------------
     *  key | type | required
     *  --------------------
     *  orderhead | orderhead (JSON Object) | No
     *  orderrowcontainers | List<orderrowcontainer> (JSON Object) | No
     *  customer | customerElement (JSON Object) | No
     *  </p>
     *
     * @param Order $order
     * @return array
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function createSiteOrderParams(Order $order): array
    {
        $siteOrderParams = [];
        $siteOrderParams = $this->prepareOrderHeadData($siteOrderParams, $order);
        $siteOrderParams = $this->prepareCustomerData($siteOrderParams, $order);
        $siteOrderParams = $this->prepareOrderRowContainerData($siteOrderParams, $order);

        return $siteOrderParams;
    }

    /**
     * Prepare Order Head Params
     *
     *  <p>
     *  OrderHead
     *  All fields
     *  ----------------
     *  orderno | string | No
     *  creationdate | DateTime | Yes
     *  subscriptionorder | bool | No
     *  autogenerated | bool | No
     *  paymentmethod | paymentmethodElement (JSON Object) | No
     *  campaignid | string | No
     *  shippingmethod | shippingmethodElement (JSON Object) | No
     *  language | languageElement (JSON Object) | No
     *  currency    | curencyElement (JSON Object) | No
     *  ordersumgross | decimal | Yes
     *  ordersumnet | decimal | Yes
     *  cardtype    | cardtypeElement (JSON Object) | No
     *  cardtransactiontype | cardtransactiontypeElement (JSON Object) | No
     *  transactionid | string | No
     *  transactionid2 | string | No
     *  transactionid3 | string | No
     *  notificationtype    | notificationtypeElement (JSON Object) | No
     *  portal| portalElement (JSON Object) | No
     *  shippingname | string | No
     *  shippingaddress1    | string | No
     *  shippingaddress2    | string | No
     *  shippingcontactperson | string | No
     *  shippingcareof | string | No
     *  shippingzipcode | string | No
     *  shippingcity | string | No
     *  shippingcountry | shippingcountryelement (JSON Object) | No
     *  entrycode | string | No
     *  pickuppointid | string | No
     *  pickuppointname | string | No
     *  docheckvat | bool | No
     *  ordercomment | string | No
     *  affiliateid | string | No
     *  ordercustomerno | string | No
     *  Item | string | No
     *  ItemElementName | ItemChoiceType (JSON Object) | Yes
     *  invoicetype | invoicetypeElement (JSON Object) | No
     *  ordertype | string | No
     *  originalorderreference | string | No
     *  returnreason | string | No
     *  picklistblock | string | No
     *  carrierreference | string | No
     *  exvat | exvatelement (JSON Object) | No
     *  customervataccount | customervataccountelement (JSON Object) | No
     *  warehousenumber | string | No
     *  deliverycostpricezone | deliverycostpricezoneElement (JSON Object) | No
     *  externalorderno | string | No
     *  seller | string | No
     *  subscriptioninterval | string | No
     *  deliverynotifymail | string | No
     *  deliverynotifyphone | string | No
     *  </p>
     *
     * @param array $siteOrderParams
     * @param Order $order
     * @return array
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function prepareOrderHeadData(array $siteOrderParams, Order $order): array
    {
        $siteOrderParams = $this->prepareOrderData($siteOrderParams, $order);
        $siteOrderParams = $this->prepareOrderShippingData($siteOrderParams, $order);
        $siteOrderParams = $this->prepareCardData($siteOrderParams, $order);

        return $siteOrderParams;
    }

    /**
     * Prepare Order basic data for OrderHead params
     *
     * @param array $siteOrderParams
     * @param Order $order
     *
     * @return array
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function prepareOrderData(array $siteOrderParams, Order $order): array
    {
        $siteOrderParams['orderhead']['orderno'] = $order->getId();
        $siteOrderParams['orderhead']['creationdate'] = $order->getCreatedAt();
        $siteOrderParams['orderhead']['autogenerated'] = false;
        $siteOrderParams['orderhead']['paymentmethod'] = $this->preparePaymentMethod($order);
        $siteOrderParams['orderhead']['shippingmethod'] = $this->prepareShippingMethod($order);
        $siteOrderParams['orderhead']['language'] = $this->prepareLanguage($order);
        $siteOrderParams['orderhead']['currency'] = ['Value' => strtolower($order->getBaseCurrencyCode())];
        $siteOrderParams['orderhead']['ordersumgross'] = $order->getBaseGrandTotal();
        $siteOrderParams['orderhead']['ordersumnet'] = max($order->getBaseGrandTotal() - $order->getBaseTaxAmount(), 0);
        $siteOrderParams['orderhead']['notificationtype'] = $this->prepareNotificationType();
        $siteOrderParams['orderhead']['portal'] = $this->preparePortal($order);
        $siteOrderParams['orderhead']['ordercomment'] = $this->getOrderComment($order);
        $siteOrderParams['orderhead']['ordercustomerno'] = $this->prepareCustomerNumber($order);
        $siteOrderParams['orderhead']['Item'] = $this->prepareItem($order);
        $siteOrderParams['orderhead']['ItemElementName'] = $this->prepareItemElementName($order);
        $siteOrderParams['orderhead']['invoicetype'] = ['Value' => 'Item0'];
        $siteOrderParams['orderhead']['ordertype'] = $this->prepareOrdertype($order);
        $siteOrderParams['orderhead']['externalorderno'] = $order->getIncrementId();
        $siteOrderParams['orderhead']['deliverynotifyphone'] = $this->prepareDeliveryNotifyPhone($order);
        $siteOrderParams['orderhead']['purchasereference'] = $this->preparePurchaseReference($order);

        return $siteOrderParams;
    }

    /**
     * @param array $siteOrderParams
     * @param Order $order
     * @return array
     *
     * <p>
     * Customer
     * All fields
     * ----------------
     * customerno | string | No
     * customertype | customertypeelement (JSON Object) | No
     * customername | string | No
     * billingaddress1 | string | No
     * billingaddress2 | string | No
     * billingcontactperson | string | No
     * careof | string | No
     * billingzipcode | string | No
     * billingcity | string | No
     * entrycode | string | No
     * socialsecurityno | string | No
     * exvat | exvatelement | No
     * customercountry | customercountryelement (JSON Object) | No
     * customerlanguage | customerlanguageelement (JSON Object) | No
     * customercurrency | customercurrencyelement (JSON Object) | No
     * phoneno | string | No
     * email | string | No
     * paymentterm | paymenttermelement (JSON Object) | No
     * customerportal | customerportalelement (JSON Object) | No
     * customerstatus | customerstatuselement (JSON Object) | No
     * creditlimit | decimal | Yes
     * mobilephoneno | string | No
     * customervatno | customervatnoelement (JSON Object) | No
     * originalcampaign | originalcampaignelement (JSON Object) | No
     * customervataccount | customervataccountelement (JSON Object) | No
     * eprocurementid | string | No
     * costcenter | string | No
     * invoiceemail | string | No
     * </p>
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function prepareCustomerData(array $siteOrderParams, Order $order): array
    {
        $siteOrderParams['customer'] = [
            'customerno' => $this->prepareCustomerNumber($order),
            'customertype' => $this->prepareCustomerType($order),
            'customername' => $this->prepareCustomerName($order),
            'billingaddress1' => $this->prepareBillingAddress($order),
            'billingaddress2' => $this->prepareBillingAddress($order, 2),
            'billingcontactperson' => $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname(),
            'billingzipcode' => $order->getBillingAddress()->getPostcode(),
            'billingcity' => $order->getBillingAddress()->getCity(),
            'billingcounty' => $order->getBillingAddress()->getRegion(),
            'exvat' => $this->prepareCustomerExVat($order),
            'customercountry' => ['Value' => $order->getBillingAddress()->getCountryId()],
            'customerlanguage' => $this->prepareLanguage($order),
            'phoneno' => $this->prepareTelephoneNo($order),
            'email' => $order->getCustomerEmail(),
            'customerportal' => $this->preparePortal($order),
            'customerstatus' => ['Value' => 'Item4'],
            'creditlimit' => $this->prepareCreditlimit($order),
            'mobilephoneno' => $this->prepareTelephoneNo($order),
            'customervatno' => $this->prepareCustomerExVat($order),
            'customervataccount' => $this->prepareCustomerVatAccount($order),
            'customercurrency' => ['Value' => strtolower($order->getBaseCurrencyCode())],
            'paymentterm' => $this->preparePaymentTerm($order),
            'invoiceemail' => $this->prepareCustomerInvoiceMail($order),
        ];
        return $siteOrderParams;
    }

    /**
     * @param array $siteOrderParams
     * @param Order $order
     * @return array
     *
     * <p>
     * OrderRowContainer[]
     * All fields
     * ----------------
     * productno | string | No
     * subscriptionline | bool | No
     * quantity | string | No
     * price | decimal | Yes
     * deliverygroup | string | No
     * name | string | No
     * orderheadno | string | No
     * externalproductno | string | No
     * externallineno | string | No
     * discountvalue | decimal | Yes
     * discountvalueSpecified | bool | Yes
     * discountpercentage | decimal | Yes
     * discountpercentageSpecified | bool | Yes
     * expecteddeliverydate | DateTime | Yes
     * expecteddeliverydateSpecified | bool | Yes
     * excludestockbalance | bool | Yes
     * excludestockbalanceSpecified | bool | Yes
     * exciseduty | decimal | Yes
     * serialnumberlist | string | No
     * </p>
     * @throws NoSuchEntityException
     */
    protected function prepareOrderRowContainerData(array $siteOrderParams, Order $order): array
    {
        foreach ($order->getAllVisibleItems() as $key => $orderItem) {
            $siteOrderParams['orderrowcontainers'][$key] = [
                'productno' => $orderItem->getSku(),
                'quantity' => (int)$orderItem->getQtyOrdered(),
                'price' => $orderItem->getPrice(),
                'deliverygroup' => $this->prepareDeliveryGroup($orderItem, $order),
                'name' => $orderItem->getName(),
                'orderheadno' => (string)$order->getId(),
                'serialnumberlist' => '',
                'externallineno' => $orderItem->getItemId()
            ];
            $itemDiscount = abs((float)$orderItem->getDiscountAmount());
            if ($itemDiscount && !$order->getCouponCode()) {
                $siteOrderParams['orderrowcontainers'][$key]['discountvalueSpecified'] = true;
                $siteOrderParams['orderrowcontainers'][$key]['discountvalue'] = $itemDiscount;
            }
        }
        // SHIPPING and SHIPPING FEE
        //        $shippingAndShippingFee[] = [
        //            'productno' => self::SHIPPING_PRODUCT_NO,
        //            'quantity' => 1,
        //            'price' => $order->getShippingAmount(),
        //            'name' => $this->getShippingDescription($order),
        //            'orderheadno' => (string)$order->getId(),
        //        ];
        //        // Add shipinng + shipping fee as 8b should be only one 8b record!
        //        $shippingAndShippingFeeTotal = 0;
        //        $shippingAndShippingFeeDescription = [];
        //        foreach ($shippingAndShippingFee as $item) {
        //            $shippingAndShippingFeeTotal += $item['price'];
        //            if ($item['price'] > 0) {
        //                $shippingAndShippingFeeDescription[] = $item['name'];
        //            }
        //        }
        //        $siteOrderParams['orderrowcontainers'][] = [
        //            'productno' => self::SHIPPING_PRODUCT_NO,
        //            'quantity' => 1,
        //            'price' => $shippingAndShippingFeeTotal,
        //            'name' => implode('+', $shippingAndShippingFeeDescription),
        //            'orderheadno' => (string)$order->getId(),
        //        ];

        // SHIPPING
        $siteOrderParams['orderrowcontainers'][] = [
            'productno' => self::SHIPPING_PRODUCT_NO,
            'quantity' => 1,
            'price' => $order->getShippingAmount(),
            'name' => $this->getShippingDescription($order),
            'orderheadno' => (string)$order->getId(),
        ];
        //        // INFO20
        //        $rulesIds = [];
        //        $isFreeShippingHandled = false;
        //        if (!empty($order->getAppliedRuleIds())) {
        //            $rulesIds = explode(',', $order->getAppliedRuleIds());
        //        }
        //        foreach ($rulesIds as $ruleId) {
        //            $rule = $this->ruleRepository->getById($ruleId);
        //            if ($rule->getSimpleFreeShipping() == 1) { // INFO20 for free shipping
        //                $isFreeShippingHandled = true;
        //            }
        //            $siteOrderParams['orderrowcontainers'][] = [
        //                'productno' => self::INFO_PRODUCT_NO,
        //                'quantity' => 1,
        //                'price' => 0,
        //                'name' => !empty($rule->getDescription()) ? $rule->getDescription() : $rule->getName(),
        //                'orderheadno' => (string)$order->getId(),
        //            ];
        //        }
        //        // RABATT20
        //        if ($order->getCouponCode()) {
        //            if (!$isFreeShippingHandled) {
        //                $discountAmount = abs((float)$order->getDiscountAmount());
        //                if ($discountAmount > 0) {
        //                    $siteOrderParams['orderrowcontainers'][] = [
        //                        'productno' => self::DISCOUNT_PRODUCT_NO,
        //                        'quantity' => -1,
        //                        'price' => $discountAmount,
        //                        'name' => (string)__("You received a discount: %1 %2", $order->getDiscountAmount(), $order->getBaseCurrencyCode()),
        //                        'orderheadno' => (string)$order->getId(),
        //                    ];
        //                }
        //            }
        //        }
        // INFO20
        $rulesIds = [];
        $isFreeShippingHandled = false;
        if (!empty($order->getAppliedRuleIds())) {
            $rulesIds = explode(',', $order->getAppliedRuleIds());
        }
        foreach ($rulesIds as $ruleId) {
            $rule = $this->ruleRepository->getById($ruleId);
            if ($rule->getSimpleFreeShipping() == 1) { // INFO20 for free shipping
                $isFreeShippingHandled = true;
                $siteOrderParams['orderrowcontainers'][] = [
                    'productno' => self::INFO_PRODUCT_NO,
                    'quantity' => 1,
                    'price' => 0,
                    'name' => !empty($rule->getDescription()) ? $rule->getDescription() : $rule->getName(),
                    'orderheadno' => (string)$order->getId(),
                ];
            }
        }
        // RABATT20
        if (!$isFreeShippingHandled) {
            $discountAmount = abs((float)$order->getDiscountAmount());
            if ($discountAmount > 0) {
                $siteOrderParams['orderrowcontainers'][] = [
                    'productno' => self::DISCOUNT_PRODUCT_NO,
                    'quantity' => -1,
                    'price' => $discountAmount,
                    'name' => (string)__("You received a discount: %1 %2", $order->getDiscountAmount(), $order->getBaseCurrencyCode()),
                    'orderheadno' => (string)$order->getId(),
                ];
            }
        }
        return $siteOrderParams;
    }

    protected function prepareOrderShippingData(array $siteOrderParams, Order $order): array
    {
        $shippingAddress = $order->getShippingAddress();

        $siteOrderParams['orderhead']['shippingname'] = $this->prepareShippingName($order);
        $siteOrderParams['orderhead']['shippingaddress1'] = $this->prepareShippingAddress($order);
        $siteOrderParams['orderhead']['shippingaddress2'] = $this->prepareShippingAddress($order, 2);
        $siteOrderParams['orderhead']['shippingcontactperson'] = $shippingAddress->getFirstname() . ' ' . $shippingAddress->getLastname();
        $siteOrderParams['orderhead']['shippingcareof'] = '';
        $siteOrderParams['orderhead']['shippingzipcode'] = $shippingAddress->getPostcode();
        $siteOrderParams['orderhead']['shippingcity'] = $shippingAddress->getCity();
        $siteOrderParams['orderhead']['shippingcounty'] = $shippingAddress->getRegion();
        $siteOrderParams['orderhead']['shippingcountry'] = ['Value' => $shippingAddress->getCountryId()];

        return $siteOrderParams;
    }

    /**
     * @throws LocalizedException
     */
    protected function prepareCardData(array $siteOrderParams, Order $order): array
    {
        $paymentMethod = $order->getPayment()->getMethod();
        $paymentMethodVismaCode = null;
        if ($paymentMethod == "adyen_pay_by_link") {
            $orderPspReference = $order->getPayment()->getAdyenPspReference();

            $adyenNotificationCollection = $this->adyenNotificationCollectionFactory->create()
                ->addFieldToFilter('pspreference', $orderPspReference)
                ->getFirstItem();

            $additionalData = unserialize($adyenNotificationCollection->getAdditionalData());
            $paymentSubMethod = $additionalData['checkout.cardAddedBrand'];
            if (isset($this->paymentMethodsArray[$paymentMethod][$paymentSubMethod])) {
                $paymentMethodVismaCode = $this->paymentMethodsArray[$paymentMethod][$paymentSubMethod];
            }
        } else {
            $paymentMethodVismaCode = $this->paymentMethodsArray[$paymentMethod];
        }

        if (!$paymentMethodVismaCode) {
            $errorMessage = __(
                'SiteIntegrationOrder Issue : Order Id: %1, Payment method [%2] SubPaymentMethod [%3] mapping not found.',
                $order->getId(),
                $paymentMethod,
                $paymentSubMethod
            );
            $this->siteOrderLogger->error($errorMessage);
            throw new LocalizedException($errorMessage);
        }

        $allowedOfflinePaymentMethods = $this->siteOrderHelper->getAllowedOfflinePaymentMethodsFromConfig('1');

        $vismaCardTypePaymentMethods = ['Item706', 'Item707'];

        // Set card transaction details for specific payment methods
        if (in_array($paymentMethodVismaCode, $vismaCardTypePaymentMethods)) {
            $siteOrderParams['orderhead']['cardtype'] = $this->preparePaymentCardType($order);
        }

        if ($paymentMethodVismaCode === 'Item706') {
            $siteOrderParams['orderhead']['cardtransactiontype'] = [
                'Value' => 'Item2'
            ];
        } elseif ($paymentMethodVismaCode === 'Item707') {
            $siteOrderParams['orderhead']['cardtransactiontype'] = [
                'Value' => 'Item102'
            ];
        }

        $transactionId = $order->getEntityId();

        if (!in_array($paymentMethod, $allowedOfflinePaymentMethods)) {
            $transactionId = $order->getPayment()->getLastTransId();
        }

        $siteOrderParams['orderhead']['transactionid'] = $transactionId;
        $siteOrderParams['orderhead']['transactionid2'] = $transactionId;

        return $siteOrderParams;
    }

    /**
     * @throws LocalizedException
     */
    protected function preparePaymentMethod(Order $order): array
    {
        $paymentMethod = $order->getPayment()->getMethod();

        if ($paymentMethod == "adyen_pay_by_link") {
            $orderPspReference = $order->getPayment()->getAdyenPspReference();

            $adyenNotificationCollection = $this->adyenNotificationCollectionFactory->create()
                ->addFieldToFilter('pspreference', $orderPspReference)
                ->getFirstItem();

            $additionalData = unserialize($adyenNotificationCollection->getAdditionalData());
            $paymentSubMethod = $additionalData['checkout.cardAddedBrand'];
            if (isset($this->paymentMethodsArray[$paymentMethod][$paymentSubMethod])) {
                return ['Value' => $this->paymentMethodsArray[$paymentMethod][$paymentSubMethod]];
            } else {
                $errorMessage = __(
                    'SiteIntegrationOrder Issue : Order Id: %1, Payment method [%2] SubPaymentMethod [%3] mapping not found.',
                    $order->getId(),
                    $paymentMethod,
                    $paymentSubMethod
                );
                $this->siteOrderLogger->error($errorMessage);
                throw new LocalizedException($errorMessage);
            }
        }

        if (!empty($this->paymentMethodsArray)) {
            if (isset($this->paymentMethodsArray[$paymentMethod])) {
                return ['Value' => $this->paymentMethodsArray[$paymentMethod]];
            } else {
                $errorMessage = __(
                    'SiteIntegrationOrder Error : Payment method : %1 mapping not found for Order Id: %2',
                    $paymentMethod,
                    $order->getId()
                );
                $this->siteOrderLogger->error($errorMessage);
                throw new LocalizedException($errorMessage);
            }
        } else {
            $errorMessage = __(
                'SiteIntegrationOrder Error : Payment method mapping is empty. Please configure in admin-panel',
                $paymentMethod,
                $order->getId()
            );
            $this->siteOrderLogger->error($errorMessage);
            throw new LocalizedException($errorMessage);
        }
    }

    /**
     * @param Order $order
     * @return string[]
     * @throws LocalizedException
     */
    protected function prepareShippingMethod(Order $order): array
    {
        $shippingMethod = $order->getShippingDescription();

        $this->siteOrderLogger->info(
            __(
                "Magento Order ID : [%1], Shipping Description : [%2] Data : %3",
                $order->getId(),
                $shippingMethod,
                json_encode($order->getData())
            )
        );

        if (empty($shippingMethod)) {
            $errorMessage = __('SiteIntegrationOrder Error: Order Shipping method is empty for Order ID : %1', $order->getId());
            $this->siteOrderLogger->error($errorMessage);
            throw new LocalizedException(__($errorMessage));
        }

        if (empty($this->shippingMethodsArray)) {
            $errorMessage = __('SiteIntegrationOrder Error: Shipping method mapping is empty. Please configure it in the admin panel.');
            $this->siteOrderLogger->error($errorMessage);
            throw new LocalizedException(__($errorMessage));
        }

        if (!isset($this->shippingMethodsArray[$shippingMethod])) {
            $errorMessage = sprintf(
                'SiteIntegrationOrder Error: Shipping method "%s" mapping not found for Order ID: %s',
                $shippingMethod,
                $order->getId()
            );
            $this->siteOrderLogger->error($errorMessage);
            throw new LocalizedException(__($errorMessage));
        }

        return ['Value' => $this->shippingMethodsArray[$shippingMethod]];
    }

    protected function prepareLanguage(Order $order): array
    {
        $language = $this->siteOrderHelper->getLanguageCode($order);
        $languageArr = explode('_', $language);

        return [
            'Value' => $languageArr[0],
        ];
    }

    /**
     * Prepare Notification Type Value for SITE Order Params
     *
     * @return string[]
     *
     * <p>
     * NotificationType - delivery notification type:
     *    0 = default
     *    1 = SMS notification
     *    2 = flag to denote a separate delivery address that is passed in with the message
     *    3 = SMS notification and separate delivery address
     * </p>
     */
    protected function prepareNotificationType(): array
    {
        return ['Value' => 'Item0'];
    }

    /**
     * @param Order $order
     * @return array
     */
    protected function preparePortal(Order $order): array
    {
        $portalNumber = $this->siteOrderHelper->getPortalValue(
            $order->getStore()->getWebsiteId()
        );
        return ['Value' => $portalNumber];
    }

    protected function prepareShippingName(Order $order): string
    {
        $shippingAddress = $order->getShippingAddress();
        $company = $shippingAddress->getCompany();
        if ($company === null) {
            return '';
        }
        return trim($company);
    }

    protected function prepareShippingAddress(Order $order, int $index = 1): string
    {
        list($shippingAddress1, $shippingAddress2) = array_pad(
            $order->getShippingAddress()->getStreet(),
            2,
            ''
        );
        if ($index == 2) {
            return $shippingAddress2;
        }
        return $shippingAddress1;
    }

    /**
     * Get Order Customer Comment from Hyva Checkout Module
     *
     * @param Order $order
     *
     * @return string
     */
    protected function getOrderComment(Order $order): string
    {
        try {
            // Create sort order
            $sortOrder = $this->sortOrderBuilder
                ->setField('created_at')
                ->setDirection(\Magento\Framework\Api\SortOrder::SORT_ASC)
                ->create();

            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter('parent_id', $order->getEntityId())
                ->addFilter('is_customer_comment', 1)
                ->setPageSize(1)
                ->setSortOrders([$sortOrder])
                ->create();

            $historyList = $this->orderStatusHistoryRepository->getList($searchCriteria);

            if ($historyList->getTotalCount() > 0) {
                $items = $historyList->getItems();
                $firstItem = reset($items);
                $comment = $firstItem->getComment();
                return $comment ? substr((string)$comment, 0, 40) : '';
            }

            return '';
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'getOrderComment Exception',
                $e->getMessage(),
                'NOTICE',
                LogIdentifierProvider::ORDER_EXPORT
            );
            return '';
        }
    }

    /**
     * @param Order $order
     * @return int
     * <p>
     * OrderType - type of order
     * 0 = default, regular web order
     * 1 = refund or crediting order
     * 2 = replacement order
     * </p>
     */
    protected function prepareOrdertype(Order $order): int
    {
        // TODO : Need to implement "Refund or Replacement" order.
        return 0;
    }

    //    protected function isRefundOrReplacement(Order $order): bool
    //    {
    //        if ($order->getPayment()->getMethod() == self::PAYMENT_METHOD_REPLACEMENT) {
    //            return true;
    //        }
    //        return false;
    //    }

    /**
     * For B2B orders/customers in Europe: specify ItemElementName attribute and set ItemChoiceType enum value
     * For SE and NO as 'organizationno'
     * For the rest of European countries as 'eutaxno' .
     *
     * @param Order $order
     * @return string
     */
    protected function prepareItemElementName(Order $order): string
    {
        $countryId = $order->getBillingAddress()->getCountryId();
        if (in_array($countryId, ['SE', 'NO'])) {
            return 'organizationno';
        } elseif (in_array($countryId, $this->siteOrderHelper->getEuropeanCountries())) {
            return 'eutaxno';
        }
        return '';
    }

    /**
     * @TODO: Not finished yet, don't know what must return for europe and other countries
     * @param Order $order
     * @return string
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function prepareItem(Order $order): string
    {
        if (in_array($order->getBillingAddress()->getCountryId(), ['SE', 'NO'])) {
            return $this->getOrganisationNumber($order);
        } elseif (in_array($order->getBillingAddress()->getCountryId(), $this->siteOrderHelper->getEuropeanCountries())) {
            return $this->getVat($order);
        } else {
            return '';
        }
    }

    protected function getOrganisationNumber(Order $order): string
    {
        return '';
    }

    /**
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    protected function getVat(Order $order): string
    {
        $customer = $this->customerRepository->getById($order->getCustomerId());
        $address = $order->getBillingAddress();

        if ($customer && $addressId = $customer->getDefaultBilling()) {
            $address = $this->addressRepository->getById($addressId);
        }

        $vat = preg_replace('/^[^0-9]*/', '', $customer->getTaxvat());
        return str_replace('GR', 'EL', $address->getCountryId()) . $vat;
    }

    protected function prepareDeliveryNotifyPhone(Order $order): string
    {
        try {
            $shippingAddress = $order->getShippingAddress();
            $telephone = $shippingAddress->getTelephone();
            return trim($telephone);
        } catch (Exception $e) {
            $this->siteOrderLogger->error(
                __(
                    "Order ID : %1, %2 => Exception : %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $e->getMessage()
                )
            );
            return '';
        }
    }

    /**
     * Prepare purchase_reference value for Order data
     *
     * @param Order $order
     * @return string
     * @throws NoSuchEntityException
     */
    protected function preparePurchaseReference(Order $order): string
    {
        $quoteId = (int) $order->getQuoteId();
        // $quote = $this->quoteFactory->create()->load($quoteId); DEPRICATED
        $quote = $this->cartRepositoryInterface->get($quoteId);
        $purchaseReference = $quote->getPurchaseReference();

        return $purchaseReference !== null
            ? substr($purchaseReference, 0, 40)
            : '';
    }

    /**
     * @param OrderInterface $order
     * @return string
     *
     * <p>
     * picklistblock - default 0 or skipped. If refund or replacement order: set to 98
     * </p>
     */
    protected function preparePicklistBlock(Order $order): string
    {
        return '98';
    }

    /**
     * @throws LocalizedException
     */
    protected function prepareCustomerNumber($order)
    {
        $this->siteOrderLogger->info(
            __(
                "Magento Order ID : [%1], Order Data : %2",
                $order->getId(),
                json_encode($order->getData())
            )
        );

        $customerEmail = $order->getCustomerEmail();
        try {
            $madhatCustomerInfo = $this->madhatCustomerInfoRepository->getByEmail($customerEmail);
        } catch (Exception $e) {
            $madhatCustomerInfo = $this->madhatCustomerInfoFactory->create();
            $madhatCustomerInfo->setCustomerEmail($customerEmail);

            $madhatCustomerInfo = $this->madhatCustomerInfoRepository->save($madhatCustomerInfo);
        }
        return $madhatCustomerInfo->getEntityId();
    }

    /**
     * @param Order $order
     * @return string[]
     */
    protected function prepareCustomerType(Order $order): array
    {
        if ($this->siteOrderHelper->getCustomerType($order) == CustomerType::B2B) {
            return ['Value' => 'Item49'];
        }

        return ['Value' => 'Item10'];
    }

    protected function prepareCustomerName(Order $order): string
    {
        if ($order->getCustomerId()) {
            $customer = $this->customerRepository->getById($order->getCustomerId());
            if ($customer) {
                return $customer->getFirstname() . ' ' . $customer->getLastname();
            }
        }

        return $order->getCustomerFirstname() . ' ' . $order->getCustomerLastname();
    }

    protected function prepareBillingAddress($order, $index = 1)
    {
        list($billingAddress1, $billingAddress2) = array_pad(
            $order->getBillingAddress()->getStreet(),
            2,
            ''
        );
        if ($index == 2) {
            return $billingAddress2;
        }
        return $billingAddress1;
    }

    /**
     * Prepare exvat value for Order customerdata
     *
     * @param $order
     * @return array
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function prepareCustomerExVat($order): array
    {
        try {
            $countryId = $order->getBillingAddress()->getCountryId();
            if (isset($this->vatMappingArray[$countryId])) {
                return ['Value' => $this->vatMappingArray[$countryId]['exVat']];
            }

            $this->siteOrderLogger->warning(
                __(
                    "Order Id: %1, Function: %2 => Invalid country code: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $countryId
                )
            );

            throw new LocalizedException(
                __("Invalid country code (%1) to get Customer Ex VAT.", $countryId)
            );
        } catch (Exception $e) {
            $this->siteOrderLogger->error(
                __(
                    "Order Id: %1, Function: %2 => Exception: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $e->getMessage()
                )
            );
            throw $e;
        }
    }

    /**
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    protected function prepareTelephoneNo(Order $order): string
    {
        $address = $order->getBillingAddress();

        if ($order->getCustomerId()) {
            $customer = $this->customerRepository->getById($order->getCustomerId());
            if ($customer && $addressId = $customer->getDefaultBilling()) {
                $address = $this->addressRepository->getById($addressId);
            }
        }

        return (string)$address->getTelephone();
    }

    protected function prepareCreditlimit(Order $order)
    {
        if ($order->getCustomerId()) {
            $customer = $this->customerRepository->getById($order->getCustomerId());

            //TODO : Use Amasty Credit for Invoice Credit Limit
            if ($customer->getCustomAttribute('fortnox_invoice_limit')) {
                if (!empty($customer->getCustomAttribute('fortnox_invoice_limit')->getValue())) {
                    return (float)$customer->getCustomAttribute('fortnox_invoice_limit')->getValue();
                }
            }
        }

        return 0;
    }

    /**
     * Prepare customervataccount value for order customerdata
     *
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    protected function prepareCustomerVatAccount(Order $order): array
    {
        try {
            $countryId = $order->getBillingAddress()->getCountryId();
            if (isset($this->vatMappingArray[$countryId])) {
                return ['Value' => $this->vatMappingArray[$countryId]['customerVatAccount']];
            }

            $this->siteOrderLogger->warning(
                __(
                    "Order Id: %1, Function: %2 => Invalid country code: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $countryId
                )
            );

            throw new LocalizedException(
                __("Invalid country code (%1) to get Customer VAT account.", $countryId)
            );
        } catch (Exception $e) {
            $this->siteOrderLogger->error(
                __(
                    "Order Id: %1, Function: %2 => Exception: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $e->getMessage()
                )
            );
            throw $e;
        }
    }

    /**
     * Prepare Payment Term for Site Order Export Customer Params
     *
     * @param Order $order
     * @return int[]
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function preparePaymentTerm(Order $order): array
    {
        $customerType = $this->siteOrderHelper->getCustomerType($order);
        if ($customerType == CustomerType::B2C) {
            return ['Value' => 14];
        } elseif ($customerType == CustomerType::B2B) {
            $itemElementName = $this->prepareItemElementName($order);
            if ($itemElementName == 'organizationno') {
                if (!empty($this->getOrganisationNumber($order))) {
                    return ['Value' => 30];
                } else {
                    return ['Value' => 14];
                }
            } elseif ($itemElementName == 'eutaxno') {
                if (!empty($this->getVat($order))) {
                    return ['Value' => 30];
                } else {
                    return ['Value' => 14];
                }
            } else {
                return ['Value' => 14];
            }
        }
        return ['Value' => 0];
    }

    /**
     * @param Item $orderItem
     * @param Order $order
     * @return int|string
     * @throws NoSuchEntityException
     *
     * <p>
     * Deliverygroup = 1 for orderrows that is in stock
     * Deliverygroup = 2 for orderrows that is rested (not in stock)
     * </p>
     */
    protected function prepareDeliveryGroup(Item $orderItem, Order $order): int|string
    {
        // Pass shipping options to the function.
        // If it is DHL och UPS → always set to 1 as delivery group for all order entries
        $shippingName = $this->getShippingDescription($order);
        if (preg_match('/\b(DHL|UPS)\b/i', $shippingName)) {
            return 1;
        }
        //if price = 0 → delivery group 1 (for free gifts etc)
        if ($orderItem->getPrice() == 0) {
            return 1;
        }
        // Others are processed as usual
        $stockItem = $this->stockRegistry->getStockItemBySku($orderItem->getSku());
        $qty = $stockItem->getQty();
        if ($qty > 0) {
            return '1';
        }
        return '2';
    }

    /**
     * @param Order $order
     * @return string|null
     */
    protected function getShippingDescription(Order $order): ?string
    {
        if (str_contains($order->getShippingDescription(), '-')) {
            $descArr = explode('-', $order->getShippingDescription());
            array_shift($descArr);
            return trim(implode('-', $descArr));
        }
        return $order->getShippingDescription();
    }

    /**
     * @throws CouldNotSaveException
     */
    protected function saveSiteOrderId(Order $order, array $response): void
    {
        $orderId = $order->getId();
        try {
            $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$orderId);
        } catch (Exception $e) {
            $madhatOrderInfo = $this->madhatOrderInfoFactory->create();
            $madhatOrderInfo->setOrderId($orderId);
        }

        $madhatOrderInfo->setSiteOrderId($response['VismaOrderNo']);
        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
        $this->orderGridRefresher->refresh($order->getId());
    }

    /**
     * @param int $magentoOrderId
     * @param mixed $response
     * @return void
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    protected function savePickedStatus(int $magentoOrderId, mixed $response): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId($magentoOrderId);

        if (isset($response['OrderStatusData']['Status']) && $response['OrderStatusData']['Status'] == self::PICKED_STATUS) {
            $madhatOrderInfo->setSiteIsPicked(true);
            foreach ($response['OrderStatusData']['OrderRowsData'] as $orderRow) {
                if ($orderRow['ProductNo'] == 'ROUND') {
                    $madhatOrderInfo->setSiteRound((float)$orderRow['PriceGross']);
                }
            }
        }

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
        $this->orderGridRefresher->refresh($magentoOrderId);
    }

    /**
     * @param int $magentoOrderId
     * @param mixed $response
     * @return void
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    protected function saveInvoicedStatus(int $magentoOrderId, mixed $response): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$magentoOrderId);

        if (isset($response['OrderStatusData']['Status']) && $response['OrderStatusData']['Status'] == self::INVOICED_STATUS) {
            $madhatOrderInfo->setSiteIsInvoiced(true);
        }

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
        $this->orderGridRefresher->refresh($magentoOrderId);
    }

    /**
     * @param int $magentoOrderId
     * @param mixed $response
     * @return void
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    protected function saveCapturedStatus(int $magentoOrderId, mixed $response): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId($magentoOrderId);

        if (isset($response['ResponseResult']) && $response['ResponseResult']['Code'] == 'SUCCESS') {
            $madhatOrderInfo->setSiteIsCaptured(true);
        }

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
    }

    /**
     * Return SITE Response for Export Transaction Details.
     *
     * @throws NoSuchEntityException
     * @throws InputException
     */
    protected function processSitePayment($magentoOrder): array
    {
        if (!$this->isInvoiceGenerated($magentoOrder)) {
            return [
                'success' => false,
                'error_message' => __('Payment capture pending for Order.')
            ];
        }

        $payment = $magentoOrder->getPayment();
        $paymentMethod = $payment->getMethod();
        $magentoOrderId = $magentoOrder->getId();
        $acceptedPayments = array_keys($this->paymentMethodsArray);

        $paymentParams = [
            'PaymentData' => [
                'Status' => ['Value' => 'Item10'],
                'WebOrderNo' => $magentoOrderId,
                'StatusInfo' => '',
                'CurrencyCode' => $magentoOrder->getBaseCurrencyCode(),
            ]
        ];

        if (!in_array($paymentMethod, $acceptedPayments)) {
            return $this->paymentEnhancedMessage($paymentParams);
        }

        $orderCollection = $magentoOrder->getCollection();
        $orderCollection->addFieldToSelect(['increment_id', 'status'])
            ->getSelect()
            ->join(
                ['sop' => $orderCollection->getTable('sales_order_payment')], // Join sales_order_payment table
                'main_table.entity_id = sop.parent_id',
                ['cc_trans_id']
            )
            ->join(
                ['an' => $orderCollection->getTable('adyen_notification')], // Join adyen_notification table
                'an.original_reference = sop.cc_trans_id AND an.merchant_reference = main_table.increment_id',
                ['event_code', 'success', 'done', 'pspreference', 'amount_value']
            );

        $orderCollection
            ->addFieldToFilter('entity_id', $magentoOrderId)
            ->addFieldToFilter('an.event_code', 'CAPTURE')
            ->addFieldToFilter('an.success', 'true')
            ->addFieldToFilter('an.done', '1');

        $orderCaptureData = $orderCollection->getFirstItem();

        if (!$orderCaptureData) {
            return [
                'success' => false,
                'error_message' => __('No successful payment capture found.')
            ];
        }

        $paymentParams['PaymentData']['Status'] = ['Value' => 'Item0'];
        $paymentParams['PaymentData']['StatusInfo'] = 'Capture';

        $transactionTypeMapping = $this->siteOrderHelper->getPaymentMethodsCaptureMapping();

        if ($paymentMethod == 'adyen_pay_by_link') {
            $orderPspReference = $magentoOrder->getPayment()->getAdyenPspReference();

            $adyenNotificationCollection = $this->adyenNotificationCollectionFactory->create()
                ->addFieldToFilter('pspreference', $orderPspReference)
                ->getFirstItem();

            $additionalData = unserialize($adyenNotificationCollection->getAdditionalData());
            $paymentSubMethod = $additionalData['checkout.cardAddedBrand'];
            if (isset($transactionTypeMapping[$paymentMethod][$paymentSubMethod])) {
                $paymentParams['PaymentData']['TransactionType'] = $transactionTypeMapping[$paymentMethod][$paymentSubMethod];
            }
        } else {
            if (isset($transactionTypeMapping[$paymentMethod])) {
                $paymentParams['PaymentData']['TransactionType'] = $transactionTypeMapping[$paymentMethod];
            }
        }

        $paymentParams['PaymentData']['Amount'] = $orderCaptureData->getAmountValue() / 100;
        $paymentParams['PaymentData']['TransactionInfo'] = $orderCaptureData->getPspreference();

        return $this->paymentEnhancedMessage($paymentParams);
    }

    protected function getSearchCriteriaForPaymentExport(mixed $orderIncrementIds): SearchCriteria
    {
        if ($orderIncrementIds) {
            $orderCollection = $this->magentoOrderCollectionFactory->create()
                ->addFieldToFilter('increment_id', ['in' => $orderIncrementIds]);

            $orderIds = $orderCollection->getColumnValues('entity_id');

            $madhatOrderInfoCollection = $this->madhatOrderInfoCollectionFactory->create()
                ->addFieldToFilter('order_id', ['in' => $orderIds])
                ->addFieldToFilter('site_order_id', ['neq' => '']);

            $madhatOrderIds = $madhatOrderInfoCollection->getColumnValues('order_id');

            $this->searchCriteriaBuilder->addFilter('entity_id', $madhatOrderIds, 'in');
        }

        return $this->searchCriteriaBuilder->create();
    }

    public function isInvoiceGenerated(Order $order): bool
    {
        $invoices = $order->getInvoiceCollection();

        return (bool)$invoices->getSize();
    }

    /**
     * @throws NoSuchEntityException
     * @throws InputException
     */
    protected function updateSiteOrder(int $magentoOrderId, mixed $fcOrderId): array
    {
        $magentoOrder = $this->magentoOrderRepository->get($magentoOrderId);
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId($magentoOrderId);

        $siteOrderParams['WebOrderNo'] = $magentoOrderId;
        $siteOrderParams['VismaOrderNo'] = $madhatOrderInfo->getSiteOrderId();
        $siteOrderParams['CustomerNo'] = $magentoOrder->getCustomerId();
        $siteOrderParams['FulfilmentcrowdOrderNo'] = $fcOrderId;

        $siteOrderResponse = $this->updateFulfillmentCrowdOrderMessage($siteOrderParams);
        if (isset($siteOrderResponse['ResponseResult']) && ((int)$siteOrderResponse['ResponseResult']['SystemCode'] == 0)) {
            return [
                'success' => true,
                'magento_order_id' => $magentoOrderId,
                'message' => __('FC Order Id exported successfully.')
            ];
        } else {
            return [
                'success' => true,
                'magento_order_id' => $magentoOrderId,
                'message' => __('Issue occurred in FC Order Id export process.')
            ];
        }
    }

    protected function logOrderSyncReportToDbLogger(): void
    {
        $message = "Total Order's : " . $this->totalOrders;
        $message .= " | Success Order's : " . $this->successOrders;
        $message .= " | Failed Order's : " . $this->failedOrders;

        if (!empty($this->orderSyncErrorMessage)) {
            $message .= " | Errors : " . $this->json->serialize($this->orderSyncErrorMessage);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            'SITE Order Sync Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::ORDER
        );
        $this->emulation->stopEnvironmentEmulation();
    }

    protected function logOrderPaymentReportToDbLogger(): void
    {
        $message = "Total Order's : " . $this->totalOrders;
        $message .= " | Success Order's : " . $this->successOrders;
        $message .= " | Failed Order's : " . $this->failedOrders;

        if (!empty($this->orderPaymentErrorMessage)) {
            $message .= " | Errors : " . $this->json->serialize($this->orderPaymentErrorMessage);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            'SITE Order Payment Transaction Export Report',
            $message,
            'NOTICE',
            LogIdentifierProvider::ORDER
        );
        $this->emulation->stopEnvironmentEmulation();
    }

    /**
     * @throws NoSuchEntityException
     * @throws Exception
     */
    public function editOrderItems(int $magentoOrderId, array $siteOrderData): void
    {
        $this->logger->debug("[Line " . __LINE__ . "] Starting editOrderItems for Magento order ID: {$magentoOrderId}");
        $order = $this->magentoOrderFactory->create()->load($magentoOrderId);
        $this->logger->debug("[Line " . __LINE__ . "] Order loaded with increment ID: {$order->getIncrementId()}");
        $websiteId = $order->getStore()->getWebsiteId();
        $this->logger->debug("[Line " . __LINE__ . "] Website ID: {$websiteId}");

        $magentoOrderItems = [];
        foreach ($order->getItems() as $orderItem) {
            if ($orderItem->getProductType() == 'configurable') {
                $this->logger->debug("[Line " . __LINE__ . "] Skipping configurable product: {$orderItem->getProduct()->getSku()}");
                continue;
            }
            $sku = $orderItem->getProduct()->getSku();
            $magentoOrderItems[] = $sku;
            $this->logger->debug("[Line " . __LINE__ . "] Added to array Magento order item: {$sku}, Qty: {$orderItem->getQtyOrdered()}, Price: {$orderItem->getPrice()}");
        }
        $this->logger->debug("[Line " . __LINE__ . "] Magento order items: " . json_encode($magentoOrderItems));

        $siteOrderItems = [];
        $customSiteProducts = $this->siteOrderHelper->getCustomSiteProducts($websiteId);
        $this->logger->debug("[Line " . __LINE__ . "] Custom site products: " . json_encode($customSiteProducts));
        $this->logger->debug("[Line " . __LINE__ . "] OrderRowsData: " . var_export($siteOrderData['OrderRowsData'], true));

        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if (in_array($item['ProductNo'], $customSiteProducts)) {
                $this->logger->debug("[Line " . __LINE__ . "] Skipping custom site product: {$item['ProductNo']}");
                continue;
            }
            $siteOrderItems[] = $item['ProductNo'];
            $this->logger->debug("[Line " . __LINE__ . "] Added site order item: {$item['ProductNo']}, Qty: {$item['Quantity']}, Price: {$item['PriceNet']}");
        }
        $this->logger->debug("[Line " . __LINE__ . "] Site order items: " . json_encode($siteOrderItems));
        $this->logger->debug("[Line " . __LINE__ . "] Values for DIFF: siteOrderItems:" . var_export($siteOrderItems, true));
        $this->logger->debug("[Line " . __LINE__ . "] Values for DIFF: magentoOrderItems:" . var_export($magentoOrderItems, true));

        $productsToBeAdded = array_values(array_diff($siteOrderItems, $magentoOrderItems));
        $this->logger->debug("[Line " . __LINE__ . "] List of products to be added: " . json_encode($productsToBeAdded));

        $productsToBeRemoved = array_values(array_diff($magentoOrderItems, $siteOrderItems));
        $this->logger->debug("[Line " . __LINE__ . "] List of products to be removed: " . json_encode($productsToBeRemoved));

        $productsToBeModified = $this->getProductsToBeModified($order, $productsToBeRemoved, $siteOrderData);

        $newOrderData = [];
        $skuForFixFunctions = $this->getSkuForFixFunctions($productsToBeAdded, $productsToBeModified);
        $this->logger->debug(__('[Line: %1] %2, SKU List for fix functions: %3', __LINE__, __CLASS__, json_encode($skuForFixFunctions)));
        $newOrderData['payment'] = [];
        $newOrderData['shipping'] = [];

        if (!empty($productsToBeAdded) || !empty($productsToBeRemoved) || !empty($productsToBeModified)) {
            $this->logger->debug("[Line " . __LINE__ . "] Order items need modification. Processing changes...");

            // Remove Item
            $newOrderData = $this->removeOrderItems($order, $newOrderData, $productsToBeRemoved);
            // Add Item
            $newOrderData = $this->addOrderItems($order, $newOrderData, $productsToBeAdded, $siteOrderData);
            // Modify Item
            $newOrderData = $this->modifyOrderItems($newOrderData, $productsToBeModified);
            // Fix discounts before saving to be able to row recalculation
            $this->fixDiscountForExistProducts($order, $siteOrderData, $skuForFixFunctions);

            // Save data
            if (!empty($newOrderData)) {
                $this->logger->debug("[Line " . __LINE__ . "] Saving new order data: " . json_encode($newOrderData));
                try {
                    $this->itemsSection->save($order, $newOrderData);
                    $this->setOriginalPricesForOrderItems($order, $skuForFixFunctions);
                    $this->logger->debug("[Line " . __LINE__ . "] Order items successfully updated for order ID: {$magentoOrderId}");
                } catch (\Exception $e) {
                    $this->logger->error("[Line " . __LINE__ . "] Error saving order items for order ID {$magentoOrderId}: " . $e->getMessage());
                }
            }
        } else {
            $this->logger->debug("[Line " . __LINE__ . "] No changes needed for order items in order ID: {$magentoOrderId}");
        }
    }

    protected function getQtyFromSiteData(string $itemSku, mixed $siteOrderData, int $itemQty)
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['Quantity'];
            }
        }
        return $itemQty;
    }

    protected function getPriceFromMagentoData(OrderItemInterface $item): ?float
    {
        if ($item->getParentItem()) {
            return $item->getParentItem()->getPrice();
        }
        return $item->getPrice();
    }

    protected function getPriceNetFromSiteData(string $itemSku, mixed $siteOrderData)
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['PriceNet'];
            }
        }
        return null;
    }

    protected function getPriceGrossFromSiteData(string $itemSku, mixed $siteOrderData)
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['PriceGross'];
            }
        }
        return null;
    }

    protected function getDiscountPercentageFromSiteData(string $itemSku, mixed $siteOrderData)
    {
        foreach ($siteOrderData['OrderRowsData'] as $item) {
            if ($item['ProductNo'] == $itemSku) {
                return $item['DiscountPercentage'];
            }
        }
        return null;
    }

    /**
     * Export fc_order_id for order's to SITE.
     *
     * @param array $orderIncrementIds
     * @return array
     *
     * @throws CouldNotSaveException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     *
     */
    public function exportFcOrderData(array $orderIncrementIds = []): array
    {
        $orderCollection = $this->filterOrderCollectionForFcOrderDataExport($orderIncrementIds);

        $result['success'] = false;
        $result['total_orders'] = $orderCollection->getSize();
        $result['success_orders'] = 0;
        $result['failed_orders'] = 0;
        $result['error_messages'] = [];

        if ($orderCollection->getSize()) {

            $orderIds = $orderCollection->getColumnValues('entity_id');
            $orderCollection = $this->magentoOrderCollectionFactory->create();
            $orderCollection->addFieldToFilter('entity_id', ['in' => $orderIds]);

            foreach ($orderCollection as $order) {
                $response = $this->processFcOrderIdToSite($order);
                if (isset($response['ResponseResult']) && $response['ResponseResult']['Code'] == 'SUCCESS') {
                    $this->updateFcOrderFlag($order);
                    $result['success_orders']++;
                } else {
                    $result['error_messages'][] = [
                        'Order Id' => $order->getEntityId(),
                        'Order Increment Id' => $order->getIncrementId(),
                        'Error Message' => __('Failed to export Fulfilment Crowd Order Id.')
                    ];
                    $this->siteOrderLogger->error(
                        __(
                            "Order ID : %1[%2] failed to export Fulfilment Crowd Order Id. Error Response : %2",
                            $order->getEntityId(),
                            $order->getIncrementId(),
                            $this->json->serialize($response)
                        )
                    );
                    $result['failed_orders']++;
                }
            }
            $result['message'] = __(
                '%1 out of %2 order\'s synced from SITE.',
                $result['success_orders'],
                $result['total_orders']
            );
        } else {
            $result['message'] = __(
                'No orders were found to export.'
            );
        }

        if ($result['success_orders'] > 0) {
            $result['success'] = true;
        }

        if ($result['total_orders'] > 0) {
            $this->logReportToDbLogger($result, 'SITE Fc Order Data Sync Report');
        }

        return $result;
    }

    /**
     * @param $order
     * @return array|bool|float|int|mixed|string|null
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    private function processFcOrderIdToSite($order): mixed
    {
        $magentoOrderId = $order->getEntityId();
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$magentoOrderId);
        $siteOrderParams['WebOrderNo'] = $magentoOrderId;
        $siteOrderParams['VismaOrderNo'] = $madhatOrderInfo->getSiteOrderId();
        $siteOrderParams['CustomerNo'] = $this->prepareCustomerNumber($order);
        $siteOrderParams['FulfilmentcrowdOrderNo'] = $madhatOrderInfo->getFcOrderId();

        return $this->updateFulfillmentCrowdOrderMessage($siteOrderParams);
    }

    private function logOrderFcOrderIdReportToDbLogger(): void
    {
        $message = "Total Order's : " . $this->totalOrders;
        $message .= " | Success Order's : " . $this->successOrders;
        $message .= " | Failed Order's : " . $this->failedOrders;

        if (!empty($this->fcOrderIdExportErrorMessages)) {
            $message .= " | Errors : " . $this->json->serialize($this->fcOrderIdExportErrorMessages);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            'SITE Order Report for FC Order ID Export',
            $message,
            'NOTICE',
            LogIdentifierProvider::ORDER
        );
        $this->emulation->stopEnvironmentEmulation();
    }

    /**
     * @param $order
     * @return void
     *
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    private function updateFcOrderFlag($order): void
    {
        $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$order->getEntityId());
        $madhatOrderInfo->setSiteIsFcOrderSynced(true);

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
    }

    /**
     * Filter orderCollection for fc_order_id export to SITE.
     *
     * @param array $orderIncrementIds
     * @return Collection
     */
    protected function filterOrderCollectionForFcOrderDataExport(array $orderIncrementIds = []): Collection
    {
        $orderCollection = $this->magentoOrderCollectionFactory->create();

        $orderCollection->addFieldToSelect(['increment_id', 'status'])
            ->getSelect()
            ->join(
                ['moi' => $orderCollection->getTable('madhat_order_info')],
                'moi.order_id = main_table.entity_id',
                ['site_order_id', 'fc_order_id', 'site_is_fc_order_id_sync']
            );

        $orderCollection
            ->addFieldToFilter('status', ['nin' => ['canceled', 'complete']])
            ->addFieldToFilter('moi.site_order_id', ['notnull' => true])
            ->addFieldToFilter('moi.fc_order_id', ['notnull' => true])
            ->addFieldToFilter('moi.site_is_fc_order_id_sync', ['null' => true]);

        if (!empty($orderIncrementIds)) {
            $orderCollection->addFieldToFilter('increment_id', ['in' => $orderIncrementIds]);
        }

        // To log site order export sql query (dev debug)
        // Need to test issues on stage01, need to be removed before prod.
        $this->siteOrderLogger->info(
            __(
                'SITE fc_order_id update sql query [%1].',
                $orderCollection->getSelect()->__toString()
            )
        );

        $this->siteOrderLogger->info(
            __(
                'SITE fc_order_id order ids {%1}.',
                implode(', ', $orderCollection->getColumnValues('entity_id'))
            )
        );

        return $orderCollection;
    }

    /**
     * Log failed attempt for SITE Order Export failures
     *
     * @param Order $order
     * @return void
     * @throws CouldNotSaveException
     */
    private function logFailOrderExport(Order $order)
    {
        $orderId = $order->getId();
        try {
            $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId((int)$orderId);
            $exportAttempts = $madhatOrderInfo->getSiteOrderExportAttempts();
            $exportAttempts++;
            $madhatOrderInfo->setSiteOrderExportAttempts($exportAttempts);
        } catch (Exception $e) {
            $madhatOrderInfo = $this->madhatOrderInfoFactory->create();
            $madhatOrderInfo->setOrderId($orderId);
            $madhatOrderInfo->setSiteOrderExportAttempts(1);
        }

        $this->madhatOrderInfoRepository->save($madhatOrderInfo);
    }

    protected function preparePaymentCardType(Order $order): array
    {
        $additionalInformation = $order->getPayment()->getAdditionalInformation();
        $cardType = $additionalInformation['payment_method'] ?? 'unspecified';
        switch ($cardType) {
            case 'visa':
                $cardType = 'Item1';
                break;
            case 'mc':
                $cardType = 'Item2';
                break;
            case 'amex':
                $cardType = 'Item3';
                break;
            case 'diners':
                $cardType = 'Item4';
                break;
            case 'eurocard':
                $cardType = 'Item5';
                break;
            case 'dankort':
                $cardType = 'Item6';
                break;
            case 'switch':
                $cardType = 'Item7';
                break;
            case 'solo':
                $cardType = 'Item8';
                break;
            case 'delta':
                $cardType = 'Item9';
                break;
            case 'fsbkort':
                $cardType = 'Item10';
                break;
            case 'discover':
                $cardType = 'Item11';
                break;
            case 'cartebleue':
                $cardType = 'Item12';
                break;
            case 'cartesbancaires':
                $cardType = 'Item13';
                break;
            case 'paypal':
                $cardType = 'Item71';
                break;
            default:
                $cardType = 'Item99';
                break;
        }
        return ['Value' => $cardType];
    }

    /**
     * @param OrderInterface $order
     * @return bool
     * @throws LocalizedException
     */
    public function directCancelOrder(OrderInterface $order): bool
    {
        $connection = $this->resourceConnection->getConnection();
        $orderId = $order->getEntityId();
        $cancelReason = __($this::CANCEL_REASON);

        try {
            $order->getPayment()->cancel();

            // Start transaction
            $connection->beginTransaction();

            // Get table names
            $orderTable = $this->resourceConnection->getTableName('sales_order');
            $orderGridTable = $this->resourceConnection->getTableName('sales_order_grid');
            $orderItemTable = $this->resourceConnection->getTableName('sales_order_item');
            $historyTable = $this->resourceConnection->getTableName('sales_order_status_history');

            // Update sales_order table
            $connection->update(
                $orderTable,
                [
                    'state' => 'canceled',
                    'status' => 'canceled',
                    'is_virtual' => 0,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                ['entity_id = ?' => $orderId]
            );
            $this->logger->debug("Updated order status to canceled for order #{$orderId}");

            // Update sales_order_grid table
            $connection->update(
                $orderGridTable,
                [
                    'status' => 'canceled',
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                ['entity_id = ?' => $orderId]
            );
            $this->logger->debug("Updated order grid status for order #{$orderId}");

            // Get all order items and update them
            $orderItems = $connection->select()
                ->from($orderItemTable, ['item_id', 'qty_ordered'])
                ->where('order_id = ?', $orderId);

            $items = $connection->fetchAll($orderItems);

            foreach ($items as $item) {
                $connection->update(
                    $orderItemTable,
                    [
                        'qty_canceled' => $item['qty_ordered'],
                        'updated_at' => date('Y-m-d H:i:s')
                    ],
                    ['item_id = ?' => $item['item_id']]
                );
            }
            $this->logger->debug("Updated order items to canceled for order #{$orderId}");

            // Add status history comment
            $connection->insert(
                $historyTable,
                [
                    'parent_id' => $orderId,
                    'entity_name' => 'order',
                    'status' => 'canceled',
                    'comment' => __($cancelReason),
                    'is_customer_notified' => 0,
                    'is_visible_on_front' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            );
            $this->logger->debug("Added status history comment for order #{$orderId}");

            // Commit the transaction
            $connection->commit();

            $this->logger->debug("Order #{$orderId} was directly canceled in the database. Reason: {$cancelReason}");

            return true;
        } catch (\Exception $e) {
            // Rollback transaction in case of error
            $connection->rollBack();
            $this->logger->debug("Failed to cancel order #{$orderId}: " . $e->getMessage(), ['exception' => $e]);
            throw new LocalizedException(__('Could not cancel order #%1: %2', $orderId, $e->getMessage()));
        }
    }

    /**
     * @param int $magentoOrderId
     * @param mixed $OrderStatusData
     * @return void
     */
    private function editOrderTotals(int $magentoOrderId, mixed $OrderStatusData): void
    {
        $this->logger->debug("Starting editOrderTotals for Magento Order ID: {$magentoOrderId}");
        $this->logger->debug("Order Status Data: " . json_encode($OrderStatusData, JSON_PRETTY_PRINT));

        try {
            // Check if OrderStatusData exists in the structure
            if (!isset($OrderStatusData['OrderStatusData'])) {
                $this->logger->debug("OrderStatusData is the main object itself, not a nested key");
                // In this case, the OrderStatusData is the main object
                $orderStatusDataObj = $OrderStatusData;
            } else {
                $this->logger->debug("OrderStatusData is a nested key");
                $orderStatusDataObj = $OrderStatusData['OrderStatusData'];
            }

            // Validate required fields exist
            if (!isset($orderStatusDataObj['AmountGross']) || !isset($orderStatusDataObj['AmountNet']) ||
                !isset($orderStatusDataObj['CurrencyISO']) || !isset($orderStatusDataObj['OrderRowsData'])) {
                $this->logger->error("Missing required fields in OrderStatusData: " . json_encode($orderStatusDataObj));
                throw new \InvalidArgumentException("Missing required fields in OrderStatusData");
            }

            // add recalaculate for base prices
            // If response 'CurrencyISO' same as website currency then base = regular
            // Update order regular amounts
            $order = $this->magentoOrderRepository->get($magentoOrderId);
            $this->logger->debug("Retrieved order #{$magentoOrderId}");

            // Get the store's base currency
            $baseCurrency = $order->getBaseCurrencyCode();
            $orderCurrency = $order->getOrderCurrencyCode();
            $this->logger->debug("Base currency: {$baseCurrency}, Order currency: {$orderCurrency}");

            // Check if we need currency conversion
            $responseCurrency = $orderStatusDataObj['CurrencyISO'];
            $needsConversion = ($baseCurrency !== $responseCurrency);
            $this->logger->debug("Response currency: {$responseCurrency}, Needs conversion: " . ($needsConversion ? 'Yes' : 'No'));

            // Set regular amounts
            $amountGross = $orderStatusDataObj['AmountGross'];
            $amountNet = $orderStatusDataObj['AmountNet'];
            $taxAmount = $amountGross - $amountNet;

            $this->logger->debug("Setting regular amounts - Gross: {$amountGross}, Net: {$amountNet}, Tax: {$taxAmount}");

            $order->setGrandTotal($amountGross);
            //            $order->setSubtotal($amountNet); // in magento used subtotal_incl_tax (autocalculated)
            $order->setTaxAmount($taxAmount);

            // Set base amounts - either equal to regular amounts or converted
            if (!$needsConversion) {
                // Same currency, base amounts equal regular amounts
                $this->logger->debug("Using same values for base amounts (no conversion needed)");

                $order->setBaseGrandTotal($amountGross);
                //                $order->setBaseSubtotal($amountNet); // in magento used subtotal_incl_tax (autocalculated)
                $order->setBaseTaxAmount($taxAmount);
            } else {
                // Different currency, need to convert
                $exchangeRate = $this->getCurrencyExchangeRate($responseCurrency, $baseCurrency);
                $this->logger->debug("Converting with exchange rate: {$exchangeRate} ({$responseCurrency} to {$baseCurrency})");

                $baseGrandTotal = $amountGross * $exchangeRate;
                //                $baseSubtotal = $amountNet * $exchangeRate;
                $baseTaxAmount = $taxAmount * $exchangeRate;

                $this->logger->debug("Calculated base amounts - Grand Total: {$baseGrandTotal}, Tax: {$baseTaxAmount}");

                $order->setBaseGrandTotal($baseGrandTotal);
                //                $order->setBaseSubtotal($baseSubtotal); // in magento used subtotal_incl_tax (autocalculated)
                $order->setBaseTaxAmount($baseTaxAmount);
            }

            $this->logger->debug("Processing order rows. Count: " . count($orderStatusDataObj['OrderRowsData']));

            foreach ($orderStatusDataObj['OrderRowsData'] as $index => $product) {
                $this->logger->debug("Processing row #{$index}: ProductNo: {$product['ProductNo']}, PriceNet: {$product['PriceNet']}, PriceGross: {$product['PriceGross']}");

                if ($product['ProductNo'] == self::DISCOUNT_PRODUCT_NO) {
                    $discountAmount = -1 * $product['PriceNet'];
                    $this->logger->debug("Found discount product. Discount amount: {$discountAmount}");

                    $order->setDiscountAmount($discountAmount);

                    if (!$needsConversion) {
                        $order->setBaseDiscountAmount($discountAmount);
                        $this->logger->debug("Set base discount amount: {$discountAmount} (no conversion)");
                    } else {
                        $baseDiscountAmount = $discountAmount * $exchangeRate;
                        $order->setBaseDiscountAmount($baseDiscountAmount);
                        $this->logger->debug("Set base discount amount: {$baseDiscountAmount} (converted)");
                    }
                }

                if ($product['ProductNo'] == self::SHIPPING_PRODUCT_NO) {
                    $shippingNet = $product['PriceNet'];
                    $shippingGross = $product['PriceGross'];
                    $shippingTax = $shippingGross - $shippingNet;

                    $this->logger->debug("Found shipping product. Net: {$shippingNet}, Gross: {$shippingGross}, Tax: {$shippingTax}");

                    $order->setShippingAmount($shippingNet);
                    $order->setShippingInclTax($shippingGross);
                    $order->setShippingTaxAmount($shippingTax);

                    if (!$needsConversion) {
                        $order->setBaseShippingAmount($shippingNet);
                        $order->setBaseShippingInclTax($shippingGross);
                        $order->setBaseShippingTaxAmount($shippingTax);
                        $this->logger->debug("Set base shipping amounts (no conversion) - Base Net: {$shippingNet}, Base Gross: {$shippingGross}, Base Tax: {$shippingTax}");
                    } else {
                        $baseShippingNet = $shippingNet * $exchangeRate;
                        $baseShippingGross = $shippingGross * $exchangeRate;
                        $baseShippingTax = $shippingTax * $exchangeRate;

                        $order->setBaseShippingAmount($baseShippingNet);
                        $order->setBaseShippingInclTax($baseShippingGross);
                        $order->setBaseShippingTaxAmount($baseShippingTax);
                        $this->logger->debug("Set base shipping amounts (converted) - Base Net: {$baseShippingNet}, Base Gross: {$baseShippingGross}, Base Tax: {$baseShippingTax}");
                    }
                    // @TODO: shipping_discount_amount ?
                }
            }

            $this->logger->debug("Saving order changes for Magento Order ID: {$magentoOrderId}");
            $this->magentoOrderRepository->save($order);
            $this->logger->debug("Order #{$magentoOrderId} successfully updated with new totals");

        } catch (\Exception $e) {
            $this->logger->error("Error in editOrderTotals for order #{$magentoOrderId}: " . $e->getMessage());
            $this->logger->debug("Exception trace: " . $e->getTraceAsString());
            //            throw $e; // Re-throw to maintain original behavior - No because we don't want stop order processing
        }
    }

    /**
     * Get exchange rate between two currencies using Magento's directory/currency model
     *
     * @param string $fromCurrency Currency code to convert from (e.g., 'USD')
     * @param string $toCurrency Currency code to convert to (e.g., 'EUR')
     * @return float The exchange rate
     */
    public function getCurrencyExchangeRate(string $fromCurrency, string $toCurrency): float
    {
        // If currencies are the same, return 1.0 (no conversion needed)
        if ($fromCurrency === $toCurrency) {
            return 1.0;
        }

        try {
            // Get the currency model from Magento
            $currencyModel = $this->currencyFactory->create();

            // Load the target currency
            $currencyModel->load($toCurrency);

            // Get the rate from source to target currency
            $rate = $currencyModel->getAnyRate($fromCurrency);

            // If no rate found, log error and return 1.0
            if (!$rate) {
                $this->logger->error(
                    sprintf(
                        'Currency conversion rate not found: %s to %s. Using rate 1.0',
                        $fromCurrency,
                        $toCurrency
                    )
                );
                return 1.0;
            }

            return (float)$rate;
        } catch (\Exception $e) {
            // Log any exceptions and return default rate
            $this->logger->error(
                sprintf(
                    'Error getting currency rate from %s to %s: %s',
                    $fromCurrency,
                    $toCurrency,
                    $e->getMessage()
                )
            );
            return 1.0;
        }
    }

    /**
     * @param Order $order
     * @param array $newOrderData
     * @param $productsToBeRemoved
     * @return array
     */
    private function removeOrderItems(Order $order, array $newOrderData, $productsToBeRemoved): array
    {
        foreach ($order->getItems() as $item) {
            $itemSku = $item->getProduct()->getSku();
            if (in_array($itemSku, $productsToBeRemoved)) {
                $newOrderData['remove_item'][$item->getItemId()] = $item->getQtyOrdered();
                $this->logger->debug("[Line " . __LINE__ . "] Removing item: {$itemSku}, Item ID: {$item->getItemId()}, Qty: {$item->getQtyOrdered()}");
            }
        }
        return $newOrderData;
    }

    /**
     * @param Order $order
     * @param array $newOrderData
     * @param array $productsToBeAdded
     * @param $siteOrderData
     * @return array
     */
    private function addOrderItems(Order $order, array $newOrderData, array $productsToBeAdded, $siteOrderData): array
    {
        $this->logger->debug("[Line " . __LINE__ . "] addOrderItems() - Started processing with order ID: " . $order->getIncrementId());
        $this->logger->debug("[Line " . __LINE__ . "] Products to be added: " . json_encode($productsToBeAdded));

        if (!empty($productsToBeAdded)) {
            $this->logger->debug("[Line " . __LINE__ . "] Processing " . count($productsToBeAdded) . " products to be added...");

            foreach ($productsToBeAdded as $index => $productSku) {
                $this->logger->debug("[Line " . __LINE__ . "] Processing product #{$index}: {$productSku}");

                try {
                    $this->logger->debug("[Line " . __LINE__ . "] Fetching product from repository by SKU: {$productSku}");
                    $product = $this->productRepository->get($productSku);
                    $productId = $simpleProductId = $product->getId();
                    $productSku = $product->getSku();

                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved product - SKU: {$productSku}, ID: {$productId}, Type: {$product->getTypeId()}, Name: {$product->getName()}");

                    // We need configurable product id
                    $this->logger->debug("[Line " . __LINE__ . "] Checking for parent configurable products for ID: {$productId}");
                    $parentIds = $this->configurableType->getParentIdsByChild($productId);

                    if (!empty($parentIds)) {
                        $this->logger->debug("[Line " . __LINE__ . "] Found " . count($parentIds) . " parent products: " . json_encode($parentIds));

                        foreach ($parentIds as $parentIndex => $parentId) {
                            $this->logger->debug("[Line " . __LINE__ . "] Processing parent #{$parentIndex} with ID: {$parentId}");
                            $parentProduct = $this->productRepository->getById($parentId);
                            $productId = $parentProduct->getId();
                            $this->logger->debug("[Line " . __LINE__ . "] Using parent product - ID: {$productId}, SKU: {$parentProduct->getSku()}, Name: {$parentProduct->getName()}");
                        }
                    } else {
                        $this->logger->debug("[Line " . __LINE__ . "] No parent products found, using simple product ID: {$productId}");
                    }

                    $this->logger->debug("[Line " . __LINE__ . "] Getting quantity from site data for SKU: {$productSku}");
                    $siteQty = (int)$this->getQtyFromSiteData($productSku, $siteOrderData, 1);
                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved quantity: {$siteQty}");

                    $this->logger->debug("[Line " . __LINE__ . "] Getting price from site data for SKU: {$productSku}");
                    $priceNet = (float)$this->getPriceNetFromSiteData($productSku, $siteOrderData);
                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved price: {$priceNet}");

                    $this->logger->debug("[Line " . __LINE__ . "] Getting discount percentage from site data for SKU: {$productSku}");
                    $discountPercentage = (float)$this->getDiscountPercentageFromSiteData($productSku, $siteOrderData);
                    $this->logger->debug("[Line " . __LINE__ . "] Retrieved discount percentage: {$discountPercentage}");

                    $this->logger->debug("[Line " . __LINE__ . "] Setting up new order data - adding simple product ID: {$simpleProductId}");
                    $newOrderData['add_item'] = [$simpleProductId => $simpleProductId];

                    $this->logger->debug("[Line " . __LINE__ . "] Setting price-excl-tax for product ID: {$productId} to {$priceNet}");
                    $newOrderData['price-excl-tax']['preview-' . $productId] = $priceNet;

                    $this->logger->debug("[Line " . __LINE__ . "] Setting ordered quantity for product ID: {$productId} to {$siteQty}");
                    $newOrderData['col-ordered-qty']['preview-' . $productId] = $siteQty;

                    $this->logger->debug("[Line " . __LINE__ . "] Setting discount percentage for product ID: {$productId} to {$discountPercentage}");
                    $newOrderData['col-discont']['preview-' . $productId] = $discountPercentage;

                    $this->logger->debug("[Line " . __LINE__ . "] Successfully added new product: {$productSku}, Product ID: {$productId}, Simple ID: {$simpleProductId}, Qty: {$siteQty}, Price: {$priceNet}, Discount: {$discountPercentage}%");
                } catch (\Exception $e) {
                    $this->logger->error("[Line " . __LINE__ . "] Error adding product {$productSku}: " . $e->getMessage());
                    $this->logger->error("[Line " . __LINE__ . "] Exception trace: " . $e->getTraceAsString());
                }
            }

            $this->logger->debug("[Line " . __LINE__ . "] Finished processing all products");
        } else {
            $this->logger->debug("[Line " . __LINE__ . "] No products to be added, skipping product processing");
        }

        $this->logger->debug("[Line " . __LINE__ . "] addOrderItems() - Completed, returning updated order data");

        return $newOrderData;
    }

    /**
     * @param array $newOrderData
     * @param array $productsToBeModified
     * @return array
     */
    private function modifyOrderItems(array $newOrderData, array $productsToBeModified): array
    {
        // Modify Item
        foreach ($productsToBeModified as $ptbmItemId => $productData) {
            $newOrderData['price-excl-tax'][$ptbmItemId] = $productData['price-excl-tax'];
            $newOrderData['col-ordered-qty'][$ptbmItemId] = $productData['new_qty'];
            $newOrderData['col-discont'][$ptbmItemId] = $productData['col-discont'];

            $this->logger->debug("[Line " . __LINE__ . "] Update order item: {$ptbmItemId}, Product Data:"
                . json_encode($productData)
                . "New Product Data:" . json_encode($newOrderData));
        }
        $this->logger->debug("[Line " . __LINE__ . "] Modified Items array: " . var_export($newOrderData, true));
        return $newOrderData;
    }

    /**
     * @param Order $order
     * @param array $productsToBeRemoved
     * @param array $siteOrderData
     * @return array
     */
    private function getProductsToBeModified(Order $order, array $productsToBeRemoved, array $siteOrderData): array
    {
        // Initialize the array for products that need quantity modification
        $productsToBeModified = [];

        // Check for quantity differences between Magento order items and site order data
        foreach ($order->getItems() as $item) {
            if ($item->getProductType() == 'configurable') {
                continue;
            }
            $itemSku = $item->getProduct()->getSku();
            // Skip items that are already being removed
            if (in_array($itemSku, $productsToBeRemoved)) {
                continue;
            }

            $itemId = $item->getItemId();
            if (!empty($item->getParentItemId())) {
                $itemId = $item->getParentItemId();
            }
            $productId = $item->getProductId();
            $currentQty = (int)$item->getQtyOrdered();
            $currentPrice = (float)$this->getPriceFromMagentoData($item);
            $currentDiscountPercentage = (float)$item->getDiscountPercent();
            $siteQty = (int)$this->getQtyFromSiteData($itemSku, $siteOrderData, $currentQty);
            $priceNet = (float)$this->getPriceNetFromSiteData($itemSku, $siteOrderData);
            $priceGross = (float)$this->getPriceGrossFromSiteData($itemSku, $siteOrderData);
            $discountPercentage = (float)$this->getDiscountPercentageFromSiteData($itemSku, $siteOrderData);

            // If the quantity/price/discount is different, add to the array of products to be modified
            if (
                ($currentQty !== $siteQty) ||
                ($currentPrice !== $priceNet) ||
                ($currentDiscountPercentage !== $discountPercentage)) {
                $productsToBeModified[$itemId] = [
                    'product_id' => $productId,
                    'sku' => $itemSku,
                    'current_qty' => $currentQty,
                    'new_qty' => $siteQty,
                    'price-excl-tax' => $priceNet,
                    'price-incl-tax' => $priceGross,
                    'current_price' => $currentPrice,
                    'col-discont' => $discountPercentage,
                    'current-discount-percent' => $currentDiscountPercentage,
                ];
                $this->logger->debug("[Line " . __LINE__ . "] Changes for item SKU: {$itemSku}, Product ID: {$productId}, Item ID: {$itemId}, Old Qty: {$currentQty}, New Qty: {$siteQty}, Old Price: {$currentPrice}, New Price(PriceNet): {$priceNet}, (PriceGross){$priceGross} Old discount: {$currentDiscountPercentage}, New discount(DiscountPercentage): {$discountPercentage}");
            }
        }
        $this->logger->debug("[Line " . __LINE__ . "] List of products to be modified: " . var_export($productsToBeModified, true));

        return $productsToBeModified;
    }

    /**
     * Fix discount for existing products by updating discount_percent, discount_amount,
     * and base_discount_amount in sales_order_item table via direct SQL
     *
     * @param Order $order
     * @param array $siteOrderData
     * @param array $newOrderData
     * @return void
     */
    private function fixDiscountForExistProducts(Order $order, array $siteOrderData, array $skuForFixFunctions): void
    {
        $this->logger->info(
            sprintf(
                "Starting discount fix for order #%s with %d items",
                $order->getId(),
                count($order->getAllItems())
            )
        );

        try {
            $connection = $this->resourceConnection->getConnection();
            $orderItemTable = $this->resourceConnection->getTableName('sales_order_item');

            // Begin transaction
            $connection->beginTransaction();
            $this->logger->debug("Transaction started for order #{$order->getId()}");

            // Get all order items (including configurable products)
            $orderItems = $order->getAllItems();
            $this->logger->debug(
                sprintf(
                    "Processing %d order items for order #%s",
                    count($orderItems),
                    $order->getId()
                )
            );

            $itemsProcessed = 0;
            $itemsUpdated = 0;

            foreach ($orderItems as $item) {
                $sku = $item->getSku();
                if (!in_array($sku, $skuForFixFunctions)) {
                    continue;
                }
                $itemId = $item->getItemId();
                $itemsProcessed++;

                $this->logger->debug(
                    sprintf(
                        "Processing item %d/%d: ID %s, SKU %s, Type %s",
                        $itemsProcessed,
                        count($orderItems),
                        $itemId,
                        $sku,
                        $item->getProductType()
                    )
                );

                // Find matching item in siteOrderData
                $matchFound = false;
                foreach ($siteOrderData['OrderRowsData'] as $index => $siteItem) {
                    if ($siteItem['ProductNo'] === $sku) {
                        $matchFound = true;
                        $this->logger->debug(
                            sprintf(
                                "Match found for SKU %s in siteOrderData at index %d",
                                $sku,
                                $index
                            )
                        );

                        // Get discount percentage from site data
                        $discountPercent = (float)($siteItem['DiscountPercentage'] ?? 0);

                        // Get PriceNet from site data, fallback to Magento price
                        $priceNet = (float)($siteItem['PriceNet'] ?? $item->getPrice());
                        $this->logger->debug(
                            sprintf(
                                "Item %s: Using price %s (site data: %s, Magento price: %s)",
                                $itemId,
                                $priceNet,
                                $siteItem['PriceNet'] ?? 'N/A',
                                $item->getPrice()
                            )
                        );

                        // Calculate discount amount
                        $qtyOrdered = $item->getQtyOrdered();
                        $discountAmount = $priceNet * ($discountPercent / 100) * $qtyOrdered;
                        $baseDiscountAmount = $discountAmount; // Assuming base currency same as order currency

                        $this->logger->debug(
                            sprintf(
                                "Item %s: Initial calculation - Price: %s, Discount%%: %s, Qty: %s, Discount Amount: %s",
                                $itemId,
                                $priceNet,
                                $discountPercent,
                                $qtyOrdered,
                                $discountAmount
                            )
                        );

                        // If currencies differ, convert base amount
                        $orderCurrency = $order->getOrderCurrencyCode();
                        $baseCurrency = $order->getBaseCurrencyCode();

                        if ($orderCurrency !== $baseCurrency) {
                            $this->logger->debug(
                                sprintf(
                                    "Currency conversion needed: %s to %s for item %s",
                                    $orderCurrency,
                                    $baseCurrency,
                                    $itemId
                                )
                            );

                            $exchangeRate = $this->getCurrencyExchangeRate($orderCurrency, $baseCurrency);
                            $baseDiscountAmount = $discountAmount * $exchangeRate;

                            $this->logger->debug(
                                sprintf(
                                    "Exchange rate: %s, Converted base discount amount: %s",
                                    $exchangeRate,
                                    $baseDiscountAmount
                                )
                            );
                        }

                        // Prepare update data
                        $updateData = [
                            'discount_percent' => $discountPercent,
                            'discount_amount' => $discountAmount,
                            'base_discount_amount' => $baseDiscountAmount,
                            'updated_at' => date('Y-m-d H:i:s')
                        ];

                        // Log original values before update
                        $this->logger->debug(
                            sprintf(
                                "Item %s: Original values - Discount%%: %s, Amount: %s, Base Amount: %s",
                                $itemId,
                                $item->getDiscountPercent(),
                                $item->getDiscountAmount(),
                                $item->getBaseDiscountAmount()
                            )
                        );

                        // Update sales_order_item table
                        $affectedRows = $connection->update(
                            $orderItemTable,
                            $updateData,
                            ['item_id = ?' => $itemId]
                        );

                        if ($affectedRows) {
                            $itemsUpdated++;
                            $this->logger->info(
                                sprintf(
                                    "Updated discount for item %s (SKU: %s, Type: %s) - Percent: %s, Amount: %s, Base Amount: %s",
                                    $itemId,
                                    $sku,
                                    $item->getProductType(),
                                    $discountPercent,
                                    $discountAmount,
                                    $baseDiscountAmount
                                )
                            );
                        } else {
                            $this->logger->warning(
                                sprintf(
                                    "Update query returned 0 affected rows for item %s (SKU: %s)",
                                    $itemId,
                                    $sku
                                )
                            );
                        }

                        break; // Found matching item, move to next order item
                    }
                }

                if (!$matchFound) {
                    $this->logger->warning(
                        sprintf(
                            "No matching SKU found in siteOrderData for item %s (SKU: %s, Type: %s)",
                            $itemId,
                            $sku,
                            $item->getProductType()
                        )
                    );
                }
            }

            // Commit transaction
            $connection->commit();
            $this->logger->info(
                sprintf(
                    "Successfully updated discounts for order #%s: %d/%d items updated",
                    $order->getId(),
                    $itemsUpdated,
                    $itemsProcessed
                )
            );
        } catch (\Exception $e) {
            // Rollback transaction on error
            if (isset($connection)) {
                $connection->rollBack();
                $this->logger->debug("Transaction rolled back for order #{$order->getId()}");
            }

            $this->logger->error(
                sprintf(
                    "Failed to update discounts for order #%s: %s\nStack trace: %s",
                    $order->getId(),
                    $e->getMessage(),
                    $e->getTraceAsString()
                )
            );
        }
    }

    /**
     * Set original_price and base_original_price in sales_order_item table via direct SQL,
     * mimicking Magento's behavior on order creation by using catalog prices
     *
     * @param Order $order
     * @param array $skuForFixFunctions
     * @return void
     */
    private function setOriginalPricesForOrderItems(Order $order, array $skuForFixFunctions): void
    {
        $this->logger->debug("Starting setOriginalPricesForOrderItems for Order ID: {$order->getId()}");

        try {
            // Get database connection
            $connection = $this->resourceConnection->getConnection();
            $orderItemTable = $this->resourceConnection->getTableName('sales_order_item');

            // Begin transaction
            $connection->beginTransaction();
            $this->logger->debug("Transaction started for order #{$order->getId()}");

            // Get all order items
            $orderItems = $order->getAllItems();
            $this->logger->debug(
                sprintf(
                    "Processing %d order items for order #%s",
                    count($orderItems),
                    $order->getId()
                )
            );

            $itemsUpdated = 0;

            foreach ($orderItems as $item) {
                $itemId = $item->getItemId();
                $sku = $item->getSku();
                if (!in_array($sku, $skuForFixFunctions)) {
                    continue;
                }
                try {
                    // Get the product from repository
                    $product = $this->productRepository->get($sku);

                    // Get product price from catalog
                    $catalogPrice = $product->getPrice();

                    // Determine if we need currency conversion
                    $orderCurrency = $order->getOrderCurrencyCode();
                    $baseCurrency = $order->getBaseCurrencyCode();
                    $needsConversion = ($orderCurrency !== $baseCurrency);

                    // Set original price
                    $originalPrice = $catalogPrice;
                    $baseOriginalPrice = $catalogPrice;

                    // If currencies differ, convert base amount
                    if ($needsConversion) {
                        $this->logger->debug(
                            sprintf(
                                "Currency conversion needed: %s to %s for item %s",
                                $orderCurrency,
                                $baseCurrency,
                                $itemId
                            )
                        );

                        $exchangeRate = $this->getCurrencyExchangeRate($orderCurrency, $baseCurrency);
                        $baseOriginalPrice = $originalPrice * $exchangeRate;

                        $this->logger->debug(
                            sprintf(
                                "Exchange rate: %s, Converted base original price: %s",
                                $exchangeRate,
                                $baseOriginalPrice
                            )
                        );
                    }

                    // Prepare update data
                    $updateData = [
                        'original_price' => $originalPrice,
                        'base_original_price' => $baseOriginalPrice,
                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    // Log values before update
                    $this->logger->debug(
                        sprintf(
                            "Item %s (SKU: %s): Setting original_price: %s, base_original_price: %s",
                            $itemId,
                            $sku,
                            $originalPrice,
                            $baseOriginalPrice
                        )
                    );

                    // Update sales_order_item table
                    $affectedRows = $connection->update(
                        $orderItemTable,
                        $updateData,
                        ['item_id = ?' => $itemId]
                    );

                    if ($affectedRows) {
                        $itemsUpdated++;
                        $this->logger->debug(
                            sprintf(
                                "Updated original prices for item %s (SKU: %s)",
                                $itemId,
                                $sku
                            )
                        );
                    } else {
                        $this->logger->warning(
                            sprintf(
                                "Update query returned 0 affected rows for item %s (SKU: %s)",
                                $itemId,
                                $sku
                            )
                        );
                    }

                } catch (\Exception $e) {
                    $this->logger->warning(
                        sprintf(
                            "Failed to update original prices for item %s (SKU: %s): %s",
                            $itemId,
                            $sku,
                            $e->getMessage()
                        )
                    );
                    // Continue with other items even if one fails
                    continue;
                }
            }

            // Commit transaction
            $connection->commit();
            $this->logger->info(
                sprintf(
                    "Successfully updated original prices for order #%s: %d items updated",
                    $order->getId(),
                    $itemsUpdated
                )
            );

        } catch (\Exception $e) {
            // Rollback transaction on error
            if (isset($connection)) {
                $connection->rollBack();
                $this->logger->debug("Transaction rolled back for order #{$order->getId()}");
            }

            $this->logger->error(
                sprintf(
                    "Failed to update original prices for order #%s: %s\nStack trace: %s",
                    $order->getId(),
                    $e->getMessage(),
                    $e->getTraceAsString()
                )
            );
        }
    }

    /**
     * @param array $productsToBeAdded
     * @param array $productsToBeModified
     * @return array
     */
    private function getSkuForFixFunctions(array $productsToBeAdded, array $productsToBeModified): array
    {
        $skuForFixFunctions = [];
        if(!empty($productsToBeAdded)) {
            foreach ($productsToBeAdded as $sku) {
                $skuForFixFunctions[] = $sku;
            }
        }
        if(!empty($productsToBeModified)) {
            foreach ($productsToBeModified as $item) {
                $skuForFixFunctions[] = $item['sku'];
            }
        }
        return $skuForFixFunctions;
    }

    /**
     * Prepares the customer invoice email data based on the provided order.
     *
     * @param Order $order The order object used to retrieve customer information.
     *
     * @return string
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    private function prepareCustomerInvoiceMail(Order $order): string
    {
        $customerEmail = $order->getCustomerEmail();
        $customer = $this->customerRepository->get($customerEmail);

        $invoiceEmail = '';
        if ($customer->getCustomAttribute('invoice_email')) {
            $invoiceEmail = $customer->getCustomAttribute('invoice_email')->getValue();
        }

        return $invoiceEmail;
    }
}

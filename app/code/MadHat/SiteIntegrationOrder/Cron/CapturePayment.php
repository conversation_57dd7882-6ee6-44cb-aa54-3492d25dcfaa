<?php

declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\InvoiceProcessor;

class CapturePayment
{
    /**
     * @var Data
     */
    private Data $siteOrderHelper;

    /**
     * @var InvoiceProcessor
     */
    private InvoiceProcessor $invoiceProcessor;

    /**
     * @param Data $siteOrderHelper
     * @param InvoiceProcessor $invoiceProcessor
     */
    public function __construct(
        Data         $siteOrderHelper,
        InvoiceProcessor $invoiceProcessor
    ) {
        $this->siteOrderHelper = $siteOrderHelper;
        $this->invoiceProcessor = $invoiceProcessor;
    }

    /**
     * Export Magento orders to SITE
     *
     * @return void
     */
    public function execute(): void
    {
        if ($this->siteOrderHelper->getIsEnabled()) {
            $this->invoiceProcessor->capturePayments();
        }
    }
}

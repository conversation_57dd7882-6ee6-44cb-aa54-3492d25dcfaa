<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;
use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\Email\OrderExportReportSender;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;

class NotifySiteExportReport
{
    /**
     * @var Data
     */
    private Data $siteOrderHelper;

    /**
     * @var OrderExportReportSender
     */
    private OrderExportReportSender $orderExportReportSender;

    /**
     * @var SiteCoreHelper
     */
    private SiteCoreHelper $siteCoreHelper;

    /**
     * @param Data $siteOrderHelper
     * @param OrderExportReportSender $orderExportReportSender
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        Data $siteOrderHelper,
        OrderExportReportSender $orderExportReportSender,
        SiteCoreHelper $siteCoreHelper
    ) {
        $this->siteOrderHelper = $siteOrderHelper;
        $this->orderExportReportSender = $orderExportReportSender;
        $this->siteCoreHelper = $siteCoreHelper;
    }

    /**
     * Send SITE Export Report
     *
     * @return void
     * @throws CouldNotSaveException
     * @throws LocalizedException
     */
    public function execute(): void
    {
        if ($this->siteOrderHelper->getIsEnabled()) {
            $this->orderExportReportSender->sendOrderExportFailNotificationMail();
        }
    }
}

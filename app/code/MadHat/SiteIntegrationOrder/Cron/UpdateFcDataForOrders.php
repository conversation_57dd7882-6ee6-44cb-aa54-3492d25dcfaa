<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\Api\SiteOrderApi;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

class UpdateFcDataForOrders
{
    /**
     * @var Data
     */
    private Data $siteOrderHelper;

    /**
     * @var SiteOrderApi
     */
    private SiteOrderApi $siteOrderApi;

    /**
     * @param Data $siteOrderHelper
     * @param SiteOrderApi $siteOrderApi
     */
    public function __construct(
        Data         $siteOrderHelper,
        SiteOrderApi $siteOrderApi
    ) {
        $this->siteOrderHelper = $siteOrderHelper;
        $this->siteOrderApi = $siteOrderApi;
    }

    /**
     * Export Magento orders to SITE
     *
     * @return void
     * @throws CouldNotSaveException
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function execute(): void
    {
        if ($this->siteOrderHelper->getIsEnabled()) {
            $this->siteOrderApi->exportFcOrderData();
        }
    }
}

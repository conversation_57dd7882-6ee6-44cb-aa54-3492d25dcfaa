<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationOrder\Cron;

use MadHat\SiteIntegrationCore\Helper\Data as SiteCoreHelper;
use MadHat\SiteIntegrationOrder\Helper\Data;
use MadHat\SiteIntegrationOrder\Model\Api\SiteOrderApi;
use Magento\Framework\Exception\CouldNotSaveException;

class ExportOrders
{
    /**
     * @var Data
     */
    private Data $madhatSiteOrderHelper;

    /**
     * @var SiteOrderApi
     */
    private SiteOrderApi $siteOrderApi;

    /**
     * @param Data $madhatSiteOrderHelper
     * @param SiteOrderApi $siteOrderApi
     * @param SiteCoreHelper $siteCoreHelper
     */
    public function __construct(
        Data                          $madhatSiteOrderHelper,
        SiteOrderApi                  $siteOrderApi,
        SiteCoreHelper                $siteCoreHelper
    ) {
        $this->madhatSiteOrderHelper = $madhatSiteOrderHelper;
        $this->siteOrderApi = $siteOrderApi;
    }

    /**
     * Export Magento orders to SITE
     *
     * @return void
     * @throws CouldNotSaveException
     */
    public function execute(): void
    {
        if ($this->madhatSiteOrderHelper->getIsEnabled()) {
            $this->siteOrderApi->exportSiteOrders();
        }
    }
}

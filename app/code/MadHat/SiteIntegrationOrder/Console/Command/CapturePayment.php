<?php
declare(strict_types=1);

namespace Madhat\SiteIntegrationOrder\Console\Command;

use MadHat\SiteIntegrationOrder\Helper\Data as MadhatSiteOrderHelper;
use MadHat\SiteIntegrationOrder\Model\InvoiceProcessor;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use Magento\Framework\Exception\LocalizedException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class CapturePayment extends Command
{
    const INPUT_KEY_ORDER_IDS = 'order_ids';

    /**
     * @param MadhatSiteOrderHelper $madhatSiteOrderHelper
     * @param InvoiceProcessor $invoiceProcessor
     * @param State $appState
     * @param string|null $name
     */
    public function __construct(
        MadhatSiteOrderHelper $madhatSiteOrderHelper,
        InvoiceProcessor      $invoiceProcessor,
        State                 $appState,
        string                $name = null
    )
    {
        parent::__construct($name);
        $this->madhatSiteOrderHelper = $madhatSiteOrderHelper;
        $this->invoiceProcessor = $invoiceProcessor;
        $this->appState = $appState;
    }

    /**
     * @inheritDoc
     */
    protected function configure(): void
    {
        $this->setName("madhat:magento:capture-payment");
        $this->setDescription("Capture payment for Magento orders");
        $this->setDefinition([
            new InputArgument(
                self::INPUT_KEY_ORDER_IDS,
                InputArgument::OPTIONAL | InputArgument::IS_ARRAY,
                "Space-separated list of Magento Increment Id's"
            )
        ]);
        parent::configure();
    }

    /**
     * Sync Order Status from SITE to Magento 2
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     * @throws LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);

        if (!$this->madhatSiteOrderHelper->getIsEnabled()) {
            $output->writeln(
                __("MadHat Site Order Integration is disabled. Please enable it from system configuration.")
            );
            return Cli::RETURN_FAILURE;
        }
        $orderIncrementIds = $input->getArgument(self::INPUT_KEY_ORDER_IDS);

        $orderSyncResult = $this->invoiceProcessor->capturePayments($orderIncrementIds);

        if (isset($orderSyncResult['error_messages']) && count($orderSyncResult['error_messages'])) {
            foreach ($orderSyncResult['error_messages'] as $errorMessage) {
                $output->writeln($errorMessage);
            }
        }

        $output->writeln($orderSyncResult['message']);
        if ($orderSyncResult['success']) {
            return Cli::RETURN_SUCCESS;
        }
        return Cli::RETURN_FAILURE;
    }
}

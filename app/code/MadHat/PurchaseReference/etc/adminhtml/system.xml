<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd"
>
    <system>
        <section id="hyva_themes_checkout">
            <group id="component">
                <group id="purchase_reference" translate="label" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Purchase Reference</label>
                    <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                        <label>Enable</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>

                    <field id="placeholder" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                        <label>Placeholder</label>
                        <comment>The text will be displayed within the textarea, serving as an example or description to guide customers on what they can optionally write.</comment>
                        <depends>
                            <field id="hyva_themes_checkout/component/purchase_reference/enable">1</field>
                        </depends>
                    </field>
                </group>
            </group>
        </section>
    </system>
</config>

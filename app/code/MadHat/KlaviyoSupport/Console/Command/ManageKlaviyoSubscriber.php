<?php

namespace MadHat\KlaviyoSupport\Console\Command;

use MadHat\KlaviyoSupport\Model\KlaviyoApi;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ManageKlaviyoSubscriber extends Command
{
    private $klaviyoApi;
    public function __construct(
        KlaviyoApi $klaviyoApi,
        ?string $name = null
    ) {
        parent::__construct($name);
        $this->klaviyoApi = $klaviyoApi;
    }

    /**
     * Initialization of the command.
     */
    protected function configure()
    {
        $this->setName('madhat:klaviyo:syncsubscriber');
        $this->setDescription('This command will fetch profiles having subscription flag. If it is changed than subscribed, then it will unsubscribe that customer in M2.');
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $updateCount = $this->klaviyoApi->process();
        $output->writeln('<info> Updated : '.$updateCount.'</info>');
        return 0;
    }
}

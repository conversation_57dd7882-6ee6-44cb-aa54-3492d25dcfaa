<?php

declare(strict_types=1);

use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\Media;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use MadHat\Banners\Model\Source\BannerPosition;

/** @var ViewModelRegistry $viewModels */
/** @var \Magefan\Blog\Block\Widget\Recent $block */
/** @var Escaper $escaper */
/** @var \MadHat\Banners\Block\Banners $block */

$bannerId = [BannerPosition::BRAND_SLIDER];
$banners  = $block->getFrontBanners($bannerId);

$imageWidth = $block->getData('image_width') ?? 96;
$imageHeight = $block->getData('image_Height') ?? 54;
$mediaUrl = $viewModels->require(Media::class)->getMediaUrl();

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$itemIndex = 1;
?>

<script>
    function carousel_brand() {
        return {
            container: null,
            prev: null,
            next: null,
            first: null,
            visibleElements: null,
            showNav: false,
            speed: 3000, // set to 0 to disable
            slidePage: false, // set to false for sliding one item at the time, true for the whole visible set
            sliderTimer: null,
            init() {
                this.container = this.$refs.container
                this.first = this.container.firstElementChild;

                this.update();
                if (this.next) {
                    this.showNav = true;
                }
                this.container.addEventListener('scroll', this.update.bind(this), {passive: true});
                if(this.speed > 0){
                    this.start();
                }
            },
            start() {
                this.sliderTimer = setInterval(() => this.scrollToNextOrFirst() , this.speed);
            },
            stop() {
                clearInterval(this.sliderTimer);
            },
            update() {
                const rect = this.container.getBoundingClientRect();

                this.visibleElements = Array.from(this.container.children).filter((child) => {
                    const childRect = child.getBoundingClientRect();
                    return childRect.left >= Math.floor(rect.left) && Math.floor(childRect.right) <= rect.right;
                });

                if (this.visibleElements.length > 0) {
                    this.prev = this.getPrevElement();
                    this.next = this.getNextElement();
                }
            },
            getPrevElement() {
                const sibling = this.visibleElements[0].previousElementSibling;
                return (sibling instanceof HTMLElement) ? sibling : null;
            },
            getNextElement() {
                const list = this.visibleElements;
                const lastElementIsVisible = !(list[list.length - 1].nextElementSibling instanceof HTMLElement);

                const sibling = (this.slidePage)
                    ? list[list.length - 1].nextElementSibling
                    : ((lastElementIsVisible)
                        ? null
                        : list[0].nextElementSibling);

                return (sibling instanceof HTMLElement) ? sibling : null;
            },
            scrollToNextOrFirst() {
                (this.next !== null)? this.scrollTo(this.next) : this.scrollTo(this.first);
            },
            scrollToPreviousOrFirst() {
                (this.prev !== null)? this.scrollTo(this.prev) : this.scrollTo(this.first);
            },
            scrollTo(element) {
                const current = this.container;
                if (!current || !element) return;
                const nextScrollPosition = element.offsetLeft;

                current.scroll({
                    left: nextScrollPosition,
                    behavior: 'smooth',
                });
            }
        };
    }
</script>
<?php $itemCount = count($banners); ?>
<h3 class="section-heading cmsp2-text-xl cmsp2-border-b cmsp2-pb-1 cmsp2-my-4 cmsp2-font-semibold"><?= __('Our brands') ?></h3>
<div class="carousel_brand mb-4">

    <span class="bg-cgrey-50 text-corange"></span>

    <div class="flex mx-auto items-center">
        <div x-data="carousel_brand()"
             x-id="['carousel-end', 'carousel-desc']"
             role="group"
             class="relative overflow-hidden min-w-full"
             aria-roledescription="<?= $escaper->escapeHtml(__('Carousel')) ?>"
             aria-label="<?= $escaper->escapeHtml(__('Our favourite brands')) ?>"
             :aria-describedby="$id('carousel-desc')"
        >
            <span
                    class="sr-only"
                    :id="$id('carousel-desc')"
                    tabindex="-1"
            >
                <?= $escaper->escapeHtml(__('Navigating through the elements of the carousel is possible using the tab key. You can skip the carousel using the skip link.')) ?>
            </span>
            <a
                    :href="`#${$id('carousel-end')}`"
                    class="action skip sr-only focus:not-sr-only focus:absolute focus:z-30 focus:bg-white"
            >
                <?= $escaper->escapeHtml(__('Press to skip the carousel')) ?>
            </a>
            <div x-ref="container" class="snap gap-3 flex overflow-x-scroll">
                <?php foreach ($banners as $banner): ?>
                    <div class="slide brand-slider-container"
                        role="navigation"
                         aria-roledescription="<?= $escaper->escapeHtml(__('Carousel item')) ?>"
                         :aria-hidden="!visibleElements.includes($el)"
                         aria-label="<?= $escaper->escapeHtmlAttr(__('Item %1 of %2', $itemIndex++, $itemCount)) ?>"
                         @focusin="stop()"
                    >
                        <?php $url = $banner->getLink()  ?>
                        <?php if ($url ?? false): ?>
                            <a href="<?= $escaper->escapeUrl($block->getUrl($url))?>" aria-label="<?= $banner->getTitle() ?>">
                        <?php endif; ?>

                        <?php if ($item['svg'] ?? false): ?>
                            <?= $item['svg'] ?>
                        <?php elseif ($banner->getBannerimage() ?? false): ?>
                            <img src="<?= $escaper->escapeHtmlAttr($mediaUrl . $banner->getBannerimage()) ?>"
                                 alt="<?= $banner->getTitle() ?>"
                                 width="<?= $escaper->escapeHtmlAttr($banner->getImageWidth() ?? $imageWidth) ?>"
                                 height="<?= $escaper->escapeHtmlAttr($banner->getImageHeight() ?? $imageHeight) ?>"
                                 loading="lazy"
                            />
                        <?php endif; ?>

                        <?php if ($url ?? false): ?>
                            </a>
                        <?php endif; ?>

                    </div>
                <?php endforeach; ?>
            </div>
            <?php $sliderShowNav = true;
            $sliderShowArrows = true;
            $sliderShowDots = false;
             ?>
            <?php if ($sliderShowNav): ?>
                <template x-if="showNav">
                    <nav
                        aria-label="<?= $escaper->escapeHtml(__('Slider controls')) ?>"
                        :id="$id('slider-nav')"
                    >
                        <?php if ($sliderShowArrows): ?>
                            <!-- <div class="absolute top-1/2 -translate-y-1/2 flex justify-between w-full"> -->
                            <div class="home-brand-slide-nav">
                                <button
                                    role="button"
                                    aria-label="<?= $escaper->escapeHtml(__('Previous slide')) ?>"
                                    class="previous-button text-cgrey-0 text-gray-500"
                                    @click="scrollToPreviousOrFirst"
                                >
                                    <?= $heroicons->chevronLeftHtml("w-6 h-6", 64, 64, ['aria-hidden' => 'true']) ?>
                                </button>

                                <button
                                    role="button"
                                    aria-label="<?= $escaper->escapeHtml(__('Next slide')) ?>"
                                    class="next-button text-cgrey-0 text-gray-500"
                                    @click="scrollToNextOrFirst"
                                >
                                    <?= $heroicons->chevronRightHtml("w-6 h-6", 64, 64, ['aria-hidden' => 'true']) ?>
                                </button>
                            </div>
                        <?php endif; ?>
                    </nav>
                </template>
            <?php endif; ?>

            <span :id="$id('carousel-end')" tabindex="-1"></span>
        </div>
    </div>
</div>

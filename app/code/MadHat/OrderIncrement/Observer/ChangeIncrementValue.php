<?php

declare(strict_types=1);

namespace MadHat\OrderIncrement\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Model\Quote;
use Magento\Sales\Model\Order;

class ChangeIncrementValue implements ObserverInterface
{
    /**
     * Ensure guest customer name data exists in both quote and order.
     */
    public function execute(Observer $observer): void
    {
        /** @var Quote $quote */
        $quote = $observer->getData('quote');
        /** @var Order $order */
        $order = $observer->getData('order');

        if ($quote->getStoreId() == 1) {
            $newIncrementId = $quote->getStoreId() . $quote->getReservedOrderId();
        } else {
            $newIncrementId = $quote->getReservedOrderId();
        }
        $order->setIncrementId($newIncrementId);
    }
}

<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="madhat_portal_mapping" resource="default" comment="List of available campaigns">
        <column xsi:type="int" name="entity_id" identity="true" comment="Id"/>
        <column xsi:type="int" name="portal_id" unsigned="true" nullable="false" identity="false"
                default="0" comment="Portal ID"/>
        <column xsi:type="smallint" name="store_id" unsigned="true" nullable="false" identity="false"
                default="0" comment="Store ID"/>
        <column xsi:type="varchar" name="currency_code" nullable="false" length="10" comment="currency code"/>
        <column xsi:type="datetime" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="MADHAT_PORTAL_ENTITY_ID_STORE_ID" table="madhat_portal_mapping"
                    column="store_id" referenceTable="store" referenceColumn="store_id" onDelete="CASCADE"/>
    </table>
</schema>

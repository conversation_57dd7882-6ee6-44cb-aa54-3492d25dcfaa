<?php
/**
 * MadHat_PortalMapping extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_PortalMapping
 * @copyright Copyright (c) 2021
 **/

namespace MadHat\PortalMapping\Logger;

class Handler extends \Magento\Framework\Logger\Handler\Base
{
    /**
     * Logging level.
     *
     * @var int
     */
    public $loggerType = Logger::INFO;

    /**
     * File name.
     *
     * @var string
     */
    public $fileName = '/var/log/portalmapping.log';
}

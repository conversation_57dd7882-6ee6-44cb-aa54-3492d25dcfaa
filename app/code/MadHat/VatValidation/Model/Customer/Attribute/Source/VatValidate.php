<?php

namespace MadHat\VatValidation\Model\Customer\Attribute\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Magento\Framework\Data\OptionSourceInterface;

class VatValidate extends AbstractSource implements OptionSourceInterface
{
    const VATVALIDATE_NONE = 0;
    const VATVALIDATE_VALID = 1;
    const VATVALIDATE_INVALID = 2;

    public function getAllOptions()
    {
        return [
            ['value' => self::VATVALIDATE_NONE, 'label' => __('None')],
            ['value' => self::VATVALIDATE_VALID, 'label' => __('VAT Valid')],
            ['value' => self::VATVALIDATE_INVALID, 'label' => __('VAT Invalid')],
        ];
    }
}

<?php
declare(strict_types=1);

namespace MadHat\MiniCart\Plugin\Checkout\Magewire\Checkout\PriceSummary;

use Hyva\Checkout\Magewire\Checkout\PriceSummary\CartItems as HyvaCheckoutCartItems;

class CartItems
{
    /**
     * Sort cart items by item_id (newest first) after getQuoteItemData() is executed.
     *
     * @param HyvaCheckoutCartItems $subject
     * @param array|null $items
     * @return array|null
     */
    public function afterGetQuoteItemData(HyvaCheckoutCartItems $subject, ?array $items): ?array
    {
        if(is_array($items)) {
            usort($items, function ($a, $b) {
                return (int) $b['item_id'] - (int) $a['item_id'];
            });
        }

        return $items;
    }
}

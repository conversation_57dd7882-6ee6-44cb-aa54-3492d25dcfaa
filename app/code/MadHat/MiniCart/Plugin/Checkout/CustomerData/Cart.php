<?php
declare(strict_types=1);

namespace MadHat\MiniCart\Plugin\Checkout\CustomerData;

use Hyva\Theme\ViewModel\ProductPrice;
use Magento\Catalog\Model\ProductRepository;
use Magento\Catalog\Pricing\Price\FinalPrice;
use Magento\Catalog\Pricing\Price\RegularPrice;
use Magento\Checkout\CustomerData\Cart as MagentoCart;
use Magento\Checkout\Helper\Data;
use Magento\Checkout\Model\Session;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

class Cart
{
    /**
     * @var ProductPrice
     */
    protected ProductPrice $productPriceViewModel;

    /**
     * @var ProductRepository
     */
    protected ProductRepository $productRepository;

    /**
     * @var Data
     */
    protected Data $checkoutHelper;

    /**
     * @var Session
     */
    protected Session $checkoutSession;

    /**
     * @param ProductPrice $productPriceViewModel
     * @param ProductRepository $productRepository
     * @param Data $checkoutHelper
     * @param Session $checkoutSession
     */
    public function __construct(
        ProductPrice $productPriceViewModel,
        ProductRepository $productRepository,
        Data $checkoutHelper,
        Session $checkoutSession
    ) {
        $this->productPriceViewModel = $productPriceViewModel;
        $this->productRepository = $productRepository;
        $this->checkoutHelper = $checkoutHelper;
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * Add tax data to result
     *
     * @param MagentoCart $subject
     * @param array $result
     * @return array
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function afterGetSectionData(MagentoCart $subject, array $result): array
    {
        if (!is_array($result['items']) || empty($result['items'])) {
            return $result;
        }

        $quote = $this->checkoutSession->getQuote();

        foreach ($result['items'] as &$item) {
            $itemModel = $quote->getItemById($item['item_id']);
            if (!$itemModel) {
                continue;
            }

            $product = $itemModel->getProduct();

            if (isset($item['product_type']) && $item['product_type'] === Configurable::TYPE_CODE) {
                $simpleProductId = $itemModel->getOptionByCode('simple_product')?->getValue();
                if ($simpleProductId) {
                    $product = $this->productRepository->getById($simpleProductId);
                }
            }

            $regularPrice = $this->productPriceViewModel->getPriceValue(RegularPrice::PRICE_CODE, $product);
            $finalPrice = $this->productPriceViewModel->getPriceValue(FinalPrice::PRICE_CODE, $product);

            $item['product_regular_price_value'] = $regularPrice;
            $item['product_regular_price'] = $this->checkoutHelper->formatPrice($regularPrice);
            $item['product_final_price_value'] = $finalPrice;
            $item['product_final_price'] = $this->checkoutHelper->formatPrice($finalPrice);
        }

        return $result;
    }

}

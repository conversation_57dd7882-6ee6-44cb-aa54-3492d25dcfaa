<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="site_order">
            <group id="fulfillment_crowd" sortOrder="20" showInWebsite="1" showInStore="0" showInDefault="1"
                   translate="label">
                <label>Fulfillment Crowd</label>
                <field id="is_enabled" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Enable Fulfillment Crowd</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="api_url" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>API Url</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="api_key" type="obscure" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label">
                    <label>API Key</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="channel_id" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Channel Id</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="shipping_methods_mapping" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Shipping Methods Mapping</label>
                    <comment>Shipping methods mapping for standard/premium delivery.</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="default_fulfilment_workflow" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Default Fulfilment Workflow</label>
                    <comment>Select default fulfilment workflow to use when 'Default' is received in OrderStatus messages.</comment>
                    <source_model>MadHat\FulfilmentCrowd\Model\Config\Source\FulfilmentWorkflowOptions</source_model>
                </field>
            </group>
        </section>
    </system>
</config>

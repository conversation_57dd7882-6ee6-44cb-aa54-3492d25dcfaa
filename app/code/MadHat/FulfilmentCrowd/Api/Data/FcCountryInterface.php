<?php
declare(strict_types=1);

namespace MadHat\FulfilmentCrowd\Api\Data;

interface FcCountryInterface
{
    public const EXTERNAL_COUNTRY_ID = 'external_country_id';
    public const COUNTRY_NAME = 'country_name';
    public const COUNTRY_CODE = 'country_code';
    public const FC_COUNTRY_ID = 'fc_country_id';
    public const EU_MEMBER = 'eu_member';
    public const SUBDIVISIONS_IN_USE = 'subdivisions_in_use';

    /**
     * Get fc_country_id
     *
     * @return string|null
     */
    public function getFcCountryId();

    /**
     * Set fc_country_id
     *
     * @param string $fcCountryId
     * @return \MadHat\FulfilmentCrowd\FcCountry\Api\Data\FcCountryInterface
     */
    public function setFcCountryId($fcCountryId);

    /**
     * Get external_country_id
     *
     * @return string|null
     */
    public function getExternalCountryId();

    /**
     * Set external_country_id
     *
     * @param string $externalCountryId
     * @return \MadHat\FulfilmentCrowd\FcCountry\Api\Data\FcCountryInterface
     */
    public function setExternalCountryId($externalCountryId);

    /**
     * Get country name
     *
     * @return string|null
     */
    public function getCountryName();

    /**
     * Set country name
     *
     * @param string $countryName
     * @return \MadHat\FulfilmentCrowd\FcCountry\Api\Data\FcCountryInterface
     */
    public function setCountryName($countryName);

    /**
     * Get country_code
     *
     * @return string|null
     */
    public function getCountryCode();

    /**
     * Set country_code
     *
     * @param string $countryCode
     * @return \MadHat\FulfilmentCrowd\FcCountry\Api\Data\FcCountryInterface
     */
    public function setCountryCode($countryCode);

    /**
     * Get subdivisions_in_use
     *
     * @return string|null
     */
    public function getSubdivisionsInUse();

    /**
     * Set subdivisions_in_use
     *
     * @param string $subdivisionsInUse
     * @return \MadHat\FulfilmentCrowd\FcCountry\Api\Data\FcCountryInterface
     */
    public function setSubdivisionsInUse($subdivisionsInUse);

    /**
     * Get eu_member
     *
     * @return string|null
     */
    public function getEuMember();

    /**
     * Set eu_member
     *
     * @param string $euMember
     * @return \MadHat\FulfilmentCrowd\FcCountry\Api\Data\FcCountryInterface
     */
    public function setEuMember($euMember);
}

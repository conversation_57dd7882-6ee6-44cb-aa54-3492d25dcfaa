<?php
/**
 * Source model for Fulfilment Workflow options
 */
declare(strict_types=1);

namespace MadHat\FulfilmentCrowd\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class FulfilmentWorkflowOptions implements OptionSourceInterface
{
    /**
     * Return array of options as value-label pairs
     *
     * @return array Format: array(array('value' => '<value>', 'label' => '<label>'), ...)
     */
    public function toOptionArray(): array
    {
        return [
            ['value' => 'FulfilmentCrowd', 'label' => __('FulfilmentCrowd')],
            ['value' => 'Ongoing', 'label' => __('Ongoing')]
        ];
    }
}

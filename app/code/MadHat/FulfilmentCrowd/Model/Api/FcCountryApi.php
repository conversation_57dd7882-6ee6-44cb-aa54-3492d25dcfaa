<?php
declare(strict_types=1);

namespace MadHat\FulfilmentCrowd\Model\Api;

use MadHat\FulfilmentCrowd\Helper\Data;
use MadHat\FulfilmentCrowd\Logger\Logger;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Store\Model\StoreManagerInterface;

class FcCountryApi extends FulfilmentCrowdApi
{
    /**
     * @var AdapterInterface
     */
    protected AdapterInterface $connection;

    /**
     * @param Data $fcHelper
     * @param Logger $fcLogger
     * @param Curl $curl
     * @param Json $json
     * @param StoreManagerInterface $storeManager
     * @param ResourceConnection $resource
     */
    public function __construct(
        Data $fcHelper,
        Logger $fcLogger,
        Curl $curl,
        Json $json,
        StoreManagerInterface $storeManager,
        ResourceConnection $resource,
    ) {
        parent::__construct(
            $fcHelper,
            $fcLogger,
            $curl,
            $json,
            $storeManager
        );
        $this->connection = $resource->getConnection();
    }

    /**
     * Fetch and store Fulfillment Crowd Countries.
     *
     * @return void
     */
    public function syncCountries(): void
    {
        $fcCountries = $this->getFcCountries();

        $fcCountriesData = [];
        foreach ($fcCountries as $fcCountry) {
            $fcCountriesData[] = [
                'external_country_id' => $fcCountry['id'],
                'country_name' => $fcCountry['description'],
                'country_code' => $fcCountry['country_code'],
                'subdivisions_in_use' => $fcCountry['subdivisions_in_use'],
                'eu_member' => $fcCountry['eu_member'],
            ];
        }

        $table = $this->connection->getTableName('madhat_fulfillmentcrowd_country');
        $this->connection->insertMultiple($table, $fcCountriesData);
    }

    /**
     * Get Fulfillment Crowd Product List
     *
     * @return bool|array
     */
    public function getFcCountries(): bool|array
    {
        return $this->getApiCall(self::ENDPOINT_COUNTRIES);
    }
}

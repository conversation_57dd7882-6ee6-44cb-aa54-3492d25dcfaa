<?php
declare(strict_types=1);

namespace MadHat\OrderIntegration\Api\Data;

interface MadhatOrderItemInfoInterface
{
    const ID = 'id';

    const FC_PRODUCT_ID = 'fc_product_id';

    const ITEM_ID = 'item_id';

    const SITE_ROUND =  'site_round';

    /**
     * Get id
     * @return mixed
     */
    public function getId(): mixed;

    /**
     * Set id
     * @param mixed $value
     * @return MadhatOrderItemInfoInterface
     */
    public function setId(mixed $value): MadhatOrderItemInfoInterface;

    /**
     * Get item_id
     * @return int|null
     */
    public function getItemId(): ?int;

    /**
     * Set item_id
     * @param int $itemId
     * @return MadhatOrderItemInfoInterface
     */
    public function setItemId(int $itemId): MadhatOrderItemInfoInterface;

    /**
     * Get fc_product_id
     * @return int|null
     */
    public function getFcProductId(): ?int;

    /**
     * Set fc_product_id
     * @param int $fcProductId
     * @return MadhatOrderItemInfoInterface
     */
    public function setFcProductId(int $fcProductId): MadhatOrderItemInfoInterface;

    public function getSiteRound(): ?float;

    public function setSiteRound(float $siteRound): MadhatOrderItemInfoInterface;
}

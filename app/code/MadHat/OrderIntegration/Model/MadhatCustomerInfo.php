<?php
declare(strict_types=1);

namespace MadHat\OrderIntegration\Model;

use MadHat\OrderIntegration\Api\Data\MadhatCustomerInfoInterface;
use Magento\Framework\Model\AbstractModel;

class MadhatCustomerInfo extends AbstractModel implements MadhatCustomerInfoInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(ResourceModel\MadhatCustomerInfo::class);
    }

    /**
     * @inheritDoc
     */
    public function getCustomerEmail(): ?string
    {
        return $this->getData(self::CUSTOMER_EMAIL);
    }

    /**
     * @inheritDoc
     */
    public function setCustomerEmail($customerEmail): MadhatCustomerInfoInterface
    {
        return $this->setData(self::CUSTOMER_EMAIL, $customerEmail);
    }
}


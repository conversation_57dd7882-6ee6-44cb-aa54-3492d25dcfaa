<?php
declare(strict_types=1);

namespace MadHat\ProductEnrichment\Console\Command;

use MadHat\ProductEnrichment\Logger\Logger;
use MadHat\ProductEnrichment\Model\Api\ProductEnrichment as ProductEnrichmentApi;
use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use Magento\Framework\Exception\LocalizedException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ProductEnrichment extends Command
{
    /**
     * @var Logger
     */
    private Logger $logger;

    /**
     * @var ProductEnrichmentApi
     */
    private ProductEnrichmentApi $productEnrichmentApi;

    /**
     * @var State
     */
    private State $appState;

    /**
     * @param Logger $logger
     * @param ProductEnrichmentApi $productEnrichmentApi
     * @param State $appState
     * @param string|null $name
     */
    public function __construct(
        Logger $logger,
        ProductEnrichmentApi $productEnrichmentApi,
        State $appState,
        string $name = null
    ) {
        parent::__construct($name);
        $this->logger = $logger;
        $this->productEnrichmentApi = $productEnrichmentApi;
        $this->appState = $appState;
    }

    /**
     * @return void
     */
    protected function configure(): void
    {
        $this->setName("madhat:product:enrich");
        $this->setDescription("Madhat product enrichment");
        parent::configure();
    }

    /**
     * @throws LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->appState->setAreaCode('adminhtml'); // Set area code safely

        $this->logger->info("Madhat product enrichment command starts");
        $startTime = microtime(true);
        $result = $this->productEnrichmentApi->processProductEnrichment();
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        $output->writeln("<info>Execution Time: {$executionTime} seconds</info>");

        if (is_array($result) && $result['success_products']) {
            $message = __("%1 out of %2 products enriched successfully.", $result['success_products'], $result['total_products']);
            $this->logger->info($message);
            $this->logger->info("Execution Time: {$executionTime} seconds");
            $output->writeln($message);
            return Cli::RETURN_SUCCESS;
        }
        $output->writeln("Product enrichment failed. Please check Madhat_ProductEnrichment.log for details.");
        $this->logger->info("Madhat product enrichment command ends.");
        return Cli::RETURN_FAILURE;
    }
}

<?php
declare(strict_types=1);

namespace MadHat\ProductEnrichment\Helper;

use Magento\Framework\App\Helper\AbstractHelper;

class Data extends AbstractHelper
{
    protected const PRODUCT_ENRICHMENT_IS_ENABLED_CONFIG_PATH = 'product_enrichment/general/is_enabled';

    protected const PRODUCT_ENRICHMENT_SOURCE_URL_CONFIG_PATH = 'product_enrichment/general/product_xml_source_url';

    protected const PRODUCT_ENRICHMENT_ENRICHMENT_TYPE_CONFIG_PATH = 'product_enrichment/general/enrichment_type';

    public function getIsEnabledFromConfig(): bool
    {
        return (bool) $this->scopeConfig->getValue(
            self::PRODUCT_ENRICHMENT_IS_ENABLED_CONFIG_PATH
        );
    }

    public function getSourceUrlFromConfig(): string
    {
        return $this->scopeConfig->getValue(
            self::PRODUCT_ENRICHMENT_SOURCE_URL_CONFIG_PATH
        );
    }

    public function getEnrichmentTypeFromConfig(): string
    {
        return $this->scopeConfig->getValue(
            self::PRODUCT_ENRICHMENT_ENRICHMENT_TYPE_CONFIG_PATH
        );
    }
}

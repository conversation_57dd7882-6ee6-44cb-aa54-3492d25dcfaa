<?php
declare(strict_types=1);

namespace Madhat\CompatibilityFixes\Plugin\Model\Product;

use Magento\Catalog\Model\Product\Visibility;
use Magento\Framework\App\RequestInterface;

class VisibilityPlugin
{
    /**
     * @var RequestInterface
     */
    protected RequestInterface $request;

    /**
     * @param RequestInterface $request
     */
    public function __construct(
        RequestInterface $request,
    ) {
        $this->request = $request;
    }

    /**
     * Remove configurable product visibility from Amasty Brands page
     *
     * @param Visibility $subject
     * @param array $result
     * @return array
     */
    public function afterGetVisibleInCatalogIds(Visibility $subject, array $result)
    {
        $controllerName = $this->request->getFullActionName();

        if ($controllerName == 'ambrand_index_index') {
            return [Visibility::VISIBILITY_BOTH];
        }

        return $result;
    }
}

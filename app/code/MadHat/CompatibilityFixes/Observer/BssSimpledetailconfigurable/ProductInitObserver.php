<?php
declare(strict_types=1);

namespace MadHat\CompatibilityFixes\Observer\BssSimpledetailconfigurable;

use Bss\Simpledetailconfigurable\Helper\ModuleConfig;
use Bss\Simpledetailconfigurable\Helper\ProductData;
use Bss\Simpledetailconfigurable\Helper\UrlIdentifier;
use Bss\Simpledetailconfigurable\Model\Config\Source\GallerySwitchStrategy;
use Bss\Simpledetailconfigurable\Observer\ProductInitObserver as BssProductInitObserver;
use Exception;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Media\Config;
use Magento\Catalog\Model\ProductFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Data\CollectionFactory;
use Magento\Framework\DataObjectFactory;
use Magento\Framework\Event\Observer;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Filesystem;

class ProductInitObserver extends BssProductInitObserver
{
    /**
     * @var UrlIdentifier
     */
    private UrlIdentifier $_urlIdentifier;

    /**
     * @var ProductData
     */
    private ProductData $_productData;

    /**
     * @var Config
     */
    private Config $_catalogProductMediaConfig;

    /**
     * @var Filesystem
     */
    private Filesystem $_filesystem;

    /**
     * @var CollectionFactory
     */
    private CollectionFactory $_collectionFactory;

    /**
     * @var ProductFactory
     */
    private ProductFactory $productFactory;

    public function __construct(
        UrlIdentifier $urlIdentifier,
        ProductData $productData,
        ModuleConfig $moduleconfig,
        Config $catalogProductMediaConfig,
        Filesystem $filesystem,
        CollectionFactory $collectionFactory,
        DataObjectFactory $dataObjectFactory,
        ProductFactory $productFactory,
    ) {
        parent::__construct(
            $urlIdentifier,
            $productData,
            $moduleconfig,
            $catalogProductMediaConfig,
            $filesystem,
            $collectionFactory,
            $dataObjectFactory
        );
        $this->_urlIdentifier = $urlIdentifier;
        $this->_productData = $productData;
        $this->_catalogProductMediaConfig = $catalogProductMediaConfig;
        $this->_filesystem = $filesystem;
        $this->_collectionFactory = $collectionFactory;
        $this->productFactory = $productFactory;
    }

    public function execute(Observer $observer)
    {
        if ($this->moduleconfig->isModuleEnable()) {
            $request = $observer->getControllerAction()->getRequest();
            $product = $observer->getProduct();
            if ($this->validateObserver($request, $product)) {
                try {
                    if ($this->_productData->getEnabledModuleOnProduct($product->getId())['enabled']) {
                        $preselectData = $this->getSelectingData($product->getId());
                        $pathInfo = $request->getOriginalPathInfo();
                        if (!$this->_productData->isAjaxLoad($product->getId())) {
                            $product->setSdcpData($pathInfo);
                            $product->setPreselectData($preselectData);
                            $child = $this->_urlIdentifier->getChildProduct($this->removeFirstSlashes($pathInfo));
                            if ($child) {
                                $this->replaceData($product, $child, $pathInfo);
                                return;
                            }
                        }
                        if ($preselectData) {
                            $product->setPreSelect(true);
                        }
                        $childProductId = $request->getParam('request_child_product_id');
                        $childProduct = $this->productFactory->create()->load($childProductId);
                        $this->replaceImageByChildProduct($product, $childProduct, $pathInfo);
                    }
                } catch (Exception $e) {
                    $childProductId = $request->getParam('request_child_product_id');
                    $childProduct = $this->productFactory->create()->load($childProductId);
                    $this->replaceImageByChildProduct($product, $childProduct, $pathInfo);
                }
            }
        }
    }

    /**
     * @param $product
     * @param $child
     * @param $pathInfo
     * @return void
     * @throws NoSuchEntityException
     * @throws Exception
     */
    private function replaceImageByChildProduct($product, $child, $pathInfo)
    {
        $config = $this->_productData->getModuleConfig()->isShowImage();
        $this->replaceData($product, $child, $pathInfo);
        $this->replaceInitImage($product, $child, $config);
    }

    /**
     * @param mixed $request
     * @param Product $product
     * @return bool
     * @throws NoSuchEntityException
     */
    private function validateObserver($request, $product)
    {
        return $request->getFullActionName() === 'catalog_product_view'
            && $product->getTypeId() === 'configurable'
            && $this->_productData->getModuleConfig()->isModuleEnable()
            && ($this->moduleconfig->customUrl() || ($this->moduleconfig->preselectConfig()));
    }

    /**
     * @param Product $product
     * @param Product $child
     * @param string $pathInfo
     * @throws NoSuchEntityException
     * @throws Exception
     */
    private function replaceData($product, $child, $pathInfo)
    {
        $moduleConfig = $this->_productData->getModuleConfig()->getAllConfig();
        $product->setSdcpPriceInfo($child->getPriceInfo());
        $product->setSdcpId($pathInfo);
        if ($moduleConfig['meta_data']) {
            if ($child->hasMetaTitle()) {
                $product->setMetaTitle($child->getMetaTitle());
            }
            if ($child->hasMetaKeyword()) {
                $product->setMetaKeyword($child->getMetaKeyword());
            }
            if ($child->hasMetaDescription()) {
                $product->setMetaDescription($child->getMetaDescription());
            }
        } else {
            if (!$product->hasMetaTitle()) {
                $product->setMetaTitle($product->getName());
            }
        }
        if ($moduleConfig['sku']) {
            $product->setSku($child->getSku());
        }
        if ($moduleConfig['name']) {
            $product->setName($child->getName());
        }
        $this->replaceInitImage($product, $child, $moduleConfig['images']);
    }

    /**
     * @param Product $product
     * @param Product $child
     * @param string $config
     * @throws Exception
     */
    private function replaceInitImage($product, $child, $config)
    {
        $configEnableWS = $this->isEnableConfigurableProductWholeSale($product);
        $replaceMedia = null;
        if ($config == GallerySwitchStrategy::CONFIG_REPLACE) {
            if (!$configEnableWS) {
                $product->setMediaGalleryImages($child->getMediaGalleryImages());
            }
        } elseif ($config == GallerySwitchStrategy::CONFIG_PREPEND) {
            $replaceMedia = $product->getMediaGallery('images');
            if ($childMainImage = $this->_productData->getMainImage($child)) {
                $images = $this->_collectionFactory->create();
                $images = $this->createGallyeryCollection($images, $childMainImage);
                $images = $this->createGallyeryCollection($images, $replaceMedia);
                $product->setMediaGalleryImages($images);
            }
        }
    }

    /**
     * @param $images
     * @param $listImage
     * @return mixed
     */
    private function createGallyeryCollection($images, $listImage)
    {
        $directory = $this->_filesystem->getDirectoryRead(DirectoryList::MEDIA);
        foreach ($listImage as $image) {
            if (!empty($image['disabled'])
                || !empty($image['removed'])
                || empty($image['value_id'])
                || $images->getItemById($image['value_id']) != null
            ) {
                continue;
            }
            $image['url'] = $this->_catalogProductMediaConfig->getMediaUrl($image['file']);
            $image['id'] = $image['value_id'];
            $image['path'] = $directory->getAbsolutePath(
                $this->_catalogProductMediaConfig->getMediaPath($image['file'])
            );
            $images->addItem($this->dataObjectFactory->create()->addData($image));
        }
        return $images;
    }
}

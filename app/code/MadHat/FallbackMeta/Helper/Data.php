<?php
/**
 * MadHat_FallbackMeta extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_FallbackMeta
 * @copyright Copyright (c) 2024
 **/

namespace MadHat\FallbackMeta\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Escaper;
use Magento\Store\Model\ScopeInterface;

/**
 * Helper Data Class
 */
class Data extends AbstractHelper
{
    protected const FALLBACK_META_IS_ENABLED_PATH = 'madhat_fallbackmeta/settings/enabled';

    /**
     * @var Escaper
     */
    protected Escaper $escaper;

    /**
     * @param Context $context
     * @param Escaper $escaper
     */
    public function __construct(
        Context $context,
        Escaper $escaper
    ) {
        parent::__construct($context);
        $this->escaper = $escaper;
    }

    /**
     * Get enabled value for FallBackMeta module
     *
     * @return int
     */
    public function isEnabled(): int
    {
        return $this->scopeConfig->getValue(
            self::FALLBACK_META_IS_ENABLED_PATH,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get Meta Title template from system config
     *
     * @param string $entityType
     * @return string
     */
    public function getCustomMetaTitle(string $entityType): string
    {
        return $this->scopeConfig->getValue(
            'madhat_fallbackmeta/' . $entityType . '/title',
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get Meta Description template from system config
     *
     * @param string $entityType
     * @return string
     */
    public function getCustomMetaDescription(string $entityType): string
    {
        return $this->scopeConfig->getValue(
            'madhat_fallbackmeta/' . $entityType . '/description',
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Remove html tags
     *
     * @param string $htmlContent
     * @return string
     */
    public function getPlainText(string $htmlContent): string
    {
        // Remove CSS & JavaScript inside <style> & <script> tags
        $htmlContent = preg_replace('/<style\b[^>]*>(.*?)<\/style>/is', '', $htmlContent);
        $htmlContent = preg_replace('/<script\b[^>]*>(.*?)<\/script>/is', '', $htmlContent);

        // Decode HTML entities, including &nbsp;
        $plainText = strip_tags($htmlContent);
        $plainText = $this->escaper->escapeHtml($plainText);

        // Remove any remaining &nbsp; or other non-breaking spaces explicitly
        $plainText = str_replace("\xc2\xa0", ' ', $plainText);  // Handle UTF-8 non-breaking space
        $plainText = str_replace("&nbsp;", ' ', $plainText);   // Just in case

        // Replace multiple whitespace characters with a single space
        $plainText = preg_replace('/\s+/', ' ', $plainText);
        $plainText = substr($plainText, 0, 255);

        return trim($plainText);
    }
}

<?php
/**
 * MadHat_FallbackMeta extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_FallbackMeta
 * @copyright Copyright (c) 2024
 **/

namespace MadHat\FallbackMeta\Observer;

use MadHat\FallbackMeta\Helper\Data;
use Magento\Catalog\Model\Category;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class CategoryMetaObserver implements ObserverInterface
{
    /**
     * @var Data
     */
    protected Data $moduleHelper;

    /**
     * @param Data $moduleHelper
     */
    public function __construct(Data $moduleHelper)
    {
        $this->moduleHelper = $moduleHelper;
    }

    /**
     * Set category meta title & description, if not set
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer): void
    {
        if ($this->moduleHelper->isEnabled()) {
            /** @var Category $category */
            $category = $observer->getCategory();

            $metaTitle = $category->getMetaTitle();
            if (!$metaTitle || trim($metaTitle) === '') {
                $category = $this->setMetaTitle($category);
            }

            $metaDescription = $category->getMetaDescription();
            if (!$metaDescription || trim($metaDescription) === '') {
                $category = $this->setMetaDescription($category);
            }
        }
    }

    /**
     * Set category meta title
     *
     * @param Category $category
     * @return Category
     */
    protected function setMetaTitle(Category $category): Category
    {
        $metaTitle = $this->moduleHelper->getCustomMetaTitle('category');
        $title = str_replace("%CategoryName%", $category->getName(), $metaTitle);
        $category->setMetaTitle($title);

        return $category;
    }

    /**
     * Set category meta description
     *
     * @param Category $category
     * @return Category
     */
    protected function setMetaDescription(Category $category): Category
    {
        $metaDescription = $this->moduleHelper->getCustomMetaDescription('category');

        $categoryDescription = $category->getDescription() ? $category->getDescription() : '';
        $categoryDescription = $this->moduleHelper->getPlainText($categoryDescription);

        $metaDescription = str_replace("%CategoryDescription%", $categoryDescription, $metaDescription);

        $category->setMetaDescription($metaDescription);
        return $category;
    }
}

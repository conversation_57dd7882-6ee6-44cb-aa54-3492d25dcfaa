<?php
declare(strict_types=1);

namespace MadHat\OldUrlRewrite\Model;

use MadHat\OldUrlRewrite\Api\Data\UrlRewriteInterface;
use MadHat\OldUrlRewrite\Model\ResourceModel\UrlRewrite as UrlRewriteResource;
use Magento\Framework\Model\AbstractModel;

class UrlRewrite extends AbstractModel implements UrlRewriteInterface
{
    /**
     * @inheritDoc
     */
    public function _construct(): void
    {
        $this->_init(UrlRewriteResource::class);
    }

    /**
     * @inheritDoc
     */
    public function getOldUrl(): ?string
    {
        return $this->getData(self::OLD_URL);
    }

    /**
     * @inheritDoc
     */
    public function setOldUrl(string $oldUrl): UrlRewriteInterface
    {
        return $this->setData(self::OLD_URL, $oldUrl);
    }

    /**
     * @inheritDoc
     */
    public function getType(): ?string
    {
        return $this->getData(self::TYPE);
    }

    /**
     * @inheritDoc
     */
    public function setType(string $type): UrlRewriteInterface
    {
        return $this->setData(self::TYPE, $type);
    }

    /**
     * @inheritDoc
     */
    public function getIdentifier(): ?string
    {
        return $this->getData(self::IDENTIFIER);
    }

    /**
     * @inheritDoc
     */
    public function setIdentifier(string $identifier): UrlRewriteInterface
    {
        return $this->setData(self::IDENTIFIER, $identifier);
    }

    /**
     * @inheritDoc
     */
    public function getVariantId(): ?string
    {
        return $this->getData(self::VARIANT_ID);
    }

    /**
     * @inheritDoc
     */
    public function setVariantId(string $variantId): UrlRewriteInterface
    {
        return $this->setData(self::VARIANT_ID, $variantId);
    }

    /**
     * @inheritDoc
     */
    public function getTargetUrl(): ?string
    {
        return $this->getData(self::TARGET_URL);
    }

    /**
     * @inheritDoc
     */
    public function setTargetUrl(string $targetUrl): UrlRewriteInterface
    {
        return $this->setData(self::TARGET_URL, $targetUrl);
    }
}

<?php
declare(strict_types=1);

namespace MadHat\OldUrlRewrite\Console\Command;

use MadHat\OldUrlRewrite\Model\Api\UrlRewriteImport;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use Magento\Framework\Exception\LocalizedException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ImportUrlRewrite extends Command
{
    const STORE_ID_OPTION = 'store_id';

    /**
     * @var UrlRewriteImport
     */
    private UrlRewriteImport $urlRewriteImport;

    /**
     * @var State
     */
    private State $appState;

    /**
     * @param UrlRewriteImport $urlRewriteImport
     * @param State $appState
     * @param string|null $name
     */
    public function __construct(
        UrlRewriteImport $urlRewriteImport,
        State $appState,
        string $name = null
    ) {
        parent::__construct($name);
        $this->urlRewriteImport = $urlRewriteImport;
        $this->appState = $appState;
    }

    /**
     * @inheritdoc
     */
    protected function configure(): void
    {
        $this->setName('madhat:url-rewrite:import');
        $this->setDescription('Import url rewrite');
        $this->addOption(self::STORE_ID_OPTION, null, InputOption::VALUE_REQUIRED, 'Store ID');
        parent::configure();
    }

    /**
     * Import URL Rewrite to Magento Default from Custom Table
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     *
     * @throws LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->appState->setAreaCode(Area::AREA_ADMINHTML);
        $storeId = $input->getOption(self::STORE_ID_OPTION);

        $response = $this->urlRewriteImport->moveUrlRewrites($storeId);

        $output->writeln($response['message']);
        if ($response['success']) {
            return Cli::RETURN_SUCCESS;
        }
        return Cli::RETURN_FAILURE;
    }
}

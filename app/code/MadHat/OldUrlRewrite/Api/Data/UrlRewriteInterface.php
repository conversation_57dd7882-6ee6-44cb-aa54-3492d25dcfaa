<?php
namespace MadHat\OldUrlRewrite\Api\Data;

interface UrlRewriteInterface
{
    const ENTITY_ID = 'entity_id';

    const OLD_URL = 'old_url';

    const TYPE = 'type';

    const IDENTIFIER = 'identifier';

    const VARIANT_ID = 'variant_id';

    const TARGET_URL = 'target_url';

    /**
     * Get old_url
     *
     * @return string|null
     */
    public function getOldUrl(): ?string;

    /**
     * Set old_url
     *
     * @param string $oldUrl
     * @return \Developer\OldUrlRewrite\Api\Data\UrlRewriteInterface
     */
    public function setOldUrl(string $oldUrl): UrlRewriteInterface;

    /**
     * Get type
     *
     * @return string|null
     */
    public function getType(): ?string;

    /**
     * Set type
     *
     * @param string $type
     * @return UrlRewriteInterface
     */
    public function setType(string $type): UrlRewriteInterface;

    /**
     * Get identifier
     *
     * @return string|null
     */
    public function getIdentifier(): ?string;

    /**
     * Set identifier
     *
     * @param string $identifier
     * @return UrlRewriteInterface
     */
    public function setIdentifier(string $identifier): UrlRewriteInterface;

    /**
     * Get variant_id
     *
     * @return string|null
     */
    public function getVariantId(): ?string;

    /**
     * Set variant_id
     *
     * @param string $variantId
     * @return UrlRewriteInterface
     */
    public function setVariantId(string $variantId): UrlRewriteInterface;

    /**
     * Get target_url
     *
     * @return string|null
     */
    public function getTargetUrl(): ?string;

    /**
     * Set target_url
     *
     * @param string $targetUrl
     * @return UrlRewriteInterface
     */
    public function setTargetUrl(string $targetUrl): UrlRewriteInterface;
}

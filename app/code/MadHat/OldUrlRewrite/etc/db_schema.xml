<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="madhat_url_rewrite" resource="default" engine="innodb" comment="MadHat URL Rewrite Table">
        <column xsi:type="int" name="entity_id" unsigned="false" nullable="false" identity="true" comment="Entity ID"/>
        <column xsi:type="text" name="old_url" nullable="false" comment="Old URL"/>
        <column xsi:type="varchar" name="type" nullable="false" comment="type"/>
        <column xsi:type="varchar" name="identifier" nullable="false" comment="Identifier"/>
        <column  xsi:type="varchar" name="variant_id" nullable="true" comment="Variant ID"/>
        <column xsi:type="text" name="target_url" nullable="true" comment="Target URL"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
    </table>
</schema>

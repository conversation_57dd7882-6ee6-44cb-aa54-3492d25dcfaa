<?php
declare(strict_types=1);

namespace MadHat\EmailLogger\Controller\Account;

use Exception;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\EmailLogger\Logger\Logger;
use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Model\AccountManagement;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Escaper;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\SecurityViolationException;
use Magento\Framework\Validator\EmailAddress;
use Magento\Framework\Validator\ValidateException;
use Magento\Framework\Validator\ValidatorChain;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;

class ForgotPasswordPost extends \Magento\Customer\Controller\Account\ForgotPasswordPost
{
    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @var Emulation
     */
    protected Emulation $emulation;

    /**
     * @var DbLoggerSaver
     */
    protected DbLoggerSaver $dbLoggerSaver;

    /**
     * @var Logger
     */
    protected Logger $madhatEmailLogger;

    /**
     * @param Context $context
     * @param Session $customerSession
     * @param AccountManagementInterface $customerAccountManagement
     * @param Escaper $escaper
     * @param StoreManagerInterface $storeManager
     * @param Emulation $emulation
     * @param DbLoggerSaver $dbLoggerSaver
     * @param Logger $madhatEmailLogger
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        AccountManagementInterface $customerAccountManagement,
        Escaper $escaper,
        StoreManagerInterface $storeManager,
        Emulation $emulation,
        DbLoggerSaver $dbLoggerSaver,
        Logger $madhatEmailLogger
    ) {
        parent::__construct($context, $customerSession, $customerAccountManagement, $escaper);
        $this->storeManager = $storeManager;
        $this->emulation = $emulation;
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->madhatEmailLogger = $madhatEmailLogger;
    }

    /**
     * Forgot customer password action
     *
     * @return Redirect
     * @throws ValidateException
     */
    public function execute(): Redirect
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $email = (string)$this->getRequest()->getPost('email');
        if ($email) {
            if (!ValidatorChain::is($email, EmailAddress::class)) {
                $this->session->setForgottenEmail($email);
                $this->messageManager->addErrorMessage(
                    __('The email address is incorrect. Verify the email address and try again.')
                );
                return $resultRedirect->setPath('*/*/forgotpassword');
            }

            try {
                $this->madhatEmailLogger->info(
                    __('Password Reset Process initiated for email : %1', $email)
                );
                $this->customerAccountManagement->initiatePasswordReset(
                    $email,
                    AccountManagement::EMAIL_RESET
                );
                $this->madhatEmailLogger->info(
                    __('Password Reset Process completes for email : %1', $email)
                );
                // phpcs:ignore Magento2.CodeAnalysis.EmptyBlock.DetectedCatch
            } catch (NoSuchEntityException $exception) {
                // Do nothing, we don't want anyone to use this action to determine which email accounts are registered.
            } catch (SecurityViolationException $exception) {
                $this->madhatEmailLogger->error($exception->getMessage());
                $this->messageManager->addErrorMessage($exception->getMessage());
                $this->addExceptionDbLoggerRecord($email, $exception);
                return $resultRedirect->setPath('*/*/forgotpassword');
            } catch (Exception $exception) {
                $this->madhatEmailLogger->error($exception->getMessage());
                $this->addExceptionDbLoggerRecord($email, $exception);
                $this->messageManager->addExceptionMessage(
                    $exception,
                    __('We\'re unable to send the password reset email.')
                );
                return $resultRedirect->setPath('*/*/forgotpassword');
            }
            $this->addSuccessDbLoggerRecord($email);
            $this->messageManager->addSuccessMessage($this->getSuccessMessage($email));
            return $resultRedirect->setPath('*/*/');
        } else {
            $this->messageManager->addErrorMessage(__('Please enter your email.'));
            return $resultRedirect->setPath('*/*/forgotpassword');
        }
    }

    /**
     * @throws NoSuchEntityException
     */
    private function addSuccessDbLoggerRecord(string $email): void
    {
        $message = "Reset Password Process completed for email : " . $email;

        $storeId = $this->storeManager->getStore()->getId();
        $this->emulation->startEnvironmentEmulation($storeId);

        $this->dbLoggerSaver->addRecord(
            'Reset Password Notification',
            $message,
            'NOTICE'
        );

        $this->emulation->stopEnvironmentEmulation();
    }

    /**
     * @param string $email
     * @param SecurityViolationException|Exception $exception
     * @return void
     * @throws NoSuchEntityException
     */
    private function addExceptionDbLoggerRecord(string $email, SecurityViolationException|Exception $exception): void
    {
        $message = "Exception occurred for Reset Password for email : " . $email.". Exception : ".$exception->getMessage();

        $storeId = $this->storeManager->getStore()->getId();
        $this->emulation->startEnvironmentEmulation($storeId);

        $this->dbLoggerSaver->addRecord(
            'Reset Password Notification',
            $message,
            'NOTICE'
        );

        $this->emulation->stopEnvironmentEmulation();
    }
}

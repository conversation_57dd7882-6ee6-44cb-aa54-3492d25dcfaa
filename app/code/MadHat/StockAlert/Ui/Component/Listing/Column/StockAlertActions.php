<?php

/**
 * MadHat_StockAlert Extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_StockAlert
 * @copyright Copyright (c) 2025
 */

namespace MadHat\StockAlert\Ui\Component\Listing\Column;

use Magento\Ui\Component\Listing\Columns\Column;
use Magento\Framework\UrlInterface;

class StockAlertActions extends Column
{   
    protected $urlBuilder;

    public function __construct(
        UrlInterface $urlBuilder,
        \Magento\Framework\View\Element\UiComponent\ContextInterface $context,
        \Magento\Framework\View\Element\UiComponentFactory $uiComponentFactory,
        array $components = [],
        array $data = []
    ) {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    public function prepareDataSource(array $dataSource)
    {
        if (!isset($dataSource['data']['items'])) return $dataSource;

        foreach ($dataSource['data']['items'] as & $item) {
            if (isset($item['alert_stock_id'])) {
                $item[$this->getData('name')]['delete'] = [
                    'href' => $this->urlBuilder->getUrl('stockalert/stock/delete', ['id' => $item['alert_stock_id']]),
                    'label' => __('Delete'),
                    'confirm' => [
                        'title' => __('Delete Alert'),
                        'message' => __('Are you sure you want to delete this alert?')
                    ],
                    'post' => true,
                    'icon' => 'trash' // this works with some UI themes (optional)
                ];
            }
        }

        return $dataSource;
    }
}


<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Controller\Adminhtml\Urls;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use MadHat\Sitemap\Model\SitemapUrlFactory;
use MadHat\Sitemap\Model\ResourceModel\SitemapUrl as SitemapUrlResource;

/**
 * Class Delete
 * 
 * Controller for deleting sitemap URL
 */
class Delete extends Action
{
    /**
     * Authorization level of a basic admin session
     */
    const ADMIN_RESOURCE = 'MadHat_Sitemap::sitemap_urls';

    /**
     * @var SitemapUrlFactory
     */
    protected $sitemapUrlFactory;

    /**
     * @var SitemapUrlResource
     */
    protected $sitemapUrlResource;

    /**
     * @param Context $context
     * @param SitemapUrlFactory $sitemapUrlFactory
     * @param SitemapUrlResource $sitemapUrlResource
     */
    public function __construct(
        Context $context,
        SitemapUrlFactory $sitemapUrlFactory,
        SitemapUrlResource $sitemapUrlResource
    ) {
        parent::__construct($context);
        $this->sitemapUrlFactory = $sitemapUrlFactory;
        $this->sitemapUrlResource = $sitemapUrlResource;
    }

    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();
        $id = $this->getRequest()->getParam('id');

        if ($id) {
            try {
                $model = $this->sitemapUrlFactory->create();
                $this->sitemapUrlResource->load($model, $id);
                $this->sitemapUrlResource->delete($model);
                $this->messageManager->addSuccessMessage(__('You deleted the sitemap URL.'));
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            }
        }

        return $resultRedirect->setPath('*/*/');
    }
}

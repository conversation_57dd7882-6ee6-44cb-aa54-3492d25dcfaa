<?php
/**
 * Copyright © MadHat, Inc. All rights reserved.
 */
declare(strict_types=1);

namespace MadHat\Sitemap\Model;

use Magento\Framework\Model\AbstractModel;
use MadHat\Sitemap\Model\ResourceModel\SitemapUrl as SitemapUrlResource;

/**
 * Class SitemapUrl
 * 
 * Model for sitemap URLs management
 */
class SitemapUrl extends AbstractModel
{
    /**
     * URL types
     */
    const URL_TYPE_MISSING_INDEXABLE = 'missing_indexable';
    const URL_TYPE_NON_INDEXABLE = 'non_indexable';

    /**
     * @inheritdoc
     */
    protected function _construct()
    {
        $this->_init(SitemapUrlResource::class);
    }

    /**
     * Get available URL types
     *
     * @return array
     */
    public function getAvailableUrlTypes(): array
    {
        return [
            self::URL_TYPE_MISSING_INDEXABLE => __('Missing Indexable'),
            self::URL_TYPE_NON_INDEXABLE => __('Non-Indexable')
        ];
    }

    /**
     * Get URL type label
     *
     * @return string
     */
    public function getUrlTypeLabel(): string
    {
        $types = $this->getAvailableUrlTypes();
        return $types[$this->getUrlType()] ?? $this->getUrlType();
    }
}

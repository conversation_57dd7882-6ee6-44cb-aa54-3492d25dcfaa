<?php
declare(strict_types=1);

namespace MadHat\MagezonBrandLink\Block\Element;

use Amasty\ShopbyBrand\Model\Brand\BrandListDataProvider;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\StoreManagerInterface;
use Magezon\Core\Helper\Data;
use Magezon\NinjaMenus\Block\Element\Item as BaseItem;

class Item extends BaseItem
{
    /**
     * @var BrandListDataProvider
     */
    private BrandListDataProvider $brandListDataProvider;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * Constructor.
     *
     * @param Context $context
     * @param Data $coreHelper
     * @param BrandListDataProvider $brandListDataProvider
     * @param StoreManagerInterface $storeManager
     * @param array $data
     */
    public function __construct(
        Context $context,
        Data $coreHelper,
        BrandListDataProvider $brandListDataProvider,
        StoreManagerInterface $storeManager,
        array $data = []
    ) {
        parent::__construct($context, $coreHelper, $data);
        $this->brandListDataProvider = $brandListDataProvider;
        $this->storeManager = $storeManager;
    }

    /**
     * Retrieve the appropriate link based on the element type.
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getLink()
    {
        $link    = '#';
        $element = $this->getElement();
        $type    = $element->getData('item_type');

        switch ($type) {
            case 'category':
                $collection = $this->getGlobalData('category_collection');
                if ($collection) {
                    $category = $collection->getItemById($element->getData('category_id'));
                    if ($category) {
                        $link = $category->getUrl();
                        if ($element->getData('cat_name')) {
                            $this->setData('title', $category->getName());
                        }
                    }
                }
                break;

            case 'product':
                $collection = $this->getGlobalData('product_collection');
                if ($collection) {
                    $product = $collection->getItemById($element->getData('product_id'));
                    if ($product) $link = $product->getProductUrl();
                }
                break;

            case 'page':
                $collection = $this->getGlobalData('page_collection');
                if ($collection) {
                    $page = $collection->getItemById($element->getData('page_id'));
                    if ($page) $link = $this->_urlBuilder->getUrl(null, ['_direct' => $page->getIdentifier()]);
                }
                break;

            case 'custom':
                $customLink = $element->getData('custom_link');
                if ($customLink) $link = $this->coreHelper->filter($element->getData('custom_link'));
                break;

            /** Start of customisation for Brand Link */
            case 'brand':
                $brandId = (int) $element->getData('brand_id');
                $filterCategoryId = (int) $element->getData('filter_category_id');
                $link = $this->getBrandLink($brandId, $filterCategoryId);
                break;
            /** End of customisation for Brand Link */
        }

        return $link;
    }

    /**
     * Generate a brand-specific link.
     *
     * @param int $brandId
     * @param int $categoryId
     * @return string
     * @throws NoSuchEntityException
     */
    private function getBrandLink(int $brandId, int $categoryId): string
    {
        $link = '#';
        $storeId = (int) $this->storeManager->getStore()->getId();
        $brandList = $this->brandListDataProvider->getList($storeId);

        foreach ($brandList as $brand) {
            if ($brand->getBrandId() === $brandId) {
                $link = $brand->getUrl();
                break;
            }
        }

        if ($categoryId > 0) {
            $link .= "?cat=" . $categoryId;
        }

        return $link;
    }
}

<?php

namespace MadHat\Allstoreviewimage\Controller\Adminhtml\Allstoreviewimage;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use MadHat\Allstoreviewimage\Model\AllStoreViewImage;

class Save extends Action
{
    /**
     * @var AllStoreViewImage
     */
    protected $importData;

    public function __construct(
        Context           $context,
        AllStoreViewImage $importData
    )
    {
        parent::__construct($context);
        $this->importData = $importData;
    }

    /**
     * Execute action based on request and return result
     *
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $request = $this->_request->getParams();
        if (isset($request['product_sku']) && !empty($request['product_sku'])) {
            $skus = explode(",", $request['product_sku']);
            $skuLists = "'" . implode("', '", $skus) . "'";
            $this->importData->processProductSkus($skuLists);
            $this->messageManager->addSuccessMessage('Product Images fixed for SKU/s : ' . $request['product_sku']);
        } else {
            $this->messageManager->addErrorMessage('Please provide sku lists in input field.');
        }
        return $resultRedirect->setPath('*/*/');
    }
}

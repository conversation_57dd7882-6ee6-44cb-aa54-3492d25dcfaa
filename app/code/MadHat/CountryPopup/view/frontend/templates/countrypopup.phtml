<?php

/**
 * MadHat_CountryPopup Extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_CountryPopup
 * @copyright Copyright (c) 2025
 */

/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \MadHat\CountryPopup\ViewModel\CountryPopup $viewModel
 */
$viewModel = $block->getViewModel();
?>

<div
    x-data="countryPopupComponent('<?= $viewModel->getAjaxUrl() ?>')"
    x-init="initPopup()"
    x-show="showPopup"
    x-transition
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
    style="display: none;"
>
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-xl w-full text-center">

        <!-- Admin Text start -->
        <?= __($viewModel->getPopupContent()) ?>
        <p class="mb-4">
            <template x-if="country">
                <span><?= __("Would you like to visit our international shop instead") ?></span>
            </template>
            <template x-if="!country">
                <span><?= __("We couldn’t detect your country.") ?></span>
            </template>
        </p>
        <div class="flex justify-center gap-4 items-center my-4">
            <template x-if="redirectUrl">
                <button @click="window.location.href = redirectUrl"
                        class="btn btn-primary btn-size-sm">
                    <?= __($viewModel->getRedirectButtonText()) ?>
                </button>
            </template>
            <button @click="closePopup"
                    class="btn btn-outline btn-size-sm"><?= __($viewModel->getStayButtonText()) ?></button>
        </div>
    </div>
</div>

<script>
    function countryPopupComponent(ajaxUrl) {
        return {
            country: null,
            redirectUrl: null,
            showPopup: false,

            // Helper: set cookie
            setCookie(name, value, days) {
                let expires = "";
                if (days) {
                    const date = new Date();
                    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                    expires = "; expires=" + date.toUTCString();
                }
                document.cookie = name + "=" + (value || "") + expires + "; path=/";
            },

            // Helper: get cookie
            getCookie(name) {
                const nameEQ = name + "=";
                const ca = document.cookie.split(';');
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i];
                    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
                }
                return null;
            },

            initPopup() {
                if (!this.getCookie('countryPopupShown')) {
                    fetch(ajaxUrl, {credentials: 'same-origin'})
                        .then(res => res.json())
                        .then(data => {
                            console.log(data);
                            if (data.success) {
                                this.country = data.country;
                                this.redirectUrl = data.redirectUrl;
                                this.showPopup = true;
                            }
                            // Save cookie for 30 days
                            this.setCookie('countryPopupShown', '1', 1);
                        });
                }
            },

            closePopup() {
                this.showPopup = false;
            }
        }
    }
</script>

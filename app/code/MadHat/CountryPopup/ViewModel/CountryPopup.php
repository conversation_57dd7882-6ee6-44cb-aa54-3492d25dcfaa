<?php

/**
 * MadHat_CountryPopup Extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_CountryPopup
 * @copyright Copyright (c) 2025
 */

namespace MadHat\CountryPopup\ViewModel;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class CountryPopup implements ArgumentInterface
{
    /**
     * @var UrlInterface
     */
    private UrlInterface $urlBuilder;

    /**
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;

    public function __construct(
        UrlInterface $urlBuilder,
        StoreManagerInterface $storeManager,
        ScopeConfigInterface  $scopeConfig
    ) {
        $this->urlBuilder = $urlBuilder;
        $this->storeManager = $storeManager;
        $this->scopeConfig = $scopeConfig;
    }

    public function getAjaxUrl(): string
    {
        return $this->urlBuilder->getUrl('countrypopup/index/accept');
    }

    public function getPopupContent(): string
    {
        $currentStoreId = $this->storeManager->getStore()->getId();
        $scopeStores = ScopeInterface::SCOPE_STORES;

        return $this->scopeConfig->getValue(
            'country_popup/countrypopup/popup_content',
            $scopeStores,
            $currentStoreId
        );
    }


    public function getStayButtonText(): string
    {
        $currentStoreId = $this->storeManager->getStore()->getId();
        $scopeStores = ScopeInterface::SCOPE_STORES;

        return $this->scopeConfig->getValue(
            'country_popup/countrypopup/stay_button_text',
            $scopeStores,
            $currentStoreId
        );
    }


    public function getRedirectButtonText(): string
    {
        $currentStoreId = $this->storeManager->getStore()->getId();
        $scopeStores = ScopeInterface::SCOPE_STORES;

        return $this->scopeConfig->getValue(
            'country_popup/countrypopup/redirect_button_text',
            $scopeStores,
            $currentStoreId
        );
    }
}

<?php

namespace MadHat\InventoryImport\Model\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

class ProductStatus extends AbstractSource
{
    const VALUE_IN_STOCK = '1';
    const LABEL_IN_STOCK = 'In Stock';
    const VALUE_EXPIRES = '2';
    const LABEL_EXPIRES = 'Expires';
    const VALUE_OUT_OF_STOCK = '3';
    const LABEL_OUT_OF_STOCK = 'Out of Stock';
    const VALUE_SOON_IN_STOCK = '4';
    const LABEL_SOON_IN_STOCK = 'Back in stock soon';
    const VALUE_DISCONTINUED = '5';
    const LABEL_DISCONTINUED = 'Discontinued';
    const VALUE_NOT_RELEASED = '6';
    const LABEL_NOT_RELEASED = 'Not Released';
    const VALUE_AVAILABLE_ON_DEMAND = '7';
    const LABEL_AVAILABLE_ON_DEMAND = 'Available On Demand';
    const VALUE_AVAILABLE_ON_PREORDER = '8';
    const LABEL_AVAILABLE_ON_PREORDER = 'Available On Pre Order';
    const VALUE_ETA_2_DAYS = '12';
    const LABEL_ETA_2_DAYS = 'ETA 1-2 Days';
    const VALUE_ETA_3_DAYS = '13';
    const LABEL_ETA_3_DAYS = 'ETA 1-3 Days';
    const VALUE_ETA_7_DAYS = '9';
    const LABEL_ETA_7_DAYS = 'ETA 7 Days';
    const VALUE_ETA_14_DAYS = '10';
    const LABEL_ETA_14_DAYS = 'ETA 14 Days';
    const VALUE_ETA_30_DAYS = '11';
    const LABEL_ETA_30_DAYS = 'ETA 30 Days';

    /**
     * Retrieve All options
     * @return array
     */
    public function getAllOptions(): array
    {
        return [
            ['value' => '', 'label' => __('None')],
            ['value' => self::VALUE_IN_STOCK, 'label' => __(self::LABEL_IN_STOCK)],
            ['value' => self::VALUE_EXPIRES, 'label'=> __(self::LABEL_EXPIRES)],
            ['value' => self::VALUE_OUT_OF_STOCK, 'label'=> __(self::LABEL_OUT_OF_STOCK)],
            ['value' => self::VALUE_SOON_IN_STOCK, 'label'=> __(self::LABEL_SOON_IN_STOCK)],
            ['value' => self::VALUE_DISCONTINUED, 'label'=> __(self::LABEL_DISCONTINUED)],
            ['value' => self::VALUE_NOT_RELEASED, 'label'=> __(self::LABEL_NOT_RELEASED)],
            ['value' => self::VALUE_AVAILABLE_ON_DEMAND, 'label'=> __(self::LABEL_AVAILABLE_ON_DEMAND)],
            ['value' => self::VALUE_AVAILABLE_ON_PREORDER, 'label'=> __(self::LABEL_AVAILABLE_ON_PREORDER)],
            ['value' => self::VALUE_ETA_2_DAYS, 'label'=> __(self::LABEL_ETA_2_DAYS)],
            ['value' => self::VALUE_ETA_3_DAYS, 'label'=> __(self::LABEL_ETA_3_DAYS)],
            ['value' => self::VALUE_ETA_7_DAYS, 'label'=> __(self::LABEL_ETA_7_DAYS)],
            ['value' => self::VALUE_ETA_14_DAYS, 'label'=> __(self::LABEL_ETA_14_DAYS)],
            ['value' => self::VALUE_ETA_30_DAYS, 'label'=> __(self::LABEL_ETA_30_DAYS)],
        ];
    }
}

<?php

namespace MadHat\InventoryImport\Model;

use Magento\Catalog\Model\Product;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\InventoryApi\Api\Data\SourceItemInterface;
use Magento\InventoryApi\Api\Data\StockSourceLinkInterface;
use Magento\InventoryApi\Api\GetStockSourceLinksInterface;
use Magento\InventoryApi\Api\SourceItemRepositoryInterface;
use Magento\InventoryApi\Api\SourceItemsSaveInterface;
use Magento\InventorySalesApi\Api\Data\SalesChannelInterface;
use Magento\InventorySalesApi\Api\StockResolverInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class StockResolver
{
    public const CACHE_KEY_STOCK = 'MKS_STOCK_SOURCE';
    public const CACHE_LIFE_TIME = '14400'; // 4 hours
    /**
     * @var SourceItemRepositoryInterface
     */
    private $sourceItemRepository;
    private $sourceItemsSave;
    private $stockResolver;
    private $cache;
    private $searchCriteriaBuilder;
    private $getStockSourceLinks;
    private $storeManager;
    private $logger;

    public function __construct(
        SourceItemRepositoryInterface $sourceItemRepository,
        SourceItemsSaveInterface $sourceItemsSave,
        StockResolverInterface $stockResolver,
        CacheInterface $cache,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        GetStockSourceLinksInterface $getStockSourceLinks,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger
    ) {
        $this->sourceItemRepository = $sourceItemRepository;
        $this->sourceItemsSave = $sourceItemsSave;
        $this->stockResolver = $stockResolver;
        $this->cache = $cache;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->getStockSourceLinks = $getStockSourceLinks;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
    }
    /**
     * Decrease the stock for a product in a given source.
     *
     * @param string $sku
     * @param string $sourceCode
     * @param int    $quantity
     */
    public function decreaseStockForSource($sku, $sourceCode, $quantity)
    {
        try {
            $searchCriteria = $this->searchCriteriaBuilder->addFilter(
                SourceItemInterface::SKU,
                $sku,
            )->addFilter(
                SourceItemInterface::SOURCE_CODE,
                $sourceCode
            )->create();

            $sourceItems = $this->sourceItemRepository->getList($searchCriteria)->getItems();
            foreach ($sourceItems as $sourceItem) {
                $currentStock = $sourceItem->getQuantity();
                if ($currentStock >= $quantity) {
                    $sourceItem->setQuantity($currentStock - $quantity);
                }
            }
            $this->sourceItemsSave->execute($sourceItems);
        } catch (\Exception $ex) {
            $this->logger->error('decreaseStockForSource:'.$ex->getMessage());
        }
    }

    public function getSourceCodeByWebsiteId($websiteId)
    {
        $cacheKey = self::CACHE_KEY_STOCK.'_WEB_'.$websiteId;
        $stockSourceCode = '';

        try {
            // Return data from cache
            $cacheStockSourceCode = $this->cache->load($cacheKey);
            if ($cacheStockSourceCode) {
                return $cacheStockSourceCode;
            }

            $website = $this->storeManager->getWebsite($websiteId);
            $websiteCode = $website->getCode();
            $stock = $this->stockResolver->execute(SalesChannelInterface::TYPE_WEBSITE, $websiteCode);
            $stockId = $stock->getStockId();
            $sources = $this->getAssignedLinks($stockId);
            foreach ($sources as $source) {
                $stockSourceCode = $source->getSourceCode();

                break;
            }
            $this->cache->save(
                $stockSourceCode,
                $cacheKey,
                [],
                self::CACHE_LIFE_TIME
            );
        } catch (\Exception $ex) {
            $this->logger->error('getSourceCodeByWebsiteId:'.$ex->getMessage());
        }

        return $stockSourceCode;
    }

    /**
     * @return StockSourceLinkInterface[]
     */
    public function getAssignedLinks($stockId)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(StockSourceLinkInterface::STOCK_ID, $stockId)
            ->create();

        $result = [];
        foreach ($this->getStockSourceLinks->execute($searchCriteria)->getItems() as $link) {
            $result[$link->getSourceCode()] = $link;
        }

        return $result;
    }
}

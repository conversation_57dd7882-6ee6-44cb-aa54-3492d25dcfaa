<?php

namespace MadHat\InventoryImport\Model\Logger;

use Magento\Framework\Filesystem\DriverInterface;
use Magento\Framework\Logger\Handler\Base;
use Monolog\Logger;

class ProductStatusHandler extends Base
{
    protected $loggerType = Logger::INFO;
    protected $fileName = '/var/log/import/inventory_status_import.log';

    public function __construct(DriverInterface $filesystem, $filePath = null, $fileName = null)
    {
        $this->fileName = '/var/log/import/inventory_status_import' . date("Ymd") . '.log';
        parent::__construct($filesystem, $filePath, $fileName);
    }
}

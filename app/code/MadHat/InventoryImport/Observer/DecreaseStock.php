<?php

namespace MadHat\InventoryImport\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Sales\Api\OrderRepositoryInterface;
use MadHat\InventoryImport\Helper\ProductStatusConfig;
use MadHat\InventoryImport\Model\StockResolver;
use Magento\Sales\Api\Data\OrderInterface;
use Psr\Log\LoggerInterface;

class DecreaseStock implements ObserverInterface
{

    private $stockResolver;
    private $productStatusConfigHelper;
    private $orderRepository;
    private $logger;

    public function __construct(
        StockResolver $stockResolver,
        ProductStatusConfig $productStatusConfigHelper,
        OrderRepositoryInterface $orderRepository,
        LoggerInterface $logger
    ) {
        $this->stockResolver = $stockResolver;
        $this->productStatusConfigHelper = $productStatusConfigHelper;
        $this->orderRepository = $orderRepository;
        $this->logger = $logger;
    }

    /**
     * This method will be triggered after an order is placed to decrease stock from a source
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        if ($this->productStatusConfigHelper->ignoreReservation()) {
            $orderId = $observer->getEvent()->getOrderId();
            if ($orderId) {
                try {
                    $order = $this->orderRepository->get($orderId);
                    $orderItems = $order->getAllItems();
                    $websiteId = $order->getStore()->getWebsiteId();
                    $sourceCode = $this->stockResolver->getSourceCodeByWebsiteId($websiteId);
                    foreach ($orderItems as $orderItem) {
                        $sku = $orderItem->getSku();
                        $quantityOrdered = $orderItem->getQtyOrdered();
                        // Decrease stock from the source for this product
                        $this->stockResolver->decreaseStockForSource($sku, $sourceCode, $quantityOrdered);
                    }
                } catch (\Exception $ex) {
                    $this->logger->error('DecreaseStock:'.$ex->getMessage());
                }
            }
        }
    }
}

<?php

namespace MadHat\InventoryImport\Plugin;

use Magento\Catalog\Model\Product;
use MadHat\InventoryImport\Helper\ProductStatusConfig;
use MadHat\InventoryImport\Model\Logger\ProductStatusLogger;

abstract class AbstractPlugin
{
    protected $_productStatusConfig;
    protected $_productStatusLogger;
    public function __construct(
        ProductStatusConfig $productStatusConfig,
        ProductStatusLogger $productStatusLogger
    ) {
        $this->_productStatusConfig = $productStatusConfig;
        $this->_productStatusLogger = $productStatusLogger;
    }
    public function isInventoryOnStatus()
    {
        return $this->_productStatusConfig->isInventoryOnStatus();
    }
    public function isNoManageStock()
    {
        return $this->_productStatusConfig->isNotManageStock();
    }
    /**
     * @param Product$product
     *
     */
    public function isInStock($product)
    {
        return $this->_productStatusConfig->isInStock($product);
    }
}

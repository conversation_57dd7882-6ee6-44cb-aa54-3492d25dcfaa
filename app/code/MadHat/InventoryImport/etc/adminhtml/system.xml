<?xml version="1.0"?>
<!--
/**
 * MadHat_InventoryImport extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MADHAT License.
 *
 * @category  MadHat
 * @package   MadHat_InventoryImport
 * @copyright Copyright (c) 2021
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="cataloginventory">
            <group id="options">
                <field id="on_product_stock_status" translate="label" type="select" sortOrder="100" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Product Based on Stock Status</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[This will show product based on stock status. If <strong>Manage Stock</strong> enabled then, this will work based on <strong>Salable Quantity.</strong><br/><strong>Note:</strong> If you don't want to use based on <strong>Salable Quantity</strong>, then disable <strong>Stores > Configuration > Catalog > Inventory > Product Stock Options > Manage Stock > No</strong>]]></comment>
                </field>
                <field id="ingore_reservation" translate="label" type="select" sortOrder="101" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Ignore Reservation Behaviour</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[If enabled, will ignore default inventory reservation behaviour from Magneto.]]></comment>
                </field>
                <field id="enable_min_qty" translate="label comment" type="select" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Enabled Minimum Qty Feature</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Initially this feature is Required for Norway Websites</comment>
                </field>
                <field id="minimum_qty" translate="label" type="text" sortOrder="120" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Minimum Qty To Purchase (Multiplication)</label>
                    <depends>
                        <field id="enable_min_qty">1</field>
                    </depends>
                    <comment>Enter Minimum Qty To Purchase with multiplication of Minimum Qty</comment>
                </field>
            </group>
        </section>
    </system>
</config>

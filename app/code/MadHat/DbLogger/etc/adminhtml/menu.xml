<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="MadHat_DbLogger::system_dblogger" title="DB Logger" translate="title" module="MadHat_DbLogger"
             sortOrder="80" parent="Magento_Backend::system" resource="MadHat_DbLogger::system_dblogger"/>
        <add id="MadHat_DbLogger::dblogger" title="DB Logger" translate="title" module="MadHat_DbLogger" sortOrder="80"
             parent="MadHat_DbLogger::system_dblogger" action="dblogger/index" resource="MadHat_DbLogger::dblogger"/>
    </menu>
</config>

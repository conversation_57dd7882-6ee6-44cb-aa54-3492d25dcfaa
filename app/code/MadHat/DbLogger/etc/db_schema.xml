<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="madhat_db_logger" resource="default" comment="MadHat DB Logger">
        <column xsi:type="int" name="entity_id" identity="true" comment="Id"/>
        <column xsi:type="varchar" name="identifier" nullable="false" comment="Log Indentifier"/>
        <column xsi:type="varchar" name="title" nullable="false" comment="Log Title"/>
        <column xsi:type="varchar" name="type" nullable="false" comment="Log Type"/>
        <column xsi:type="longtext" name="message" nullable="false" comment="Log Message"/>
        <column xsi:type="datetime" name="created_at" on_update="false" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
    </table>
</schema>

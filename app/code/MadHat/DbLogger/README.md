# Mage2 Module MadHat DbLogger

    ``madhat/dblogger``

## Main Functionalities
Add log details in database and later developer can in Magento admin.
System -> DB Logger -> DB Logger

## Configuration
You can see configuration in magento admin, System -> Configuration -> MADHAT Config -> DB Logger

## Usage
When ever you want to add log just use below code and pass appropriate data in your file.

```
\MadHat\DbLogger\Logger\DbLoggerSaver::addLogRecord(
   'title',
   'message',
   'message type',
   'identifier'
);
```

(Possible identifier) 
   general
   product
   inventory
   price
   order
   order_export
   order_import
<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">dblogger_index_listing.dblogger_index_listing_data_source</item>
        </item>
    </argument>
    <settings>
        <spinner>dblogger_columns</spinner>
        <deps>
            <dep>dblogger_index_listing.dblogger_index_listing_data_source</dep>
        </deps>
    </settings>
    <dataSource name="dblogger_index_listing_data_source" component="Magento_Ui/js/grid/provider">
        <settings>
            <storageConfig>
                <param name="indexField" xsi:type="string">entity_id</param>
            </storageConfig>
            <updateUrl path="mui/index/render"/>
        </settings>
        <aclResource>MadHat_DbLogger::dblogger</aclResource>
        <dataProvider class="Magento\Cms\Ui\Component\DataProvider" name="dblogger_index_listing_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>entity_id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>
    <listingToolbar name="listing_top">
        <settings>
            <sticky>true</sticky>
        </settings>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filterSearch name="fulltext"/>
        <filters name="listing_filters">
            <settings>
                <templates>
                    <filters>
                        <select>
                            <param name="template" xsi:type="string">ui/grid/filters/elements/ui-select</param>
                            <param name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</param>
                        </select>
                    </filters>
                </templates>
            </settings>
            <filterSelect name="identifier" provider="${ $.parentName }">
                <settings>
                    <captionValue>1</captionValue>
                    <options class="MadHat\DbLogger\Ui\Component\Listing\Column\Identifier\Options"/>
                    <label translate="true">Identifier</label>
                    <dataScope>identifier</dataScope>
                    <imports>
                        <link name="visible">ns = ${ $.ns }, index = ${ $.index }:visible</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="dblogger_columns">
        <column name="entity_id">
            <settings>
                <filter>textRange</filter>
                <label translate="true">ID</label>
                <sorting>asc</sorting>
            </settings>
        </column>
        <column name="identifier">
            <settings>
                <label translate="true">Identifier</label>
            </settings>
        </column>
        <column name="title">
            <settings>
                <filter>text</filter>
                <label translate="true">Title</label>
            </settings>
        </column>
        <column name="type">
            <settings>
                <filter>text</filter>
                <label translate="true">Type</label>
            </settings>
        </column>
        <column name="message">
            <settings>
                <filter>text</filter>
                <label translate="true">message</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created</label>
            </settings>
        </column>
        <actionsColumn name="actions" class="MadHat\DbLogger\Ui\Component\Listing\Column\DbloggerActions">
            <settings>
                <indexField>entity_id</indexField>
            </settings>
        </actionsColumn>
    </columns>
</listing>

<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Model\ResourceModel\Doptions;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'doptions_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \MadHat\SiteIntegrationProducts\Model\Doptions::class,
            \MadHat\SiteIntegrationProducts\Model\ResourceModel\Doptions::class
        );
    }
}

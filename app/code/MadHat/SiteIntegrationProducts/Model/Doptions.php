<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Model;

use MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface;
use Magento\Framework\Model\AbstractModel;

class Doptions extends AbstractModel implements DoptionsInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\MadHat\SiteIntegrationProducts\Model\ResourceModel\Doptions::class);
    }

    /**
     * @inheritDoc
     */
    public function getDoptionsId()
    {
        return $this->getData(self::DOPTIONS_ID);
    }

    /**
     * @inheritDoc
     */
    public function setDoptionsId($doptionsId)
    {
        return $this->setData(self::DOPTIONS_ID, $doptionsId);
    }

    /**
     * @inheritDoc
     */
    public function getValue()
    {
        return $this->getData(self::VALUE);
    }

    /**
     * @inheritDoc
     */
    public function setValue($value)
    {
        return $this->setData(self::VALUE, $value);
    }

    /**
     * @inheritDoc
     */
    public function getLabel()
    {
        return $this->getData(self::LABEL);
    }

    /**
     * @inheritDoc
     */
    public function setLabel($label)
    {
        return $this->setData(self::LABEL, $label);
    }

    /**
     * @inheritDoc
     */
    public function getType()
    {
        return $this->getData(self::TYPE);
    }

    /**
     * @inheritDoc
     */
    public function setType($type)
    {
        return $this->setData(self::TYPE, $type);
    }

    /**
     * @inheritDoc
     */
    public function getDescription()
    {
        return $this->getData(self::DESCRIPTION);
    }

    /**
     * @inheritDoc
     */
    public function setDescription($description)
    {
        return $this->setData(self::DESCRIPTION, $description);
    }
}

<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Model\Product\Attribute\Source;

use MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface;
use MadHat\SiteIntegrationProducts\Api\DoptionsRepositoryInterface;
use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Cache\Type\Config;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Exception\LocalizedException;

abstract class MadHatDoptions extends AbstractSource implements OptionSourceInterface
{
    /**
     * @var string
     */
    public string $cacheKey = 'MADHAT_CACHE_KEY_REPLACE_WITH_UNIQUE';

    /**
     * @var string
     */
    public string $type = 'Type_Replace_With_Unique';

    /**
     * @var DoptionsRepositoryInterface
     */
    protected DoptionsRepositoryInterface $doptionsRepositoryInterface;

    /**
     * @var SearchCriteriaBuilder
     */
    protected SearchCriteriaBuilder $searchCriteriaBuilder;

    /**
     * @var Config
     */
    protected Config $configCacheType;

    /**
     * @param DoptionsRepositoryInterface $doptionsRepositoryInterface
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param Config $configCacheType
     */
    public function __construct(
        DoptionsRepositoryInterface $doptionsRepositoryInterface,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        Config $configCacheType
    ) {
        $this->doptionsRepositoryInterface = $doptionsRepositoryInterface;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->configCacheType = $configCacheType;
    }

    /**
     * Get All Options
     *
     * @return array
     * @throws LocalizedException
     */
    public function getAllOptions(): array
    {
        $options = $this->getData();

        array_unshift($options, $this->getEmptyOption());
        return $options;
    }

    /**
     * Add Empty Option
     *
     * @return array
     */
    private function getEmptyOption(): array
    {
        return ['value' => 0, 'label' => __('No Value')];
    }

    /**
     * Get Options Data for MadHat Custom Attribute
     *
     * @return array
     * @throws LocalizedException
     */
    private function getData(): array
    {
        $options = [];
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(DoptionsInterface::TYPE, $this->type)->create();
        foreach ($this->doptionsRepositoryInterface->getList($searchCriteria)->getItems() as $item) {
            $options[] = ['value' => $item->getValue(), 'label' => __($item->getLabel())];
        }
        return $options;
    }
}

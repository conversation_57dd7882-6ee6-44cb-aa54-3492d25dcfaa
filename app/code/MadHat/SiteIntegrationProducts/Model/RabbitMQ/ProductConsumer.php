<?php
/**
 * Get Product data from RabbitMQ
 */

namespace MadHat\SiteIntegrationProducts\Model\RabbitMQ;

use MadHat\SiteIntegrationProducts\Api\Data\ProductDataInterface;
use MadHat\SiteIntegrationProducts\Logger\Logger;
use MadHat\SiteIntegrationProducts\Model\ProductProcessor;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Api\WebsiteRepositoryInterface;

class ProductConsumer
{
    /**
     * @var array|string[]
     */
    private array $websites = [
        'TechnologyOutlet' => 'base',
        '3DPrima' => 'prima3d'
    ];

    /**
     * @var Logger
     */
    private Logger $logger;

    /**
     * @var ProductProcessor
     */
    private ProductProcessor $productProcessor;

    /**
     * @var WebsiteRepositoryInterface
     */
    private WebsiteRepositoryInterface $websiteRepository;

    /**
     * @param ProductProcessor $productProcessor
     * @param Logger $logger
     * @param WebsiteRepositoryInterface $websiteRepository
     */
    public function __construct(
        ProductProcessor $productProcessor,
        Logger $logger,
        WebsiteRepositoryInterface $websiteRepository
    ) {
        $this->productProcessor = $productProcessor;
        $this->logger = $logger;
        $this->websiteRepository = $websiteRepository;
    }

    /**
     * Consumer process start
     *
     * @param ProductDataInterface[] $message
     * @param int $websiteId
     * @return void
     */
    public function processMessage(array $message, int $websiteId): void
    {
        $this->logger->info("ProductConsumer::processMessage starts");
//        $this->logger->info(__(
//            '%1 => %2 Received Message: %3',
//            __CLASS__,
//            __FUNCTION__,
//            print_r($message, true)
//        ));
        try {
            $this->productProcessor->processMessage($message, $websiteId);
        } catch (\InvalidArgumentException $exception) {
            $this->logger->error(__(
                '%1 => %2 ERROR: %3',
                __CLASS__,
                __FUNCTION__,
                $exception->getMessage()
            ));
        }
        $this->logger->info("ProductConsumer::processMessage completed.");
    }

    /**
     * Process Product Data for TechnologyOutlet website
     *
     * @param array $message
     * @return void
     * @throws NoSuchEntityException
     */
    public function processTechOutletProducts(array $message): void
    {
        $websiteId = $this->websiteRepository->get($this->websites['TechnologyOutlet'])->getId();
        $this->processMessage($message, $websiteId);
    }

    /**
     * Process Product Data for 3DPrima website
     *
     * @param array $message
     * @return void
     * @throws NoSuchEntityException
     */
    public function processPrimaProducts(array $message): void
    {
        $websiteId = $this->websiteRepository->get($this->websites['3DPrima'])->getId();
        $this->processMessage($message, $websiteId);
    }
}

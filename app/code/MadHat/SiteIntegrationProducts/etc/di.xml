<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="MadHat\SiteIntegrationProducts\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="MadHat\SiteIntegrationProducts\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">SITE_ProductsLogHandler</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">MadHat\SiteIntegrationProducts\Logger\Handler</item>
            </argument>
        </arguments>
    </type>

    <!-- Commands-->
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="SyncBaseProducts" xsi:type="object">MadHat\SiteIntegrationProducts\Console\Command\SyncBaseProducts</item>
            </argument>
        </arguments>
    </type>

    <preference for="MadHat\SiteIntegrationProducts\Api\Data\ProductDataInterface"
                type="MadHat\SiteIntegrationProducts\Model\Data\ProductData"/>
    <preference for="MadHat\SiteIntegrationProducts\Api\Data\ProductAttributeDataInterface"
                type="MadHat\SiteIntegrationProducts\Model\Data\ProductAttributeData"/>

    <!-- VariantProductDataInterface preference -->
    <preference for="MadHat\SiteIntegrationProducts\Api\Data\VariantProductDataInterface"
                type="MadHat\SiteIntegrationProducts\Model\Data\VariantProduct"/>

    <!-- VariantAttributeDataInterface preference -->
    <preference for="MadHat\SiteIntegrationProducts\Api\Data\VariantAttributeDataInterface"
                type="MadHat\SiteIntegrationProducts\Model\Data\VariantAttribute"/>

    <preference for="MadHat\SiteIntegrationProducts\Api\Data\BundledProductDataInterface"
                type="MadHat\SiteIntegrationProducts\Model\Data\BundledProduct"/>

    <preference for="MadHat\SiteIntegrationProducts\Api\MadHatMappingRepositoryInterface"
                type="MadHat\SiteIntegrationProducts\Model\MadHatMappingRepository"/>
    <preference for="MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingInterface"
                type="MadHat\SiteIntegrationProducts\Model\MadHatMapping"/>
    <preference for="MadHat\SiteIntegrationProducts\Api\Data\MadHatMappingSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>

    <preference for="MadHat\SiteIntegrationProducts\Api\DoptionsRepositoryInterface"
                type="MadHat\SiteIntegrationProducts\Model\DoptionsRepository"/>
    <preference for="MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface"
                type="MadHat\SiteIntegrationProducts\Model\Doptions"/>
    <preference for="MadHat\SiteIntegrationProducts\Api\Data\DoptionsSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>
</config>

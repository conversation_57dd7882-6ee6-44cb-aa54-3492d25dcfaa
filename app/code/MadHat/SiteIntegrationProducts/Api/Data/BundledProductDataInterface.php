<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Api\Data;

/**
 * Interface VariantAttributeDataInterface
 */
interface BundledProductDataInterface
{
    /**
     * Get product no
     *
     * @return string|null
     */
    public function getProductNo(): ?string;

    /**
     * Set product no
     *
     * @param string|null $productNo
     * @return void
     */
    public function setProductNo(?string $productNo): void;

    /**
     * Get quantity value
     *
     * @return int|null
     */
    public function getQuantity(): ?int;

    /**
     * Set quantity value
     *
     * @param int|null $quantity
     * @return void
     */
    public function setQuantity(?int $quantity): void;
}

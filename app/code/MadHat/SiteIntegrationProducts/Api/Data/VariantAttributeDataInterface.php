<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Api\Data;

/**
 * Interface VariantAttributeDataInterface
 */
interface VariantAttributeDataInterface
{
    /**
     * Get the name of the variant attribute.
     *
     * @return string|null
     */
    public function getName(): ?string;

    /**
     * Set the name of the variant attribute.
     *
     * @param string|null $name
     * @return void
     */
    public function setName(?string $name): void;

    /**
     * Get the value of the variant attribute.
     *
     * @return string|null
     */
    public function getValue(): ?string;

    /**
     * Set the value of the variant attribute.
     *
     * @param string|null $value
     * @return void
     */
    public function setValue(?string $value): void;

    /**
     * Get the description for the variant attribute.
     *
     * @return string|null
     */
    public function getDescription(): ?string;

    /**
     * Set the description for the variant attribute.
     *
     * @param string|null $description
     * @return void
     */
    public function setDescription(?string $description): void;

    /**
     * Get the label for the variant attribute.
     *
     * @return string|null
     */
    public function getLabel(): ?string;

    /**
     * Set the label for the variant attribute.
     *
     * @param string|null $label
     * @return void
     */
    public function setLabel(?string $label): void;
}

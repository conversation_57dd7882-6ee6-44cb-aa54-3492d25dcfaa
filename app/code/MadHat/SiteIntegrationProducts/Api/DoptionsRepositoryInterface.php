<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface DoptionsRepositoryInterface
{

    /**
     * Save Doptions
     *
     * @param \MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface $doptions
     * @return \MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface $doptions
    );

    /**
     * Retrieve Doptions
     *
     * @param string $doptionsId
     * @return \MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($doptionsId);

    /**
     * Retrieve Doptions matching the specified criteria.
     *
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \MadHat\SiteIntegrationProducts\Api\Data\DoptionsSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Doptions
     *
     * @param \MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface $doptions
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \MadHat\SiteIntegrationProducts\Api\Data\DoptionsInterface $doptions
    );

    /**
     * Delete Doptions by ID
     *
     * @param string $doptionsId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($doptionsId);
}

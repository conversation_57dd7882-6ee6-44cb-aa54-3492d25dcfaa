<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationProducts\Setup\Patch\Data;

use Magento\Eav\Model\Config;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Type;
use Magento\Catalog\Model\ResourceModel\Eav\Attribute as EavAttribute;
use Psr\Log\LoggerInterface;

class ProductAttributeAddPatch1 implements DataPatchInterface
{
    protected const ATTRIBUTE_CODE = 'madhat_base_color';

    protected const ATTRIBUTE_LABEL = 'Madhat Base Color';
    /**
     * @var array
     */
    protected array $colorMap = [
        'Magenta' => '#530367',
        'Orange' => '#eb6703',
        'Clear' => '#000000',
        'Black' => '#000000',
        'White' => '#ffffff',
        'Red' => '#ff0000',
        'Green' => '#53a828',
        'Blue' => '#1857f7',
        'Yellow' => '#ffd500',
        'Gold' => '#ffd500',
        'Silver' => '#fffff',
        'Purple' => '#ef3dff',
        'Pink' => '#fff000',
        'Bronze' => '#000fff',
        'Brown' => '#945454',
        'Grey' => '#8f8f8f',
        'Multicolor' => '#ffffff',
        'Natural' => '#123fff',
        'Beige' => '#123456',
        'Brass' => '#fff456',
        'Copper' => '#ff3456'
    ];

    /**
     * @var array
     */
    protected array $optionCollection = [];
    /**
     * @var ModuleDataSetupInterface
     */
    private ModuleDataSetupInterface $moduleDataSetup;
    /**
     * @var EavSetupFactory
     */
    private EavSetupFactory $eavSetupFactory;
    /**
     * @var CollectionFactory
     */
    private CollectionFactory $attrOptionCollectionFactory;
    /**
     * @var Config
     */
    private Config $eavConfig;
    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param CollectionFactory $attrOptionCollectionFactory
     * @param Config $eavConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory,
        CollectionFactory $attrOptionCollectionFactory,
        Config $eavConfig,
        LoggerInterface $logger
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->attrOptionCollectionFactory = $attrOptionCollectionFactory;
        $this->eavConfig = $eavConfig;
        $this->logger = $logger;
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies(): array
    {
        return [];
    }

    /**
     * @return void
     */
    public function apply(): void
    {
        try {
            $productTypes = implode(',', [Type::TYPE_SIMPLE, Type::TYPE_VIRTUAL]);

            /** @var EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

            $eavSetup->addAttribute(Product::ENTITY, self::ATTRIBUTE_CODE, [
                    'type' => 'int',
                    'label' => self::ATTRIBUTE_LABEL,
                    'input' => 'select',
                    'required' => false,
                    'user_defined' => true,
                    'searchable' => true,
                    'filterable' => true,
                    'comparable' => true,
                    'visible_in_advanced_search' => true,
                    'apply_to' => $productTypes,
                    'is_used_in_grid' => true,
                    'is_visible_in_grid' => false,
                    'option' => [
                        'values' => [
                            'Magenta',
                            'Orange',
                            'Clear',
                            'Black',
                            'White',
                            'Red',
                            'Green',
                            'Blue',
                            'Yellow',
                            'Gold',
                            'Silver',
                            'Purple',
                            'Pink',
                            'Bronze',
                            'Brown',
                            'Grey',
                            'Multicolor',
                            'Natural',
                            'Beige',
                            'Brass',
                            'Copper'
                        ]
                    ]
                ]);

            $this->eavConfig->clear();
            $this->convertAttributeToSwatches();

        } catch (\Exception $e) {
            $this->logger->critical($e);

        }
    }

    /**
     * @return void
     * @throws LocalizedException
     */
    public function convertAttributeToSwatches(): void
    {
        $attribute = $this->eavConfig->getAttribute('catalog_product', self::ATTRIBUTE_CODE);
        if (!$attribute) {
            return;
        }

        $attributeData['option'] = $this->addExistingOptions($attribute);
        $attributeData['frontend_input'] = 'select';
        $attributeData['swatch_input_type'] = 'visual';
        $attributeData['update_product_preview_image'] = 1;
        $attributeData['use_product_image_for_swatch'] = 0;
        $attributeData['optionvisual'] = $this->getOptionSwatch($attributeData);
        $attributeData['defaultvisual'] = $this->getOptionDefaultVisual($attributeData);
        $attributeData['swatchvisual'] = $this->getOptionSwatchVisual($attributeData);
        $this->logger->log(100, print_r($attributeData, true));
        $attribute->addData($attributeData);
        $attribute->save();
    }

    /**
     * @param EavAttribute $attribute
     * @return array
     */
    private function addExistingOptions(eavAttribute $attribute): array
    {
        $options = [];
        $attributeId = $attribute->getId();
        if ($attributeId) {
            $this->loadOptionCollection($attributeId);
            /** @var \Magento\Eav\Model\Entity\Attribute\Option $option */
            foreach ($this->optionCollection[$attributeId] as $option) {
                $options[$option->getId()] = $option->getValue();
            }
        }
        return $options;
    }

    /**
     * @param int $attributeId
     * @return void
     */
    private function loadOptionCollection($attributeId): void
    {
        if (empty($this->optionCollection[$attributeId])) {
            $this->optionCollection[$attributeId] = $this->attrOptionCollectionFactory->create()
                ->setAttributeFilter($attributeId)
                ->setPositionOrder('asc', true)
                ->load();
        }
    }

    /**
     * @param array $attributeData
     * @return array
     */
    protected function getOptionSwatch(array $attributeData): array
    {
        $optionSwatch = ['order' => [], 'value' => [], 'delete' => []];
        $i = 0;
        foreach ($attributeData['option'] as $optionKey => $optionValue) {
            $optionSwatch['delete'][$optionKey] = '';
            $optionSwatch['order'][$optionKey] = (string)$i++;
            $optionSwatch['value'][$optionKey] = [$optionValue, ''];
        }
        return $optionSwatch;
    }

    /**
     * @param array $attributeData
     * @return array
     */
    private function getOptionDefaultVisual(array $attributeData): array
    {
        $optionSwatch = $this->getOptionSwatchVisual($attributeData);
        return [array_keys($optionSwatch['value'])[0]];
    }

    /**
     * @param array $attributeData
     * @return array
     */
    private function getOptionSwatchVisual(array $attributeData): array
    {
        $optionSwatch = ['value' => []];
        foreach ($attributeData['option'] as $optionKey => $optionValue) {
            if (substr($optionValue, 0, 1) == '#' && strlen($optionValue) == 7) {
                $optionSwatch['value'][$optionKey] = $optionValue;
            } elseif (!empty($this->colorMap[$optionValue])) {
                $optionSwatch['value'][$optionKey] = $this->colorMap[$optionValue];
            } else {
                $optionSwatch['value'][$optionKey] = $this->colorMap['White'];
            }
        }
        return $optionSwatch;
    }

    /**
     * @inheritDoc
     */
    public function getAliases(): array
    {
        return [];
    }
}

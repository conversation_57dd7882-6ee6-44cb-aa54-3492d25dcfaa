<?php

namespace MadHat\SiteIntegrationProducts\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Catalog\Model\Product;

class AddMadHatTechnicalDataProductAttribute implements DataPatchInterface
{
    private $eavSetupFactory;
    private $moduleDataSetup;
    private Product\Attribute\Repository $attributeRepository;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory,
        Product\Attribute\Repository $attributeRepository
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->attributeRepository = $attributeRepository;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);

        $eavSetup->addAttribute(
            Product::ENTITY,
            'madhat_technical_data',
            [
                'type' => 'text',
                'backend' => '',
                'frontend' => '',
                'label' => 'Madhat Technical Data',
                'input' => 'textarea',
                'class' => '',
                'source' => '',
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                'visible' => true,
                'required' => false,
                'user_defined' => true,
                'default' => '',
                'searchable' => false,
                'filterable' => false,
                'comparable' => false,
                'visible_on_front' => true,
                'used_in_product_listing' => false,
                'unique' => false,
                'apply_to' => '',
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
            ]
        );

        $attribute = $this->attributeRepository->get('madhat_technical_data');
        // Assign attribute to each attribute set.
        $attributeSetNames = [
            'Default',
            'Accessories Upgrades',
            'Accessories Upgrades (Nozzle Diameter)',
            'Laser Engraver & Cutter',
            'Printing Material'
        ];

        foreach ($attributeSetNames as $attributeSetName) {
            $attributeSetId = $eavSetup->getAttributeSetId(Product::ENTITY, $attributeSetName);
            $attributeGroupId = $eavSetup->getDefaultAttributeGroupId(Product::ENTITY, $attributeSetId);

            $eavSetup->addAttributeToSet(
                Product::ENTITY,
                $attributeSetId,
                $attributeGroupId,
                (int) $attribute['attribute_id'],
                100
            );
        }

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    public function getAliases()
    {
        return [];
    }

    public static function getDependencies()
    {
        return [
            AddAccessoriesUpgradesProductAttributeSet::class,
            AddAccessoriesUpgradesNozzleProductAttributeSet::class,
            AddLaserEngraverCutterProductAttributeSet::class,
            AddPrintingMaterialProductAttributeSet::class,
        ];
    }
}

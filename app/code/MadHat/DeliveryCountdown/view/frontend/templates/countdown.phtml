<?php
/**
 * @var \Magento\Framework\View\Element\Template $block
 * @var \MadHat\DeliveryCountdown\ViewModel\Countdown $viewModel
 */
$viewModel = $block->getViewModel();
$countdownData = $viewModel->getCountdownData();

// Guard: if feature disabled or data missing, render nothing
if (!$countdownData || empty($countdownData['target_timestamp'])) {
    return;
}
?>

<div
    class="mt-3 rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
    x-data="countdownTimer(<?= (int) $countdownData['target_timestamp'] ?>)"
    x-init="init()"
    role="timer"
    aria-live="polite"
>
    <div class="flex items-center gap-3">
        <span class="inline-flex h-8 w-8 items-center justify-center rounded-full bg-primary text-white">
            <!-- Clock icon -->
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="h-5 w-5">
                <path fill-rule="evenodd" d="M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm0 1.5a8.25 8.25 0 100 16.5 8.25 8.25 0 000-16.5zM12.75 6a.75.75 0 00-1.5 0v6c0 .*************.53l3.75 3.75a.75.75 0 101.06-1.06l-3.53-3.53V6z" clip-rule="evenodd" />
            </svg>
        </span>

        <div class="flex flex-wrap items-center gap-2 text-sm text-gray-800 dark:text-gray-100">
            <span class="font-medium"><?= __("Order within") ?></span>

            <span class="countdown-timer inline-flex items-center gap-1">
                <span class="inline-flex items-center rounded bg-primary/10 px-2 py-1 text-primary">
                    <span class="tabular-nums" x-text="hours">00</span>
                    <span class="ml-1 hidden sm:inline"><?= __("hrs") ?></span>
                </span>
                <span class="inline-flex items-center rounded bg-primary/10 px-2 py-1 text-primary">
                    <span class="tabular-nums" x-text="minutes">00</span>
                    <span class="ml-1 hidden sm:inline"><?= __("mins") ?></span>
                </span>
                <span class="inline-flex items-center rounded bg-primary/10 px-2 py-1 text-primary">
                    <span class="tabular-nums" x-text="seconds">00</span>
                    <span class="ml-1 hidden sm:inline"><?= __("secs") ?></span>
                </span>
            </span>

            <span class="text-gray-600 dark:text-gray-300"><?= __("to get it by") ?></span>
            <span class="font-medium text-gray-900 dark:text-white">
                <?= htmlspecialchars_decode($countdownData['message']) /* contains translated delivery day text */ ?>
            </span>
        </div>
    </div>
</div>

<script>
function countdownTimer(targetTimestamp) {
    return {
        target: new Date(targetTimestamp * 1000),
        interval: null,
        hours: '00',
        minutes: '00',
        seconds: '00',
        init() {
            this.updateCountdown();
            this.interval = setInterval(() => this.updateCountdown(), 1000);
        },
        updateCountdown() {
            const now = new Date();
            const diff = this.target.getTime() - now.getTime();
            if (diff <= 0) {
                clearInterval(this.interval);
                this.hours = this.minutes = this.seconds = '00';
                return;
            }
            this.hours = String(Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))).padStart(2, '0');
            this.minutes = String(Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))).padStart(2, '0');
            this.seconds = String(Math.floor((diff % (1000 * 60)) / 1000)).padStart(2, '0');
        }
    }
}
</script>

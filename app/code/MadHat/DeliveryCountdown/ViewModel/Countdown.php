<?php
declare(strict_types=1);

namespace MadHat\DeliveryCountdown\ViewModel;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class Countdown implements ArgumentInterface
{
    /**
     * @var TimezoneInterface
     */
    private $timezone;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var array
     */
    private $configData;

    /**
     * @param TimezoneInterface $timezone
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        TimezoneInterface $timezone,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->timezone = $timezone;
        $this->scopeConfig = $scopeConfig;
        $this->configData = $this->getConfigData();
    }

    /**
     * Get configuration data
     *
     * @return array
     */
    private function getConfigData(): array
    {
        return [
            'enabled' => (bool)$this->scopeConfig->getValue(
                'madhat_deliverycountdown/countdown_timer/enabled',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
            ),
            'cutoff_time' => $this->scopeConfig->getValue(
                'madhat_deliverycountdown/countdown_timer/cutoff_time',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
            ) ?: '12:00',
            'delivery_hour' => (int)$this->scopeConfig->getValue(
                'madhat_deliverycountdown/countdown_timer/delivery_hour',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
            ) ?: 12,
            'delivery_minute' => (int)$this->scopeConfig->getValue(
                'madhat_deliverycountdown/countdown_timer/delivery_minute',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
            ) ?: 0
        ];
    }

    /**
     * Get configured timezone
     *
     * @return string
     */
    private function getConfiguredTimezone(): string
    {
        return $this->scopeConfig->getValue(
            'general/locale/timezone',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        ) ?: 'UTC';
    }

    /**
     * @return array
     * @throws \Exception
     */
    public function getCountdownData(): array
    {
        if (!$this->configData['enabled']) {
            return [];
        }

        $timezone = new \DateTimeZone($this->getConfiguredTimezone());
        $now = new \DateTime('now', $timezone);
        
        // Parse the cutoff time
        list($cutoffHour, $cutoffMinute) = explode(':', $this->configData['cutoff_time']);
        $cutoffTime = sprintf('%02d:%02d', $cutoffHour, $cutoffMinute);
        
        $cutOff = new \DateTime($now->format('Y-m-d') . ' ' . $cutoffTime, $timezone);

        $dayOfWeek = (int)$now->format('N'); // 1 (for Monday) through 7 (for Sunday)
        $isAfterCutoff = $now > $cutOff;

        $targetDate = new \DateTime('now', $timezone);
        $targetDate->setTime($this->configData['delivery_hour'], $this->configData['delivery_minute'], 0);

        $deliveryDay = '';

        if ($dayOfWeek >= 1 && $dayOfWeek <= 4) { // Monday to Thursday
            if ($isAfterCutoff) {
                $targetDate->modify('+1 day');
                $deliveryDay = __('Monday');
            } else {
                $deliveryDay = __('next working day');
            }
        } elseif ($dayOfWeek == 5) { // Friday
            if ($isAfterCutoff) {
                $targetDate->modify('next monday');
                $deliveryDay = __('Tuesday');
            } else {
                $deliveryDay = __('Monday');
            }
        } else { // Saturday and Sunday
            $targetDate->modify('next monday');
            $deliveryDay = __('Tuesday');
        }

        if ($dayOfWeek == 4 && !$isAfterCutoff) {
            $deliveryDay = __('Friday');
        }

        if ($dayOfWeek == 4 && $isAfterCutoff) {
            $targetDate->modify('next monday');
            $deliveryDay = __('Tuesday');
        }

        if ($dayOfWeek == 5 && !$isAfterCutoff) {
            $deliveryDay = __('Monday');
        }

        if ($dayOfWeek < 4 && !$isAfterCutoff) {
            $deliveryDay = __('tomorrow');
        }

        $message = __("Order within <span class='countdown-timer'></span> to get it by %1", $deliveryDay);

        return [
            'target_timestamp' => $targetDate->getTimestamp(),
            'message' => $message,
        ];
    }
}

<?php

namespace MadHat\ImportReview\Model;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Review\Model\ReviewFactory;
use Magento\Review\Model\ResourceModel\Review as ReviewResource;
use Magento\Store\Model\StoreManagerInterface;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable;
use MadHat\SiteIntegrationProducts\Logger\Logger;

class ImportReview
{
    protected $resource;
    protected $reviewFactory;
    protected $reviewResource;
    protected $productRepository;
    protected $storeManager;
    protected $configurable;
    protected $logger;


    public function __construct(
        ResourceConnection $resource,
        ReviewFactory $reviewFactory,
        ReviewResource $reviewResource,
        ProductRepositoryInterface $productRepository,
        StoreManagerInterface $storeManager,
        Configurable $configurable,
        Logger $logger
    ) {
        $this->resource = $resource;
        $this->reviewFactory = $reviewFactory;
        $this->reviewResource = $reviewResource;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
        $this->configurable = $configurable;
        $this->logger = $logger;
    }

    public function importReviews(array $data)
    {
        $connection = $this->resource->getConnection();
        //$defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        foreach ($data as $row) {
            if (isset($row[0], $row[1], $row[2], $row[3], $row[4], $row[5], $row[6])) {
                try {
                    $product = false;
                    $product = $this->productRepository->get($row[0]);

                    if ($product) {
                        // Create review
                        $review = $this->reviewFactory->create();
                        $review->setEntityId(1); // 1 for product reviews
                        $review->setEntityPkValue($product->getId()); // Product ID
                        //if ($product->getTypeId() == 'configurable') {
                        $productID = $this->configurable->getParentIdsByChild($product->getId());
                        if (!empty($productID)) {
                            if(is_array($productID)){
                                $productID = $productID[0];
                            }
                            $review->setEntityPkValue((int) $productID); // Product ID
                        } else{
                            $productID = (int) $product->getId();
                            $review->setEntityPkValue($productID);
                        }
                        //}
                        $review->setStatusId(1); // Approved
                        $review->setTitle($row[1]); // Review Title
                        $review->setDetail($row[2]); // Review Details
                        $review->setNickname($row[3]); // Customer Nickname
                        $review->setIsVerified(1); // Custom attribute is Verified
                        $createdDate = date('Y-m-d H:i:s', strtotime($row[6]));
                        $imageFile = $row[7];
                        $review->setStores([0,1,3]); // Custom attribute is Verified
                        if ((int) $row[5] >= 3) {
                            $review->setIsRecommend(1); // setIsRecommend true
                        }
                        $review->setCreatedAt($this->convertDate($createdDate)); // Created date
                        if (!empty($row[4])) {
                            $customerEmail = $row[4];
                            // Retrieve customer ID by email if available, otherwise leave it null for guest reviews
                            $customerID = $this->getCustomerIDByEmail($customerEmail);
                            $review->setCustomerId($customerID ?? null);
                        }

                        $this->reviewResource->save($review);
                        $this->logger->info(__('review Saved sku : %1', $product->getSku()));

                        $option_id = '';
                        // Associate rating with the review
                        switch ($row[5]) {
                            case 1:
                                $option_id = 16;
                                break;
                            case 2:
                                $option_id = 17;
                                break;
                            case 3:
                                $option_id = 18;
                                break;
                            case 4:
                                $option_id = 19;
                                break;
                            case 5:
                                $option_id = 20;
                                break;
                            default :
                                $option_id = 16;
                                break;
                        }

                        $connection->insert(
                            $connection->getTableName('rating_option_vote'),
                            [
                                'option_id' => $option_id, // Rating Option ID (e.g., 1-5)
                                'review_id' => $review->getId(),
                                'entity_pk_value' => $productID,
                                'rating_id' => 4,
                                'percent' => $row[5] * 20, // Percent based on rating
                                'value' => $row[5], // Rating value
                            ]
                        );

                        $this->logger->info(__('review rating_option_vote Done sku : %1', $product->getSku()));

                        $connection->insert(
                            $connection->getTableName('mageworx_xreviewbase_review_media'),
                            [
                                'review_id' => $review->getId(),
                                'product_id' => $productID,
                                'media_type' => 'image',
                                'file' => $imageFile, // file details
                                'label' => '', // label
                                'position' => 0, // label
                                'disabled' => 0, // label
                            ]
                        );
                        $this->logger->info(__('review mageworx_xreviewbase_review_media Done sku : %1', $product->getSku()));

                        // final code for aggregate all review for sku
                        $review->aggregate();
                        $this->logger->info(__('review aggregate Done : %1', $product->getSku()));

                    }
                } catch (\Exception $e) {
                    $this->logger->info(__('review mageworx_xreviewbase_review_media Done sku : %1', $row[0]));
                    $this->logger->error(__('ERROR **** review sku : %1',$row[0]));
                    $this->logger->error(__($e->getMessage()));
                    continue;
                }
            }
        }
    }

    /**
     * Converts date to mysql formatted date
     *
     * @param string $date
     * @return string convertedDate
     */
    public function convertDate($date)
    {
        $timestamp = strtotime($date);
        return date("Y-m-d H:i:s", $timestamp);
    }

    protected function getCustomerIDByEmail($email)
    {
        $connection = $this->resource->getConnection();
        $customerTable = $connection->getTableName('customer_entity');

        $select = $connection->select()
            ->from($customerTable, ['entity_id'])
            ->where('email = ?', $email)
            ->limit(1);

        $customerId = $connection->fetchOne($select);

        if ($customerId) {
            return $customerId;
        }

        return null;
    }

    /**
     *
     */
    public function getUserId($email)
    {
        $customer = $this->customerFactory->create();
        $customer->setWebsiteId($this->storeID);
        $customer->loadByEmail($email);

        return $customer->getEntityId();
    }
}

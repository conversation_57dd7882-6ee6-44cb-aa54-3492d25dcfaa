<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="madhat" translate="label" sortOrder="1">
            <label>MadHat</label>
        </tab>
        <section id="site_core" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0" translate="label">
            <label>SITE Integration Core</label>
            <tab>madhat</tab>
            <resource>MadHat_SiteIntegrationCore::madhat_site_core_config</resource>
            <group id="general" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0" translate="label">
                <label>General</label>
                <field id="api_url" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>API URL</label>
                    <comment>API URL for SITE Integration</comment>
                </field>
                <field id="company_id" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Company ID</label>
                    <comment>Company ID for SITE Integration</comment>
                </field>
                <field id="secret_key" type="obscure" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label">
                    <label>Secret Key</label>
                    <comment>Secret Key for SITE Integration</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                </field>
                <field id="integration_mode" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0"
                       translate="label" canRestore="1">
                    <label>Order Status Integration Mode</label>
                    <comment><![CDATA[
                        <strong>Choose how order status updates are processed:</strong><br/>
                        <strong>• Cron (Scheduled Tasks):</strong> Order export, sync, and payment processing run via scheduled cron jobs every 15 minutes. Use this for traditional polling-based integration.<br/>
                        <strong>• RabbitMQ (Message Queue):</strong> Order status updates and cancellations are processed immediately via RabbitMQ message consumers. Cron jobs are disabled. Use this for real-time event-driven integration.<br/>
                        <em>Note: When switching modes, ensure the corresponding services (cron or RabbitMQ consumers) are properly configured and running.</em>
                    ]]></comment>
                    <source_model>MadHat\SiteIntegrationCore\Model\Config\Source\IntegrationMode</source_model>
                </field>
            </group>
        </section>
    </system>
</config>

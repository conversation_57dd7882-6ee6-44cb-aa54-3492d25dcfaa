<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi;

/**
 * Logging Manager
 *
 * This class centralizes logging functionality for the SITE/ERP integration
 * system. It handles structured logging of operation results, error reporting,
 * and audit trail generation using the MadHat DbLogger system.
 *
 * Key Responsibilities:
 * - Generate structured operation reports
 * - Log export/sync/payment results to database
 * - Format error messages and success summaries
 * - Handle store context for logging operations
 * - Manage log message serialization
 * - Provide consistent logging format across operations
 *
 * Report Generation:
 * Creates comprehensive reports for batch operations including:
 * - Total number of orders processed
 * - Number of successful operations
 * - Number of failed operations
 * - Detailed error messages with order IDs
 * - Operation timestamps and context
 *
 * Database Logging:
 * - Uses MadHat DbLogger for persistent logging
 * - Stores logs with appropriate log levels (NOTICE, ERROR, etc.)
 * - Associates logs with specific log identifiers (ORDER_EXPORT, etc.)
 * - Handles store emulation for proper context
 *
 * Store Context Management:
 * - Emulates default store view for logging operations
 * - Ensures proper store context for multi-store environments
 * - Handles store-specific configuration during logging
 * - Manages emulation start/stop lifecycle
 *
 * Message Formatting:
 * - Serializes complex data structures (error arrays)
 * - Creates human-readable summary messages
 * - Formats operation statistics consistently
 * - Handles special characters and encoding
 *
 * Integration Points:
 * - Used by OrderExporter for export operation logging
 * - Used by OrderSynchronizer for sync operation logging
 * - Used by PaymentProcessor for payment operation logging
 * - Provides consistent logging interface across all operations
 *
 * Log Levels:
 * - NOTICE: Successful operations and summaries
 * - ERROR: Failed operations and exceptions
 * - DEBUG: Detailed operation information (when enabled)
 *
 * @see MadHat\DbLogger\Logger\DbLoggerSaver For database logging
 * @see MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider For log types
 */

use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Store\Model\App\Emulation;
use Magento\Store\Model\StoreManagerInterface;

class LoggingManager
{
    /**
     * @var DbLoggerSaver
     */
    protected DbLoggerSaver $dbLoggerSaver;

    /**
     * @var Json
     */
    protected Json $json;

    /**
     * @var StoreManagerInterface
     */
    protected StoreManagerInterface $storeManager;

    /**
     * @var Emulation
     */
    protected Emulation $emulation;

    public function __construct(
        DbLoggerSaver $dbLoggerSaver,
        Json $json,
        StoreManagerInterface $storeManager,
        Emulation $emulation
    ) {
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->json = $json;
        $this->storeManager = $storeManager;
        $this->emulation = $emulation;
    }

    /**
     * Log Report to DbLogger
     *
     * @param array $result
     * @param string $title
     * @return void
     */
    public function logReportToDbLogger(array $result, string $title): void
    {
        $message = "Total Order's : " . $result['total_orders'];
        $message .= " | Success Order's : " . $result['success_orders'];
        $message .= " | Failed Order's : " . $result['failed_orders'];

        if (!empty($result['error_messages'])) {
            $message .= " | Errors : " . $this->json->serialize($result['error_messages']);
        }

        $defaultStoreId = $this->storeManager->getDefaultStoreView()->getId();

        if (!$defaultStoreId) {
            $defaultStoreId = 1;
        }

        $this->emulation->startEnvironmentEmulation($defaultStoreId);
        $this->dbLoggerSaver->addRecord(
            $title,
            $message,
            'NOTICE',
            LogIdentifierProvider::ORDER_EXPORT
        );
        $this->emulation->stopEnvironmentEmulation();
    }
}

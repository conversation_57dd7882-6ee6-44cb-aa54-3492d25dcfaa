<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi;

/**
 * Utility Helper
 *
 * This class provides common utility methods and data transformation functions
 * used across the SITE/ERP integration classes. It handles data formatting,
 * validation, and business logic that is shared between multiple components.
 *
 * Key Responsibilities:
 * - Language and locale formatting for SITE API
 * - Address and contact information processing
 * - Payment card type mapping and detection
 * - Shipping method and delivery group logic
 * - Order comment extraction and formatting
 * - Portal and notification type preparation
 * - VAT and organization number handling
 * - Stock-based delivery group calculations
 *
 * Language Processing:
 * - Converts Magento locale codes to SITE language format
 * - Extracts language portion from locale (e.g., 'en_US' -> 'en')
 * - Provides consistent language formatting across API calls
 *
 * Address Handling:
 * - Splits multi-line addresses for SITE requirements
 * - Formats shipping and billing addresses consistently
 * - Handles company name processing for B2B orders
 * - Manages contact person information
 *
 * Payment Processing:
 * - Maps Magento payment card types to SITE card codes
 * - Handles various card brands (Visa, MasterCard, Amex, etc.)
 * - Provides fallback for unknown card types
 * - Supports PayPal and other payment methods
 *
 * Delivery Logic:
 * - Calculates delivery groups based on stock availability
 * - Handles special shipping methods (DHL, UPS)
 * - Manages free product delivery grouping
 * - Processes shipping description formatting
 *
 * Business Logic:
 * - European country identification for VAT handling
 * - Organization number processing for Nordic countries
 * - EU tax number validation and formatting
 * - Purchase reference extraction from quotes
 *
 * Order Comments:
 * - Extracts customer comments from order history
 * - Handles Hyva checkout comment integration
 * - Formats comments for SITE API requirements
 * - Manages comment length limitations
 *
 * @see SiteOrderHelper For configuration and mapping data
 * @see CustomerDataBuilder For customer-specific utilities
 * @see OrderDataBuilder For order-specific utilities
 */

use Magento\Quote\Api\CartRepositoryInterface;
use Exception;
use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\SiteIntegrationOrder\Helper\Data as SiteOrderHelper;
use MadHat\SiteIntegrationOrder\Logger\Logger as SiteOrderLogger;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Model\QuoteFactory;
use Magento\Sales\Api\OrderStatusHistoryRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Item;

class UtilityHelper
{
    /**
     * @var SiteOrderHelper
     */
    protected SiteOrderHelper $siteOrderHelper;

    /**
     * @var SiteOrderLogger
     */
    protected SiteOrderLogger $siteOrderLogger;

    /**
     * @var StockRegistryInterface
     */
    protected StockRegistryInterface $stockRegistry;

    /**
     * @var CustomerRepositoryInterface
     */
    protected CustomerRepositoryInterface $customerRepository;

    /**
     * @var AddressRepositoryInterface
     */
    protected AddressRepositoryInterface $addressRepository;

    /**
     * @var QuoteFactory
     */
    protected QuoteFactory $quoteFactory;

    /**
     * @var OrderStatusHistoryRepositoryInterface
     */
    protected OrderStatusHistoryRepositoryInterface $orderStatusHistoryRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    protected SearchCriteriaBuilder $searchCriteriaBuilder;

    /**
     * @var SortOrderBuilder
     */
    protected SortOrderBuilder $sortOrderBuilder;

    /**
     * @var DbLoggerSaver
     */
    protected DbLoggerSaver $dbLoggerSaver;
    private CartRepositoryInterface $cartRepositoryInterface;

    public function __construct(
        CartRepositoryInterface $cartRepositoryInterface,
        SiteOrderHelper $siteOrderHelper,
        SiteOrderLogger $siteOrderLogger,
        StockRegistryInterface $stockRegistry,
        CustomerRepositoryInterface $customerRepository,
        AddressRepositoryInterface $addressRepository,
        QuoteFactory $quoteFactory,
        OrderStatusHistoryRepositoryInterface $orderStatusHistoryRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        SortOrderBuilder $sortOrderBuilder,
        DbLoggerSaver $dbLoggerSaver
    ) {
        $this->cartRepositoryInterface = $cartRepositoryInterface;
        $this->siteOrderHelper = $siteOrderHelper;
        $this->siteOrderLogger = $siteOrderLogger;
        $this->stockRegistry = $stockRegistry;
        $this->customerRepository = $customerRepository;
        $this->addressRepository = $addressRepository;
        $this->quoteFactory = $quoteFactory;
        $this->orderStatusHistoryRepository = $orderStatusHistoryRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->dbLoggerSaver = $dbLoggerSaver;
    }

    /**
     * Prepare Language
     *
     * @param Order $order
     * @return array
     */
    public function prepareLanguage(Order $order): array
    {
        $language = $this->siteOrderHelper->getLanguageCode($order);
        $languageArr = explode('_', $language);

        return [
            'Value' => $languageArr[0],
        ];
    }

    /**
     * Prepare Notification Type Value for SITE Order Params
     *
     * @return array
     */
    public function prepareNotificationType(): array
    {
        return ['Value' => 'Item0'];
    }

    /**
     * Prepare Portal
     *
     * @param Order $order
     * @return array
     */
    public function preparePortal(Order $order): array
    {
        $portalNumber = $this->siteOrderHelper->getPortalValue(
            $order->getStore()->getWebsiteId()
        );
        return ['Value' => $portalNumber];
    }

    /**
     * Prepare Shipping Name
     *
     * @param Order $order
     * @return string
     */
    public function prepareShippingName(Order $order): string
    {
        $shippingAddress = $order->getShippingAddress();
        $company = $shippingAddress->getCompany();
        if ($company === null) {
            return '';
        }
        return trim($company);
    }

    /**
     * Prepare Shipping Address
     *
     * @param Order $order
     * @param int $index
     * @return string
     */
    public function prepareShippingAddress(Order $order, int $index = 1): string
    {
        list($shippingAddress1, $shippingAddress2) = array_pad(
            $order->getShippingAddress()->getStreet(),
            2,
            ''
        );
        if ($index == 2) {
            return $shippingAddress2;
        }
        return $shippingAddress1;
    }

    /**
     * Get Order Customer Comment from Hyva Checkout Module
     *
     * @param Order $order
     * @return string
     */
    public function getOrderComment(Order $order): string
    {
        try {
            // Create sort order
            $sortOrder = $this->sortOrderBuilder
                ->setField('created_at')
                ->setDirection(\Magento\Framework\Api\SortOrder::SORT_ASC)
                ->create();

            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter('parent_id', $order->getEntityId())
                ->addFilter('is_customer_comment', 1)
                ->setPageSize(1)
                ->setSortOrders([$sortOrder])
                ->create();

            $historyList = $this->orderStatusHistoryRepository->getList($searchCriteria);

            if ($historyList->getTotalCount() > 0) {
                $items = $historyList->getItems();
                $firstItem = reset($items);
                $comment = $firstItem->getComment();
                return $comment ? substr((string)$comment, 0, 40) : '';
            }

            return '';
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'getOrderComment Exception',
                $e->getMessage(),
                'NOTICE',
                LogIdentifierProvider::ORDER_EXPORT
            );
            return '';
        }
    }

    /**
     * Prepare Order Type
     *
     * @param Order $order
     * @return int
     */
    public function prepareOrdertype(Order $order): int
    {
        // TODO : Need to implement "Refund or Replacement" order.
        return 0;
    }

    /**
     * For B2B orders/customers in Europe: specify ItemElementName attribute and set ItemChoiceType enum value
     * For SE and NO as 'organizationno'
     * For the rest of European countries as 'eutaxno' .
     *
     * @param Order $order
     * @return string
     */
    public function prepareItemElementName(Order $order): string
    {
        $countryId = $order->getBillingAddress()->getCountryId();
        if (in_array($countryId, ['SE', 'NO'])) {
            return 'organizationno';
        } elseif (in_array($countryId, $this->siteOrderHelper->getEuropeanCountries())) {
            return 'eutaxno';
        }
        return '';
    }

    /**
     * Prepare Item
     *
     * @param Order $order
     * @return string
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function prepareItem(Order $order): string
    {
        if (in_array($order->getBillingAddress()->getCountryId(), ['SE', 'NO'])) {
            return $this->getOrganisationNumber($order);
        } elseif (in_array($order->getBillingAddress()->getCountryId(), $this->siteOrderHelper->getEuropeanCountries())) {
            return $this->getVat($order);
        } else {
            return '';
        }
    }

    /**
     * Get Organisation Number
     *
     * @param Order $order
     * @return string
     */
    public function getOrganisationNumber(Order $order): string
    {
        return '';
    }

    /**
     * Get VAT
     *
     * @param Order $order
     * @return string
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function getVat(Order $order): string
    {
        $customer = $this->customerRepository->getById($order->getCustomerId());
        $address = $order->getBillingAddress();

        if ($customer && $addressId = $customer->getDefaultBilling()) {
            $address = $this->addressRepository->getById($addressId);
        }

        $vat = preg_replace('/^[^0-9]*/', '', $customer->getTaxvat());
        return str_replace('GR', 'EL', $address->getCountryId()) . $vat;
    }

    /**
     * Prepare Delivery Notify Phone
     *
     * @param Order $order
     * @return string
     */
    public function prepareDeliveryNotifyPhone(Order $order): string
    {
        try {
            $shippingAddress = $order->getShippingAddress();
            $telephone = $shippingAddress->getTelephone();
            return trim($telephone);
        } catch (Exception $e) {
            $this->siteOrderLogger->error(
                __(
                    "Order ID : %1, %2 => Exception : %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $e->getMessage()
                )
            );
            return '';
        }
    }

    /**
     * Prepare purchase_reference value for Order data
     *
     * @param Order $order
     * @return string
     * @throws NoSuchEntityException
     */
    public function preparePurchaseReference(Order $order): string
    {
        $quoteId = (int)$order->getQuoteId();
        $quote = $this->cartRepositoryInterface->get($quoteId);
        $purchaseReference = $quote->getPurchaseReference();

        return $purchaseReference !== null
            ? substr($purchaseReference, 0, 40)
            : '';
    }

    /**
     * Prepare Delivery Group
     *
     * @param Item $orderItem
     * @param Order $order
     * @return int|string
     * @throws NoSuchEntityException
     */
    public function prepareDeliveryGroup(Item $orderItem, Order $order): int|string
    {
        // Pass shipping options to the function.
        // If it is DHL och UPS → always set to 1 as delivery group for all order entries
        $shippingName = $this->getShippingDescription($order);
        if (preg_match('/\b(DHL|UPS)\b/i', $shippingName)) {
            return 1;
        }
        //if price = 0 → delivery group 1 (for free gifts etc)
        if ($orderItem->getPrice() == 0) {
            return 1;
        }
        // Others are processed as usual
        $stockItem = $this->stockRegistry->getStockItemBySku($orderItem->getSku());
        $qty = $stockItem->getQty();
        if ($qty > 0) {
            return '1';
        }
        return '2';
    }

    /**
     * Get Shipping Description
     *
     * @param Order $order
     * @return string|null
     */
    public function getShippingDescription(Order $order): ?string
    {
        if (str_contains($order->getShippingDescription(), '-')) {
            $descArr = explode('-', $order->getShippingDescription());
            array_shift($descArr);
            return trim(implode('-', $descArr));
        }
        return $order->getShippingDescription();
    }

    /**
     * Prepare Payment Card Type
     *
     * @param Order $order
     * @return array
     */
    public function preparePaymentCardType(Order $order): array
    {
        $additionalInformation = $order->getPayment()->getAdditionalInformation();
        $cardType = $additionalInformation['payment_method'] ?? 'unspecified';
        switch ($cardType) {
            case 'visa':
                $cardType = 'Item1';
                break;
            case 'mc':
                $cardType = 'Item2';
                break;
            case 'amex':
                $cardType = 'Item3';
                break;
            case 'diners':
                $cardType = 'Item4';
                break;
            case 'eurocard':
                $cardType = 'Item5';
                break;
            case 'dankort':
                $cardType = 'Item6';
                break;
            case 'switch':
                $cardType = 'Item7';
                break;
            case 'solo':
                $cardType = 'Item8';
                break;
            case 'delta':
                $cardType = 'Item9';
                break;
            case 'fsbkort':
                $cardType = 'Item10';
                break;
            case 'discover':
                $cardType = 'Item11';
                break;
            case 'cartebleue':
                $cardType = 'Item12';
                break;
            case 'cartesbancaires':
                $cardType = 'Item13';
                break;
            case 'paypal':
                $cardType = 'Item71';
                break;
            default:
                $cardType = 'Item99';
                break;
        }
        return ['Value' => $cardType];
    }
}

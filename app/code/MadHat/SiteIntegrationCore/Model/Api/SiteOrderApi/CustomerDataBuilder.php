<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi;

/**
 * Customer Data Builder
 *
 * This class handles the preparation of customer data structures for SITE/ERP
 * order export. It manages customer information, addresses, VAT handling,
 * and business logic for B2B vs B2C customer classification.
 *
 * Key Responsibilities:
 * - Generate or retrieve unique customer numbers for SITE
 * - Determine customer type (B2B vs B2C) based on business rules
 * - Prepare billing address and contact information
 * - Handle VAT exemption and tax account mapping by country
 * - Calculate payment terms based on customer type and tax information
 * - Manage credit limits and customer status
 * - Process organization numbers and EU tax numbers
 *
 * Customer Number Management:
 * - Creates unique customer numbers for SITE integration
 * - Uses email-based lookup to maintain consistency
 * - Stores customer mapping in madhat_customer_info table
 *
 * Customer Type Logic:
 * - B2C: Individual consumers (Item10 in SITE)
 * - B2B: Business customers (Item49 in SITE)
 * - Determined by helper configuration and order context
 *
 * VAT and Tax Handling:
 * - Maps countries to VAT exemption status
 * - Handles EU tax number validation
 * - Manages organization numbers for Nordic countries (SE, NO)
 * - Sets appropriate customer VAT accounts
 *
 * Payment Terms Calculation:
 * - B2C customers: 14 days standard
 * - B2B customers: 30 days if valid org/VAT number, otherwise 14 days
 * - Based on organization number (SE/NO) or EU tax number
 *
 * Address Processing:
 * - Formats billing addresses for SITE requirements
 * - Handles multi-line address splitting
 * - Manages contact person information
 * - Processes phone numbers and email addresses
 *
 * @see UtilityHelper For common utility functions
 */

use Exception;
use MadHat\OrderIntegration\Api\MadhatCustomerInfoRepositoryInterface;
use MadHat\OrderIntegration\Model\MadhatCustomerInfoFactory;
use MadHat\SiteIntegrationOrder\Helper\Data as SiteOrderHelper;
use MadHat\SiteIntegrationOrder\Logger\Logger as SiteOrderLogger;
use MadHat\SiteIntegrationOrder\Model\Config\Source\CustomerType;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Model\Order;

class CustomerDataBuilder
{
    /**
     * @var MadhatCustomerInfoRepositoryInterface
     */
    protected MadhatCustomerInfoRepositoryInterface $madhatCustomerInfoRepository;

    /**
     * @var MadhatCustomerInfoFactory
     */
    protected MadhatCustomerInfoFactory $madhatCustomerInfoFactory;

    /**
     * @var SiteOrderHelper
     */
    protected SiteOrderHelper $siteOrderHelper;

    /**
     * @var SiteOrderLogger
     */
    protected SiteOrderLogger $siteOrderLogger;

    /**
     * @var CustomerRepositoryInterface
     */
    protected CustomerRepositoryInterface $customerRepository;

    /**
     * @var AddressRepositoryInterface
     */
    protected AddressRepositoryInterface $addressRepository;

    /**
     * @var UtilityHelper
     */
    protected UtilityHelper $utilityHelper;

    public function __construct(
        MadhatCustomerInfoRepositoryInterface $madhatCustomerInfoRepository,
        MadhatCustomerInfoFactory $madhatCustomerInfoFactory,
        SiteOrderHelper $siteOrderHelper,
        SiteOrderLogger $siteOrderLogger,
        CustomerRepositoryInterface $customerRepository,
        AddressRepositoryInterface $addressRepository,
        UtilityHelper $utilityHelper
    ) {
        $this->madhatCustomerInfoRepository = $madhatCustomerInfoRepository;
        $this->madhatCustomerInfoFactory = $madhatCustomerInfoFactory;
        $this->siteOrderHelper = $siteOrderHelper;
        $this->siteOrderLogger = $siteOrderLogger;
        $this->customerRepository = $customerRepository;
        $this->addressRepository = $addressRepository;
        $this->utilityHelper = $utilityHelper;
    }

    /**
     * Prepare Customer Data
     *
     * @param array $siteOrderParams
     * @param Order $order
     * @param array $vatMappingArray
     * @return array
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function prepareCustomerData(array $siteOrderParams, Order $order, array $vatMappingArray): array
    {
        $siteOrderParams['customer'] = [
            'customerno' => $this->prepareCustomerNumber($order),
            'customertype' => $this->prepareCustomerType($order),
            'customername' => $this->prepareCustomerName($order),
            'billingaddress1' => $this->prepareBillingAddress($order),
            'billingaddress2' => $this->prepareBillingAddress($order, 2),
            'billingcontactperson' => $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname(),
            'billingzipcode' => $order->getBillingAddress()->getPostcode(),
            'billingcity' => $order->getBillingAddress()->getCity(),
            'billingcounty' => $order->getBillingAddress()->getRegion(),
            'exvat' => $this->prepareCustomerExVat($order, $vatMappingArray),
            'customercountry' => ['Value' => $order->getBillingAddress()->getCountryId()],
            'customerlanguage' => $this->utilityHelper->prepareLanguage($order),
            'phoneno' => $this->prepareTelephoneNo($order),
            'email' => $order->getCustomerEmail(),
            'customerportal' => $this->utilityHelper->preparePortal($order),
            'customerstatus' => ['Value' => 'Item4'],
            'creditlimit' => $this->prepareCreditlimit($order),
            'mobilephoneno' => $this->prepareTelephoneNo($order),
            'customervatno' => $this->prepareCustomerExVat($order, $vatMappingArray),
            'customervataccount' => $this->prepareCustomerVatAccount($order, $vatMappingArray),
            'customercurrency' => ['Value' => strtolower($order->getBaseCurrencyCode())],
            'paymentterm' => $this->preparePaymentTerm($order),
            'invoiceemail' => $this->prepareCustomerInvoiceMail($order),
        ];
        return $siteOrderParams;
    }

    /**
     * Prepare Customer Number
     *
     * @param Order $order
     * @return int
     * @throws LocalizedException
     */
    public function prepareCustomerNumber(Order $order): int
    {
        $this->siteOrderLogger->info(
            __(
                "Magento Order ID : [%1], Order Data : %2",
                $order->getId(),
                json_encode($order->getData())
            )
        );

        $customerEmail = $order->getCustomerEmail();
        try {
            $madhatCustomerInfo = $this->madhatCustomerInfoRepository->getByEmail($customerEmail);
        } catch (Exception $e) {
            $madhatCustomerInfo = $this->madhatCustomerInfoFactory->create();
            $madhatCustomerInfo->setCustomerEmail($customerEmail);

            $madhatCustomerInfo = $this->madhatCustomerInfoRepository->save($madhatCustomerInfo);
        }
        return $madhatCustomerInfo->getEntityId();
    }

    /**
     * Prepare Customer Type
     *
     * @param Order $order
     * @return array
     */
    protected function prepareCustomerType(Order $order): array
    {
        if ($this->siteOrderHelper->getCustomerType($order) == CustomerType::B2B) {
            return ['Value' => 'Item49'];
        }

        return ['Value' => 'Item10'];
    }

    /**
     * Prepare Customer Name
     *
     * @param Order $order
     * @return string
     * @throws NoSuchEntityException
     */
    protected function prepareCustomerName(Order $order): string
    {
        if ($order->getCustomerId()) {
            $customer = $this->customerRepository->getById($order->getCustomerId());
            if ($customer) {
                return $customer->getFirstname() . ' ' . $customer->getLastname();
            }
        }

        return $order->getCustomerFirstname() . ' ' . $order->getCustomerLastname();
    }

    /**
     * Prepare Billing Address
     *
     * @param Order $order
     * @param int $index
     * @return string
     */
    protected function prepareBillingAddress(Order $order, int $index = 1): string
    {
        list($billingAddress1, $billingAddress2) = array_pad(
            $order->getBillingAddress()->getStreet(),
            2,
            ''
        );
        if ($index == 2) {
            return $billingAddress2;
        }
        return $billingAddress1;
    }

    /**
     * Prepare exvat value for Order customerdata
     *
     * @param Order $order
     * @param array $vatMappingArray
     * @return array
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function prepareCustomerExVat(Order $order, array $vatMappingArray): array
    {
        try {
            $countryId = $order->getBillingAddress()->getCountryId();
            if (isset($vatMappingArray[$countryId])) {
                return ['Value' => $vatMappingArray[$countryId]['exVat']];
            }

            $this->siteOrderLogger->warning(
                __(
                    "Order Id: %1, Function: %2 => Invalid country code: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $countryId
                )
            );

            throw new LocalizedException(
                __("Invalid country code (%1) to get Customer Ex VAT.", $countryId)
            );
        } catch (Exception $e) {
            $this->siteOrderLogger->error(
                __(
                    "Order Id: %1, Function: %2 => Exception: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $e->getMessage()
                )
            );
            throw $e;
        }
    }

    /**
     * Prepare Telephone Number
     *
     * @param Order $order
     * @return string
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    protected function prepareTelephoneNo(Order $order): string
    {
        $address = $order->getBillingAddress();

        if ($order->getCustomerId()) {
            $customer = $this->customerRepository->getById($order->getCustomerId());
            if ($customer && $addressId = $customer->getDefaultBilling()) {
                $address = $this->addressRepository->getById($addressId);
            }
        }

        return (string)$address->getTelephone();
    }

    /**
     * Prepare Credit Limit
     *
     * @param Order $order
     * @return float
     * @throws NoSuchEntityException|LocalizedException
     */
    protected function prepareCreditlimit(Order $order): float
    {
        if ($order->getCustomerId()) {
            $customer = $this->customerRepository->getById($order->getCustomerId());

            //TODO : Use Amasty Credit for Invoice Credit Limit
            if ($customer->getCustomAttribute('fortnox_invoice_limit')) {
                if (!empty($customer->getCustomAttribute('fortnox_invoice_limit')->getValue())) {
                    return (float)$customer->getCustomAttribute('fortnox_invoice_limit')->getValue();
                }
            }
        }

        return 0;
    }

    /**
     * Prepare customervataccount value for order customerdata
     *
     * @param Order $order
     * @param array $vatMappingArray
     * @return array
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    protected function prepareCustomerVatAccount(Order $order, array $vatMappingArray): array
    {
        try {
            $countryId = $order->getBillingAddress()->getCountryId();
            if (isset($vatMappingArray[$countryId])) {
                return ['Value' => $vatMappingArray[$countryId]['customerVatAccount']];
            }

            $this->siteOrderLogger->warning(
                __(
                    "Order Id: %1, Function: %2 => Invalid country code: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $countryId
                )
            );

            throw new LocalizedException(
                __("Invalid country code (%1) to get Customer VAT account.", $countryId)
            );
        } catch (Exception $e) {
            $this->siteOrderLogger->error(
                __(
                    "Order Id: %1, Function: %2 => Exception: %3",
                    $order->getIncrementId(),
                    __FUNCTION__,
                    $e->getMessage()
                )
            );
            throw $e;
        }
    }

    /**
     * Prepare Payment Term for Site Order Export Customer Params
     *
     * @param Order $order
     * @return array
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    protected function preparePaymentTerm(Order $order): array
    {
        $customerType = $this->siteOrderHelper->getCustomerType($order);
        if ($customerType == CustomerType::B2C) {
            return ['Value' => 14];
        } elseif ($customerType == CustomerType::B2B) {
            $itemElementName = $this->utilityHelper->prepareItemElementName($order);
            if ($itemElementName == 'organizationno') {
                if (!empty($this->utilityHelper->getOrganisationNumber($order))) {
                    return ['Value' => 30];
                } else {
                    return ['Value' => 14];
                }
            } elseif ($itemElementName == 'eutaxno') {
                if (!empty($this->utilityHelper->getVat($order))) {
                    return ['Value' => 30];
                } else {
                    return ['Value' => 14];
                }
            } else {
                return ['Value' => 14];
            }
        }
        return ['Value' => 0];
    }

    /**
     * Prepares the customer invoice email data based on the provided order.
     *
     * @param Order $order The order object used to retrieve customer information.
     *
     * @return string
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    private function prepareCustomerInvoiceMail(Order $order): string
    {
        $customerEmail = $order->getCustomerEmail();
        $customer = $this->customerRepository->get($customerEmail);

        $invoiceEmail = '';
        if ($customer->getCustomAttribute('invoice_email')) {
            $invoiceEmail = $customer->getCustomAttribute('invoice_email')->getValue();
        }

        return $invoiceEmail;
    }
}

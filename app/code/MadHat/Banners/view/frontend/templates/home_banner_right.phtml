<?php
/**
 * @codingStandardsIgnoreFile
 * $block \MadHat\Banners\Block\Banners
*/
use MadHat\Banners\Model\Source\BannerPosition;

$bannerId       = [BannerPosition::SECOUNDRY_BANNER_RIGHT];
$banners        = $block->getFrontBanners($bannerId);
$config         = $this->helper('MadHat\Banners\Helper\Data');
$lazyLoadConf   = $config->getBannerConfig('lazyload');
$desktopHeight  = $config->getBannerConfig('sdheight');
$desktopWidth   = $config->getBannerConfig('sdwidth');
$mobileHeight   = $config->getBannerConfig('smheight');
$mobileWidth    = $config->getBannerConfig('smwidth');
?>

<?php if (count($banners) > 0): ?>
    <div class="home-banner-slider-right">
        <?php foreach($banners as $banner):?>
            <div class="banner-right-box">
            <?php if($banner->getLink()): ?>
                <a href="<?=$banner->getLink()?>">
            <?php endif;?>
            <?php if(!empty($banner->getMobileBannerImage())): ?>
                <picture>
                    <source srcset="<?=$block->getMediaDirectoryUrl().$banner->getMobileBannerImage() ?>"
                            alt="<?=$banner->getTitle()?>" media="(max-width: 640px)" >
                    <img height="<?=$mobileHeight?>" width="<?=$mobileWidth?>" src="<?=$block->getMediaDirectoryUrl().$banner->getBannerimage() ?>" alt="<?=$banner->getTitle()?>" />
                </picture>
                <?php else:?>
                    <img height="<?=$desktopHeight?>" width="<?=$desktopWidth?>" class="pagebuilder-mobile-hidden" src="<?=$block->getMediaDirectoryUrl().$banner->getBannerimage() ?>"
                        alt="<?=$banner->getTitle()?>" title="<?=$banner->getTitle()?>" data-element="desktop_image" />
                <?php endif;?>
            <?php if($banner->getLink()): ?>
                </a>
            <?php endif;?>
            </div>
        <?php endforeach; ?>
    </div>
    <?php else:
        echo $this->getLayout()->createBlock('Magento\Cms\Block\Block')->setBlockId('right_secondary_banner')->toHtml();
    endif;?>

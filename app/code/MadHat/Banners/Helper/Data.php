<?php
declare(strict_types=1);
/**
 * MadHat_Banners extension
 * NOTICE OF LICENSE
 *
 * This source file is subject to the MadHat License.
 *
 * @category  MadHat
 * @package   MadHat_Banners
 * @copyright Copyright (c) 2023
 **/

namespace MadHat\Banners\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{
    /**
     * @var ENABLED_CONFIG MODULE ENABLE
     */
    public const ENABLED_CONFIG = 'madhat_banner/banner/enable';

    const CONFIG_PATH = 'madhat_banner/banner/';

    /**
     * Innitialize constructor
     *
     * @param Context $context
     */
    public function __construct(Context $context)
    {
        parent::__construct($context);
    }

    /**
     * Returns whether or not the module is enabled.
     * @param string|int $scope
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(self::ENABLED_CONFIG, ScopeInterface::SCOPE_STORE);
    }

    /**
     * @param string|int $scope
     *
     * @return string||bool||int
     */
    public function getBannerConfig($fieldName)
    {
        return $this->scopeConfig->getValue(self::CONFIG_PATH.$fieldName, ScopeInterface::SCOPE_STORE);
    }

}

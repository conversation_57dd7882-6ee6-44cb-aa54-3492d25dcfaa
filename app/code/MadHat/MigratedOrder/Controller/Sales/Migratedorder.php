<?php

namespace MadHat\MigratedOrder\Controller\Sales;

use MadHat\MigratedOrder\Model\MigratedOrderDetailFactory;
use MadHat\MigratedOrder\Model\ResourceModel\MigratedOrderDetail\CollectionFactory as MigratedOrderDetailCollectionFactory;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\Registry;

class Migratedorder extends \Magento\Framework\App\Action\Action implements HttpGetActionInterface
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $_pageFactory;

    /**
     * @var MigratedOrderDetailCollectionFactory
     */
    protected $migratedOrderDetailFactory;

    /**
     * @var Registry
     */
    protected $registry;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $pageFactory,
        Session $customerSession,
        MigratedOrderDetailCollectionFactory $migratedOrderDetailCollectionFactory,
        Registry $registry
    ) {
        $this->_pageFactory = $pageFactory;
        $this->customerSession = $customerSession;
        $this->migratedOrderDetailFactory = $migratedOrderDetailCollectionFactory;
        $this->registry = $registry;
        return parent::__construct($context);
    }
    /**
     * View page action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        if (!$this->customerSession->isLoggedIn()) {
            return $this->getResponse()->setRedirect($this->_redirect->getRefererUrl());
        }

        $orderId = $this->getRequest()->getParam('order_id');

        $migratedOrderDetail = $this->getMigratedOrderDetail($orderId);
        $this->registry->register('migrated_order_detail', $migratedOrderDetail);

        return $this->_pageFactory->create();
    }

    protected function getMigratedOrderDetail($orderId)
    {
        $migratedOrderDetailCol = $this->migratedOrderDetailFactory->create()->addFieldToFilter(
            'migrated_order_id',
            $orderId
        )->load();

        return $migratedOrderDetailCol;
    }
}


<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="madhat_migrated_order" resource="default" comment="Old Migrated Order">
        <column xsi:type="bigint" name="migrated_order_id" nullable="false" identity="false" comment="Migrated Order Id"/>
        <column xsi:type="bigint" name="migrated_customer_id" nullable="false" comment="migrated Customer Id"/>
        <column xsi:type="varchar" name="ship_to" nullable="false" comment="Ship To"/>
        <column xsi:type="varchar" name="order_total" nullable="false" comment="Order Total"/>
        <column xsi:type="varchar" name="status" nullable="false" comment="Status"/>
        <column xsi:type="varchar" name="date" nullable="false" comment="Log Title"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="migrated_order_id"/>
        </constraint>

    </table>

    <table name="madhat_migrated_order_detail" resource="default" comment="Old Migrated Order Full detail">
        <column xsi:type="int" name="entity_id" identity="true" comment="Id"/>
        <column xsi:type="bigint" name="migrated_order_id" identity="false" comment="Id"/>
        <column xsi:type="bigint" name="migrated_customer_id" nullable="false" comment="Migrated Customer Id"/>
        <column xsi:type="longtext" name="content" nullable="false" comment="Content in Json"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>

    </table>
</schema>

<?php

/** @var \MadHat\MigratedOrder\Block\Order\View $block */
/** @var \Magento\Framework\Escaper $escaper */
$migratedOrderDetails = $block->getMigratedOrderDetail();
$migratedOrder = $migratedOrderDetails->getFirstItem()->getContent();
?>

<div class="md:flex md:justify-between md:flex-wrap md:gap-4 items-center mb-3">
    <div class="lg:flex items-center text-center md:text-left">
        <div class="lg:inline-block">
            <div class="text-2xl">Order # <?= $migratedOrder['Order Number'] ?? ''; ?></div>
            <div class="order-date">
                <span class="label">Order Date:</span> <span><?= $migratedOrder['Created At'] ?? ''; ?></span>
            </div>
        </div>
        <div class="mt-3 md:mt-0 lg:inline-block lg:ml-5">
            <span class="order-status inline-block px-3 py-3 border rounded border-container">
                <span class="sr-only">
                    Status:&nbsp;
                </span><?= $migratedOrder['Fulfillment Status'] ?? ''; ?></span>
        </div>
    </div>
</div>

<div class="order-details-items ordered bg-container-lighter">
    <div class="-mx-4">
        <ul class="items order-links">
            <li class="nav item current"><strong><?= __('order') ?></strong></li>
        </ul>
    </div>
    <div class="p-4 card">
        <div class="mb-4">
            <div class="mb-4 pb-4 border-b border-container">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
                    <div class="w-full py-4">
                        <p class="font-semibold mb-2"><?= __('Billing Address') ?></p>
                        <div class="box-content">
                            <address><?= $migratedOrder['Billing Name'] ?? ''; ?><br>
                                <?= $migratedOrder['Billing Address1'] ?? ''; ?><br>
                                <?= $migratedOrder['Billing Address2'] ?? ''; ?><br>
                                <?= $migratedOrder['Billing City'] ?? ''; ?><br>
                                <?= $migratedOrder['Billing Country'] ?? ''; ?><br>
                                <?= $migratedOrder['Billing Company'] ?? ''; ?><br>
                                T: <a href="tel:<?= $migratedOrder['Billing Phone'] ?? ''; ?>"><?= $migratedOrder['Billing Phone'] ?? ''; ?></a>

                            </address>
                        </div>
                    </div>
                    <div class="w-full py-4">
                        <p class="font-semibold mb-2"><?= __('Shipping Address') ?></p>
                        <div class="box-content">
                        <address><?= $migratedOrder['Shipping Name'] ?? ''; ?><br>
                                <?= $migratedOrder['Shipping Address1'] ?? ''; ?><br>
                                <?= $migratedOrder['Shipping Address2'] ?? ''; ?><br>
                                <?= $migratedOrder['Shipping City'] ?? ''; ?><br>
                                <?= $migratedOrder['Shipping Country'] ?? ''; ?><br>
                                <?= $migratedOrder['Shipping Company'] ?? ''; ?><br>
                                T: <a href="tel:<?= $migratedOrder['Shipping Phone'] ?? ''; ?>"><?= $migratedOrder['Billing Phone'] ?? ''; ?></a>

                            </address>
                        </div>
                    </div>
                    <div class="w-full py-4">
                        <p class="font-semibold mb-2"><?= __('Shipping Method') ?></p>
                        <div class="box-content">
                            <?= $migratedOrder['Shipping Method'] ?? ''; ?>
                        </div>
                    </div>

                    <?php if(isset($migratedOrder['Payment Method'])): ?>
                        <div class="w-full py-4">
                            <p class="font-semibold mb-2"><?= __('Payment Method') ?></p>
                            <div class="box-content">
                                <?= $migratedOrder['Payment Method']; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="order-title">
            <p class="text-lg mt-8 mb-2"><?= __('Items Ordered') ?></p>
            <?php
                $trackingCompanies = isset($migratedOrder['Tracking Companies']) ? explode(',' , $migratedOrder['Tracking Companies']) : '';
                $trackingNumbers = isset($migratedOrder['Tracking Numbers']) ? explode(',' , $migratedOrder['Tracking Numbers']) : '';
                $trackingUrl = isset($migratedOrder['Tracking URLs']) ? explode(',' , $migratedOrder['Tracking URLs']) : '';
            ?>
                <?php foreach ($trackingNumbers as $key => $trackingNumber) : ?>
                    <?php $url = $trackingUrl[$key] ?? ''; ?>
                    <?php $label = $trackingNumber ?? ''; ?>
                    <span class="text-lg mt-8 mb-2"><?= __('Tracking URL :') ?></span>
                    <a href="<?= $escaper->escapeUrl($url) ?>" class="text-primary underline"
                        target="_blank" rel="noopener" title="<?= $escaper->escapeHtmlAttr($label) ?>">
                        <?= $escaper->escapeHtml($label) ?>
                    </a>
                <?php endforeach; ?>
        </div>

        <div class="order-items">
            <div class="hidden lg:grid grid-cols-5 text-sm text-secondary mt-2">
                <div class="p-2 col-span-2"><?= __('Product Name') ?></div>
                <div class="p-2"><?= __('Price') ?></div>
                <div class="p-2"><?= __('Qty') ?></div>
                <div class="p-2 text-right"><?= __('Subtotal') ?></div>
            </div>
            <?php foreach ($migratedOrderDetails as $migratedOrderItem) : ?>
                <?php $item = $migratedOrderItem->getContent(); ?>
                <div class="parent-item mb-2">
                    <div class="lg:grid grid-cols-5 py-2">
                        <div class="p-2 col-span-2">
                            <span class="font-semibold"><?= $item['Lineitem Name'] ?? ''; ?></span>
                            <div class="item-options mt-2">
                                <div class="text-sm flex">
                                    <span class="text-sm ml-1"><?= $item['Lineitem SKU'] ?? ''; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="p-2 flex justify-between lg:block">
                            <p class="lg:sr-only font-medium"><?= __('Price') ?></p>

                            <span class="price-excluding-tax" data-label="Excl. Tax">
                                <span class="cart-price">
                                    <span class="price"><?= $item['Lineitem Price'] ?? '0'; ?></span> </span>
                            </span>
                        </div>
                        <div class="p-2 flex justify-between lg:block">
                            <p class="lg:sr-only font-medium">
                                Qty </p>
                            <div class="text-right lg:text-left">
                                <p>
                                    <span class="title"><?= __('Ordered') ?>:</span>
                                    <span class="content"><?= $item['Lineitem Quantity'] ?? '0'; ?></span>
                                </p>
                            </div>
                        </div>
                        <div class="p-2 text-right flex justify-between lg:block">
                            <p class="lg:sr-only font-medium"><?= __('Subtotal') ?></p>

                            <span class="price-excluding-tax" data-label="Excl. Tax">
                                <span class="cart-price">
                                    <span class="price">
                                        <?php
                                            $lineItemPrice = $item['Lineitem Price'] ?? 0;
                                            $lineItemQty = $item['Lineitem Quantity'] ?? 0;
                                            $rowTotal = (float) ($lineItemPrice * $lineItemQty);

                                        ?>
                                        <?= round($rowTotal, 2) ?>
                                    </span>
                                </span>
                            </span>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        <div class="flex justify-end border-t border-container mt-2">
            <div class="lg:w-1/3">
                <div class="grid grid-cols-2 text-right p-2 gap-2">
                    <div>
                        <?= __('Subtotal') ?>
                    </div>
                    <div>
                        <span class="price"><?= $migratedOrder['Subtotal'] ?? ''; ?></span>
                    </div>
                    <div>
                        <?= __('Discount Amount') ?>
                    </div>
                    <div>
                        <span class="price"><?= $migratedOrder['Discount Amount'] ?? ''; ?></span>
                    </div>
                    <div>
                        <?= __('Tax') ?>
                    </div>
                    <div>
                        <span class="price"><?= $migratedOrder['Taxes'] ?? ''; ?></span>
                    </div>
                    <div>
                        <strong><?= __('Grand Total') ?></strong>
                    </div>
                    <div>
                        <strong><span class="price"><?= $migratedOrder['Total'] ?? ''; ?></span></strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

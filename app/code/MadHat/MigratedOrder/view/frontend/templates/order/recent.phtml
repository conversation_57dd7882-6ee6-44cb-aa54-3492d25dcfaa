<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\Escaper;
use Magento\Sales\Model\Order;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use MadHat\MigratedOrder\Block\Order\Recent;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis
// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Recent $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var Order[] $orders */
$orders = $block->getOrders();
//die('okoko');
$count  = count($orders);

?>
<div
    class="block block-dashboard-orders card mt-4"
    x-data="{ tabIndex: -1 }"
>
    <div class="block-title order my-4 flex justify-between items-center">
        <h2
            id="migratedOrders"
            class="text-2xl block"
            @focus="tabIndex = 0;"
            @blur="tabIndex = -1"
            :tabindex="tabIndex"
        ><?= $escaper->escapeHtml(__('Migrated Orders')) ?></h2>
    </div>
    <div class="overflow-x-auto">
        <?= $block->getChildHtml() ?>
        <?php if ($count > 0): ?>
            <table
                id="my-orders-table"
                class="block-content w-full sm:table-fixed lg:table-auto"
                aria-labelledby="recentOrders"
            >
                <thead class="text-sm text-secondary">
                    <tr>
                        <th class="p-2 text-start">
                            <?= $escaper->escapeHtml(__('Order #')) ?>
                        </th>
                        <th class="p-2 text-start hidden lg:table-cell">
                            <?= $escaper->escapeHtml(__('Date')) ?>
                        </th>
                        <th class="p-2 text-start">
                            <?= $escaper->escapeHtml(__('Ship To')) ?>
                        </th>
                        <th class="p-2 text-start hidden lg:table-cell">
                            <?= $escaper->escapeHtml(__('Order Total')) ?>
                        </th>
                        <th class="p-2 text-start hidden lg:table-cell">
                            <?= $escaper->escapeHtml(__('Status')) ?>
                        </th>
                        <th class="p-2 text-center">
                            <?= $escaper->escapeHtml(__('View')) ?>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                        <tr class="align-top even:bg-container-darker">
                            <th
                                id="order<?= $escaper->escapeHtmlAttr($order->getMigratedOrderId()) ?>"
                                class="p-2 text-start font-normal"
                            >
                                <?= $escaper->escapeHtml($order->getMigratedOrderId()) ?>
                                <p class="lg:hidden text-sm">
                                    <?= $escaper->escapeHtml($order->getDate()) ?>
                                    <?php //= $escaper->escapeHtml($block->formatDate($order->getDate())) ?>
                                </p>
                            </th>
                            <td class="p-2 hidden lg:table-cell">
                                <?php
                                    $date = $order->getDate();
                                    $format = $block->getDateFormat() ?: \IntlDateFormatter::SHORT;
                                    $formatLong = $block->getDateFormat() ?: \IntlDateFormatter::LONG;
                                ?>
                                <span aria-hidden="true">
                                    <?= $escaper->escapeHtml($order->getDate()) ?>
                                    <?php //= $escaper->escapeHtmlAttr($block->formatDate($date, $format)); ?>
                                </span>
                                <span class="sr-only">
                                    <?= $escaper->escapeHtml($order->getDate()) ?>
                                    <?php //= $escaper->escapeHtml($block->formatDate($date, $formatLong)); ?>
                                </span>
                            </td>
                            <td class="p-2">
                                <?= $order->getShipTo() ? $escaper->escapeHtml($order->getShipTo()) : '&nbsp;' ?>
                            </td>
                            <td class="p-2 hidden lg:table-cell">
                                <?= /* @noEscape */ $order->getOrderTotal() ?>
                            </td>
                            <td class="p-2 hidden lg:table-cell">
                                <?= $escaper->escapeHtml($order->getStatus()) ?>
                            </td>
                            <td class="p-2">
                                <div class="flex items-center justify-evenly">
                                    <a
                                        href="<?= $escaper->escapeUrl($block->getViewUrl($order)) ?>"
                                        class="inline-block text-sm underline text-secondary-darker"
                                        title="<?= $escaper->escapeHtmlAttr(__('View Order')) ?>"
                                        aria-label="<?= $escaper->escapeHtmlAttr(__('View order') . ' ' . ltrim($order->getMigratedOrderId(), '0')) ?>"
                                    >
                                        <?= $heroicons->eyeHtml('', 24, 24, ['aria-hidden' => 'true']) ?>
                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="message info empty">
                <span><?= $escaper->escapeHtml(__('You have placed no orders.')) ?></span>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php

namespace MadHat\SiteIntegrationStockAvailability\Api\Data;

/**
 * Interface StockAvailabilityDataInterface
 */
interface StockAvailabilityDataInterface
{
    /**
     * @return string|null
     */
    public function getProductNo(): ?string;

    /**
     * @param string|null $productNo
     * @return void
     */
    public function setProductNo(?string $productNo): void;

    /**
     * @return string|null
     */
    public function getExternalProductNo(): ?string;

    /**
     * @param string|null $externalProductNo
     * @return void
     */
    public function setExternalProductNo(?string $externalProductNo): void;

    /**
     * @return string|null
     */
    public function getProductStatus(): ?string;

    /**
     * @param string|null $productStatus
     * @return void
     */
    public function setProductStatus(?string $productStatus): void;

    /**
     * @return string|null
     */
    public function getAvailabilityStatus(): ?string;

    /**
     * @param string|null $availabilityStatus
     * @return void
     */
    public function setAvailabilityStatus(?string $availabilityStatus): void;

    /**
     * @return string|null
     */
    public function getWarehouseAvailabilityStatus(): ?string;

    /**
     * @param string|null $warehouseAvailabilityStatus
     * @return void
     */
    public function setWarehouseAvailabilityStatus(?string $warehouseAvailabilityStatus): void;

    /**
     * @return int|null
     */
    public function getUnitsInStock(): ?int;

    /**
     * @param int|null $unitsInStock
     * @return void
     */
    public function setUnitsInStock(?int $unitsInStock): void;

    /**
     * @return int|null
     */
    public function getProductStatusValue(): ?int;

    /**
     * @param int|null $productStatusValue
     * @return void
     */
    public function setProductStatusValue(?int $productStatusValue): void;

    /**
     * @return int|null
     */
    public function getBuyableStatusOnWebsite(): ?int;

    /**
     * @param int|null $buyableStatusOnWebsite
     * @return void
     */
    public function setBuyableStatusOnWebsite(?int $buyableStatusOnWebsite): void;

    /**
     * @return int|null
     */
    public function getBuyableStatusOnWebsiteValue(): ?int;

    /**
     * @param int|null $buyableStatusOnWebsiteValue
     * @return void
     */
    public function setBuyableStatusOnWebsiteValue(?int $buyableStatusOnWebsiteValue): void;

    /**
     * @return string|null
     */
    public function getDummyValue(): ?string;

    /**
     * @param string|null $dummyValue
     * @return void
     */
    public function setDummyValue(?string $dummyValue): void;

}

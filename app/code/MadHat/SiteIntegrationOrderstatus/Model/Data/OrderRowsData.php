<?php

namespace MadHat\SiteIntegrationOrderstatus\Model\Data;

use MadHat\SiteIntegrationOrderstatus\Api\Data\OrderRowsDataInterface;
use Magento\Framework\DataObject;

class OrderRowsData extends DataObject implements OrderRowsDataInterface
{
    public function getProductNo()
    {
        return $this->getData('ProductNo');
    }

    public function setProductNo(?string $productNo)
    {
        $this->setData('ProductNo', $productNo);
    }

    public function getExternalProductNo()
    {
        return $this->getData('ExternalProductNo');
    }

    public function setExternalProductNo(?string $externalProductNo)
    {
        $this->setData('ExternalProductNo', $externalProductNo);
    }

    public function getIsSubscriptionLine()
    {
        return $this->getData('IsSubscriptionLine');
    }

    public function setIsSubscriptionLine(?bool $isSubscriptionLine)
    {
        $this->setData('IsSubscriptionLine', $isSubscriptionLine);
    }

    public function getQuantity()
    {
        return $this->getData('Quantity');
    }

    public function setQuantity(?int $quantity)
    {
        $this->setData('Quantity', $quantity);
    }

    public function getPriceGross()
    {
        return $this->getData('PriceGross');
    }

    public function setPriceGross(?float $priceGross)
    {
        $this->setData('PriceGross', $priceGross);
    }

    public function getPriceNet()
    {
        return $this->getData('PriceNet');
    }

    public function setPriceNet(?float $priceNet)
    {
        $this->setData('PriceNet', $priceNet);
    }

    public function getName()
    {
        return $this->getData('Name');
    }

    public function setName(?string $name)
    {
        $this->setData('Name', $name);
    }

    public function getDiscountPercentage()
    {
        return $this->getData('DiscountPercentage');
    }

    public function setDiscountPercentage(?float $discountPercentage)
    {
        $this->setData('DiscountPercentage', $discountPercentage);
    }

    public function getTaxClass()
    {
        return $this->getData('TaxClass');
    }

    public function setTaxClass(?int $taxClass)
    {
        $this->setData('TaxClass', $taxClass);
    }

    public function getCostPriceNet()
    {
        return $this->getData('CostPriceNet');
    }

    public function setCostPriceNet(?float $costPriceNet)
    {
        $this->setData('CostPriceNet', $costPriceNet);
    }

    public function getPriceNetSek()
    {
        return $this->getData('PriceNetSek');
    }

    public function setPriceNetSek(?float $priceNetSek)
    {
        $this->setData('PriceNetSek', $priceNetSek);
    }

    public function getDeliveryGroup()
    {
        return $this->getData('DeliveryGroup');
    }

    public function setDeliveryGroup(?int $deliveryGroup)
    {
        $this->setData('DeliveryGroup', $deliveryGroup);
    }

    public function getExpectedDeliveryDate()
    {
        return $this->getData('ExpectedDeliveryDate');
    }

    public function setExpectedDeliveryDate(?string $expectedDeliveryDate)
    {
        $this->setData('ExpectedDeliveryDate', $expectedDeliveryDate);
    }

    public function getVatRate()
    {
        return $this->getData('VatRate');
    }

    public function setVatRate(?float $vatRate)
    {
        $this->setData('VatRate', $vatRate);
    }

    public function getSerialNumbers()
    {
        return $this->getData('SerialNumbers');
    }

    public function setSerialNumbers(?array $serialNumbers)
    {
        $this->setData('SerialNumbers', $serialNumbers);
    }

    public function getLineNumber()
    {
        return $this->getData('LineNumber');
    }

    public function setLineNumber(?int $lineNumber)
    {
        $this->setData('LineNumber', $lineNumber);
    }
}

<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace MadHat\ImageResize\Console\Command;

use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\Console\Cli;
use MadHat\ImageResize\Service\ImageResize;
use MadHat\ImageResize\Service\ImageResizeScheduler;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\ProgressBarFactory;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Command\Command;
use Magento\Framework\App\ResourceConnection;


/**
 * Resizes product images according to theme view definitions.
 */
class ImagesNewResizeCommand extends Command
{
    /**
     * Asynchronous image resize mode
     */
    const ASYNC_RESIZE = 'async';

    /**
     * Do not process images marked as hidden from product page
     */
    const SKIP_HIDDEN_IMAGES = 'skip_hidden_images';

    /**
     * Product Ids
     */
    const IDS = 'ids';

    /**
     * Product Sku
     */
    const SKU = 'sku';

    /**
     * @var ImageResizeScheduler
     */
    private $imageResizeScheduler;

    /**
     * @var ImageResize
     */
    private $imageResize;

    /**
     * @var State
     */
    private $appState;

    /**
     * @var ProgressBarFactory
     */
    private $progressBarFactory;

    /**
     * @var bool
     */
    private $skipHiddenImages = false;

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    /**
     * @param State $appState
     * @param ImageResize $imageResize
     * @param ImageResizeScheduler $imageResizeScheduler
     * @param ProgressBarFactory $progressBarFactory
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function __construct(
        State $appState,
        ImageResize $imageResize,
        ImageResizeScheduler $imageResizeScheduler,
        ProgressBarFactory $progressBarFactory,
        ResourceConnection $resourceConnection
    ) {
        parent::__construct();
        $this->appState = $appState;
        $this->imageResize = $imageResize;
        $this->imageResizeScheduler = $imageResizeScheduler;
        $this->progressBarFactory = $progressBarFactory;
        $this->resourceConnection = $resourceConnection;
        $this->connection = $this->resourceConnection->getConnection();
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('catalog:images:newresize')
            ->setDescription('Creates resized product images on basis of sku')
            ->setDefinition($this->getOptionsList());
    }

    /**
     * Image resize command options list
     *
     * @return array
     */
    private function getOptionsList(): array
    {
        return [
            new InputOption(
                self::ASYNC_RESIZE,
                'a',
                InputOption::VALUE_NONE,
                'Resize image in asynchronous mode'
            ),
            new InputOption(
                self::SKIP_HIDDEN_IMAGES,
                null,
                InputOption::VALUE_NONE,
                'Do not process images marked as hidden from product page'
            ),
            new InputOption(
                self::IDS,
                null,
                InputOption::VALUE_OPTIONAL,
                'Provide comma saperated ids of products'
            ),
            new InputOption(
                self::SKU,
                null,
                InputOption::VALUE_OPTIONAL,
                'Provide comma saperated skus of products'
            ),
        ];
    }

    /**
     * @inheritdoc
     * @param InputInterface $input
     * @param OutputInterface $output
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->skipHiddenImages = $input->getOption(self::SKIP_HIDDEN_IMAGES);
        $skuS = $input->getOption(self::SKU);
        $idS = $input->getOption(self::IDS);
        if (empty($idS)) {
            if (!empty($skuS)) {
                $finalSku = [];
                $skuArray = explode(',', $skuS);
                foreach ($skuArray as $row) {
                    $finalSku[] = $this->getIdsFromSku($row);
                }
                $idS = implode(",", $finalSku);
            }
        }
        $result = $this->executeSync($output, $idS);

        return $result;
    }

    /**
     * Return select to fetch all used product images.
     *
     * @return int
     */
    private function getIdsFromSku($sku)
    {
        $select = $this->connection->select()->distinct()
            ->from(
                ['images' => $this->resourceConnection->getTableName('catalog_product_entity')],
                'entity_id as id'
            )->where(
                'images.sku = ' . $sku
            );

        return $this->connection->fetchOne($select);
    }

    /**
     * Run resize in synchronous mode
     *
     * @param OutputInterface $output
     * @return int
     */
    private function executeSync(OutputInterface $output, $idS): int
    {
        try {
            $errors = [];
            $this->appState->setAreaCode(Area::AREA_GLOBAL);
            $generator = $this->imageResize->resizeFromThemes(null, $this->skipHiddenImages, $idS);

            /** @var ProgressBar $progress */
            $progress = $this->progressBarFactory->create(
                [
                    'output' => $output,
                    'max' => $generator->current()
                ]
            );
            $progress->setFormat(
                "%current%/%max% [%bar%] %percent:3s%% %elapsed% %memory:6s% \t| <info>%message%</info>"
            );

            if ($output->getVerbosity() !== OutputInterface::VERBOSITY_NORMAL) {
                $progress->setOverwrite(false);
            }

            while ($generator->valid()) {
                $resizeInfo = $generator->key();
                $error = $resizeInfo['error'];
                $filename = $resizeInfo['filename'];

                if ($error !== '') {
                    $errors[$filename] = $error;
                }

                $progress->setMessage($filename);
                $progress->advance();
                $generator->next();
            }
        } catch (\Exception $e) {
            $output->writeln("<error>{$e->getMessage()}</error>");
            // we must have an exit code higher than zero to indicate something was wrong
            return Cli::RETURN_FAILURE;
        }

        $output->write(PHP_EOL);
        if (count($errors)) {
            $output->writeln("<info>Product images resized with errors:</info>");
            foreach ($errors as $error) {
                $output->writeln("<error>{$error}</error>");
            }
        } else {
            $output->writeln("<info>Product images resized successfully</info>");
        }

        return Cli::RETURN_SUCCESS;
    }
}

<?php
/** @var \MadHat\OrderAdminExt\Block\Adminhtml\Order\View\Title $block */
$order = $block->getOrder();
$orderInfo = $block->getAdditionalOrderInfo();
?>
<?php if ($order && !empty($orderInfo)): ?>
    <script>
        require(['jquery'], function($) {
            $(document).ready(function() {
                var titleElement = $('.page-title-wrapper').find('.page-title');
                var originalTitle = titleElement.text();

                var additionalInfo = [];
                <?php if (isset($orderInfo['order_id'])): ?>
                additionalInfo.push('ID: <?= $block->escapeJs($orderInfo['order_id']) ?>');
                <?php endif; ?>

                <?php if (isset($orderInfo['site_order_id'])): ?>
                additionalInfo.push('Visma OrderNo: <?= $block->escapeJs($orderInfo['site_order_id']) ?>');
                <?php endif; ?>

                <?php if (isset($orderInfo['fc_order_id'])): ?>
                additionalInfo.push('FC ID: <?= $block->escapeJs($orderInfo['fc_order_id']) ?>');
                <?php endif; ?>

                if (additionalInfo.length > 0) {
                    titleElement.text(originalTitle + ' [' + additionalInfo.join(', ') + ']');
                }
            });
        });
    </script>
<?php endif; ?>

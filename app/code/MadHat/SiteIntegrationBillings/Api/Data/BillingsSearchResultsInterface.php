<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace MadHat\SiteIntegrationBillings\Api\Data;

interface BillingsSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Billings list.
     * @return \MadHat\SiteIntegrationBillings\Api\Data\BillingsInterface[]
     */
    public function getItems();

    /**
     * Set invoice_file list.
     * @param \MadHat\SiteIntegrationBillings\Api\Data\BillingsInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}


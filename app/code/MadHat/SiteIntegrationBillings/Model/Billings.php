<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace MadHat\SiteIntegrationBillings\Model;

use MadHat\SiteIntegrationBillings\Api\Data\BillingsInterface;
use Magento\Framework\Model\AbstractModel;

class Billings extends AbstractModel implements BillingsInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\MadHat\SiteIntegrationBillings\Model\ResourceModel\Billings::class);
    }

    /**
     * @inheritDoc
     */
    public function getBillingsId()
    {
        return $this->getData(self::BILLINGS_ID);
    }

    /**
     * @inheritDoc
     */
    public function setBillingsId($billingsId)
    {
        return $this->setData(self::BILLINGS_ID, $billingsId);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceFile()
    {
        return $this->getData(self::INVOICEFILE);
    }

    /**
     * @inheritDoc
     */
    public function setInvoiceFile($invoiceFile)
    {
        return $this->setData(self::INVOICEFILE, $invoiceFile);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceNo()
    {
        return $this->getData(self::INVOICENO);
    }

    /**
     * @inheritDoc
     */
    public function setInvoiceNo($invoiceNo)
    {
        return $this->setData(self::INVOICENO, $invoiceNo);
    }

    /**
     * @inheritDoc
     */
    public function getOrderId()
    {
        return $this->getData(self::ORDER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setOrderId($orderId)
    {
        return $this->setData(self::ORDER_ID, $orderId);
    }

    /**
     * @inheritDoc
     */
    public function getIncrementId()
    {
        return $this->getData(self::INCREMENT_ID);
    }

    /**
     * @inheritDoc
     */
    public function setIncrementId($incrementId)
    {
        return $this->setData(self::INCREMENT_ID, $incrementId);
    }

    /**
     * @inheritDoc
     */
    public function getInvoiceUrl()
    {
        return $this->getData(self::INVOICEURL);
    }

    /**
     * @inheritDoc
     */
    public function setInvoiceUrl($invoiceUrl)
    {
        return $this->setData(self::INVOICEURL, $invoiceUrl);
    }

    /**
     * @inheritDoc
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @inheritDoc
     */
    public function getUpdatedAt()
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }
}

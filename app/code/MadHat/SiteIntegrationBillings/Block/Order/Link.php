<?php
declare(strict_types=1);

namespace MadHat\SiteIntegrationBillings\Block\Order;

use Exception;
use MadHat\SiteIntegrationBillings\Model\BillingsRepository;
use Magento\Framework\App\DefaultPathInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Registry;
use Magento\Framework\View\Element\Template\Context;

class Link extends \Magento\Sales\Block\Order\Link
{
    public function __construct(
        Context $context,
        DefaultPathInterface $defaultPath,
        Registry $registry,
        BillingsRepository $billingsRepository,
        array $data = []
    ) {
        parent::__construct($context, $defaultPath, $registry, $data);
        $this->billingsRepository = $billingsRepository;
    }

    /**
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    protected function _toHtml(): string
    {
        $order = $this->_registry->registry('current_order');
        try {
            $billingsData = $this->billingsRepository->getByOrderId($order->getId());
            $billingData = $billingsData[0];
            if ($billingData->getInvoiceFile() !== null) {
                return parent::_toHtml();
            }
        } catch (Exception $e) {
            // do nothing
        }

        return '';
    }

}

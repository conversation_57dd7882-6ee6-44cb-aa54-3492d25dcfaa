<?php

namespace MadHat\SiteIntegrationBillings\Controller\Invoicepdf;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use MadHat\SiteIntegrationBillings\Model\Api\Billings as ApiBillings;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\UrlInterface;
use Magento\Customer\Model\Session as CustomerSession;

class Download extends \Magento\Framework\App\Action\Action
{
    /**
     * @var FileFactory
     */
    private $fileFactory;
    /**
     * @var UrlInterface
     */
    private $urlInterface;
    private ApiBillings $apiBillings;
    private OrderRepositoryInterface $orderRepository;
    private ScopeConfigInterface $scopeConfig;
    /**
     * @var CustomerSession
     */
    private CustomerSession $customerSession;

    /**
     * @param ScopeConfigInterface       $scopeConfig
     * @param OrderRepositoryInterface   $orderRepository
     * @param ApiBillings                $apiBillings
     * @param UrlInterface               $urlInterface
     * @param FileFactory                $fileFactory
     * @param CustomerSession            $customerSession
     * @param Context                    $context
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        OrderRepositoryInterface $orderRepository,
        ApiBillings $apiBillings,
        UrlInterface $urlInterface,
        FileFactory $fileFactory,
        CustomerSession $customerSession,
        Context $context
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->orderRepository = $orderRepository;
        $this->apiBillings = $apiBillings;
        $this->urlInterface = $urlInterface;
        $this->fileFactory = $fileFactory;
        $this->customerSession = $customerSession;
        parent::__construct($context);
    }

    public function execute()
    {
        // Check if customer is logged in
        if (!$this->customerSession->isLoggedIn()) {
            $this->messageManager->addErrorMessage(__('Access denided'));
            return $this->_redirect($this->urlInterface->getUrl(''));
        }

        $fileName = $this->getRequest()->getParam('file');
        $orderId  = $this->getRequest()->getParam('order_id');

        if (!empty($fileName)) {
            $order = $this->orderRepository->get($orderId);
            if ($order) {
                $path = $this->apiBillings->getPathInvoice($order);
                $fullFilePath = $path . $fileName;
                $downloadName = $fileName;
                $baseDir = $this->scopeConfig->getValue(
                    'madhat_siteintegrationbillings/general/directory_type',
                    ScopeInterface::SCOPE_STORE
                );
                $content['type']  = 'filename';
                $content['value'] = $fullFilePath;
                $content['rm']    = 0; // Set to 1 to remove file after download if needed.
                $contentType = 'application/pdf';
                return $this->fileFactory->create($downloadName, $content, $baseDir, $contentType);
            }
        }
    }
}

# Magento 2 Module MadHat FreeShippingNotification

    `madhat/module-freeshippingnotification`

- [Main Functionalities](#markdown-header-main-functionalities)
- [Installation](#markdown-header-installation)
- [Configuration](#markdown-header-configuration)
- [Specifications](#markdown-header-specifications)
- [Attributes](#markdown-header-attributes)


## Main Functionalities
Displays message to avail free shipping after reaching free shipping subtotal. 

## Installation
\* = in production please use the `--keep-generated` option

### Type 1: Zip file

- Unzip the zip file in `app/code/MadHat`
- Enable the module by running `php bin/magento module:enable MadHat_FreeShippingNotification`
- Apply database updates by running `php bin/magento setup:upgrade`\*
- Flush the cache by running `php bin/magento cache:flush`

## Configuration

- Log in to the Magento Admin Panel.
- Navigate to Stores > Configuration.
- Under Sales, select Shipping Methods.
- Expand the Free Shipping section.
- Set Enabled for Checkout to "Yes". 
- In the same Shipping Methods section as above, locate the Minimum Order Amount setting under the Free Shipping section.
- Enter the desired minimum order amount (e.g., 1000) in the field provided.
- Save the configuration.
- Flush the cache by running `php bin/magento cache:flush`

## Specifications




## Attributes




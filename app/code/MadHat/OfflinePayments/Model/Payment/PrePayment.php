<?php
declare(strict_types=1);

namespace MadHat\OfflinePayments\Model\Payment;

use Magento\Payment\Model\Method\AbstractMethod;
use Magento\Quote\Api\Data\CartInterface;

class PrePayment extends AbstractMethod
{

    protected $_code = "prepayment";
    protected $_isOffline = true;

    public function isAvailable(CartInterface $quote = null): bool
    {
        return parent::isAvailable($quote);
    }
}

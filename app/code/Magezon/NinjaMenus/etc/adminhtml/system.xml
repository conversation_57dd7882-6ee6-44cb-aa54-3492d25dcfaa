<?xml version="1.0"?>
<!--
/**
 * Magezon
 *
 * This source file is subject to the Magezon Software License, which is available at https://www.magezon.com/license.
 * Do not edit or add to this file if you wish to upgrade the to newer versions in the future.
 * If you wish to customize this module for your needs.
 * Please refer to https://www.magezon.com for more information.
 *
 * @category  Magezon
 * @package   Magezon_NinjaMenus
 * @copyright Copyright (C) 2019 Magezon (https://www.magezon.com)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../Config/etc/system_file.xsd">
	<system>
		<section id="ninjamenus" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
			<class>separator-top</class>
			<label>Ninja Menus</label>
			<tab>magezon</tab>
			<resource>Magezon_NinjaMenus::settings</resource>
			<group id="general" translate="label" type="text" sortOrder="0" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>General Settings</label>
				<field id="version" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Current Version</label>
					<frontend_model>Magezon\NinjaMenus\Block\Adminhtml\Renderer\Config\Version</frontend_model>
				</field>
				<field id="enabled" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enable Ninja Menus</label>
					<comment>if you want to Disable the module and view the default menu, please select No. Don’t forget to refresh cache layout</comment>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
		</section>
	</system>
</config>
<div class="mgz__control-inner">
	<span class="mgz-icon mgz-icon-delete mgz-control-delete" ng-click="model[options.key]=''" ng-if="model[options.key]"></span>
	<ui-select ng-model="model[options.key]" theme="bootstrap" class="mgz__control-uiselect" search-enabled="true">
		<ui-select-match placeholder="{{to.placeholder}}">{{$select.selected[to.labelProp || 'label']}}</ui-select-match>
		<ui-select-choices group-by="to.groupBy" position='down' repeat="option[to.valueProp || 'value'] as option in to.options | filter: $select.search"
			refresh="refresh($select)"
			refresh-delay="300">
			<div ng-bind-html="(to.showValue ? '[' + option['value'] + '] ' : '') + option[to.labelProp || 'label'] | highlight: $select.search"></div>
		</ui-select-choices>
	</ui-select>
</div>
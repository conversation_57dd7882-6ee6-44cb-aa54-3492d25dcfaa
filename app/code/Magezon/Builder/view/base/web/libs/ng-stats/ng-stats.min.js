//! ng-stats version 2.5.4 built with ♥ by <PERSON> <<EMAIL>> (http://kent.doddsfamily.us), <PERSON> <<EMAIL>> (http://jinxidoru.blogspot.com), <PERSON> <<EMAIL>> (http://daniellmb.com) (ó ì_í)=óò=(ì_í ò)
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("angular")):"function"==typeof define&&define.amd?define(["angular"],e):"object"==typeof exports?exports.showAngularStats=e(require("angular")):t.showAngularStats=e(t.angular)}(this,function(t){return function(t){function e(o){if(n[o])return n[o].exports;var r=n[o]={exports:{},id:o,loaded:!1};return t[o].call(r.exports,r,r.exports,e),r.loaded=!0,r.exports}var n={};return e.m=t,e.c=n,e.p="",e(0)}([function(t,e,n){"use strict";function o(t){return t&&t.__esModule?t:{"default":t}}function r(){if(!P){P=!0;var t=Object.getPrototypeOf(l()),e=t.$digest;t.$digest=function(){var t=U();e.apply(this,arguments);var n=U()-t;h(s(),n)}}}function i(){return"undefined"!=typeof chrome&&"undefined"!=typeof chrome.storage&&"undefined"!=typeof chrome.storage.local}function a(t){window.self.angular&&l()?c(t):setTimeout(function(){a(t)},200)}function u(t){return t!==!1&&t.autoload||(sessionStorage.removeItem(L),localStorage.removeItem(L),t!==!1)?(t.position=t.position||"top-left",t=T.extend({htmlId:null,rootScope:void 0,digestTimeThreshold:16,watchCountThreshold:2e3,autoload:!1,trackDigest:!1,trackWatches:!1,logDigest:!1,logWatches:!1,styles:{position:"fixed",background:"black",borderBottom:"1px solid #666",borderRight:"1px solid #666",color:"#666",fontFamily:"Courier",width:130,zIndex:9999,textAlign:"right",top:-1===t.position.indexOf("top")?null:0,bottom:-1===t.position.indexOf("bottom")?null:0,right:-1===t.position.indexOf("right")?null:0,left:-1===t.position.indexOf("left")?null:0}},t||{}),t.rootScope&&(D=t.rootScope),t):void 0}function c(t){function e(e,n,o){var r=e.charAt(0).toUpperCase()+e.slice(1);t["track"+r]&&(l[e]=[],n["track + capThingToTrack"]=function(t){o&&l[e][l.length-1]===t||(l[e][l.length-1]=t,l[e].push(t))})}function n(e,n,o){var r=e.charAt(0).toUpperCase()+e.slice(1);if(t["log"+r]){var a;n["log"+r]=function(t){if(!o||a!==t){a=t;var n=i(e,t);n?console.log("%c"+e+":",n,t):console.log(e+":",t)}}}}function o(t,e){return t>e?"red":t>.7*e?"orange":"green"}function i(e,n){var r;return"digest"===e?r="color:"+o(n,t.digestTimeThreshold):"watches"===e&&(r="color:"+o(n,t.watchCountThreshold)),r}function a(e,n){var r=n||A,i=o(r,t.digestTimeThreshold);R=p(e)?R:e;var a=o(R,t.watchCountThreshold);if(A=p(n)?A:n,h.text(R).css({color:a}),v.text(A.toFixed(2)).css({color:i}),n){var u=m.getContext("2d");f>0&&(f=0,u.fillStyle="#333",u.fillRect(w.width-1,0,1,w.height)),u.fillStyle=i,u.fillRect(w.width-1,Math.max(0,w.height-r),2,2)}}function c(){if(s.active){setTimeout(c,250);var t=m.getContext("2d"),e=t.getImageData(1,0,w.width-1,w.height);t.putImageData(e,0,0),t.fillStyle=f++>2?"black":"#333",t.fillRect(w.width-1,0,1,w.height)}}t=void 0!==t?t:{};var l={listeners:E};if(O&&(O.$el&&O.$el.remove(),O.active=!1,O=null),t=u(t)){r();var s=O={active:!0};if(t.autoload)if("localStorage"===t.autoload)localStorage.setItem(L,JSON.stringify(t));else{if("sessionStorage"!==t.autoload&&"boolean"!=typeof t.autoload)throw new Error("Invalid value for autoload: "+t.autoload+' can only be "localStorage" "sessionStorage" or boolean.');sessionStorage.setItem(L,JSON.stringify(t))}var d=T.element(document.body),f=0,g=t.htmlId?' id="'+t.htmlId+'"':"";s.$el=T.element("<div"+g+"><canvas></canvas><div><span></span> | <span></span></div></div>").css(t.styles),d.append(s.$el);var h=s.$el.find("span"),v=h.next(),w={width:130,height:40},m=s.$el.find("canvas").attr(w)[0];return E.digestLength.ngStatsAddToCanvas=function(t){a(null,t)},E.watchCount.ngStatsAddToCanvas=function(t){a(t)},e("digest",E.digestLength),e("watches",E.watchCount,!0),n("digest",E.digestLength),n("watches",E.watchCount,!0),c(),D.$$phase||D.$digest(),l}}function l(){if(D)return D;var t=document.querySelector(q);return t?D=T.element(t).scope().$root:null}function s(){clearTimeout(k);var t=U();return t-I>300?(I=t,R=v()):k=setTimeout(function(){h(s())},350),R}function d(t){var e=f(t);return v(e)}function f(t){t=T.element(t);var e=t.scope();return e||(t=T.element(t.querySelector(q)),e=t.scope()),e}function g(t){return t&&t.$$watchers?t.$$watchers:[]}function h(t,e){p(t)||T.forEach(E.watchCount,function(e){e(t)}),p(e)||T.forEach(E.digestLength,function(t){t(e)})}function p(t){return null===t||void 0===t}function v(t){var e=0;return w(t,function(t){e+=g(t).length}),e}function w(t,e){if("function"==typeof t&&(e=t,t=null),t=t||l(),t=C(t)){var n=e(t);return n===!1?n:S(t,e)}}function m(t,e){for(var n;(t=t.$$nextSibling)&&(n=e(t),n!==!1)&&(n=S(t,e),n!==!1););return n}function S(t,e){for(var n;(t=t.$$childHead)&&(n=e(t),n!==!1)&&(n=m(t,e),n!==!1););return n}function y(t){var e=null;return w(function(n){return n.$id===t?(e=n,!1):void 0}),e}function C(t){return x(t)&&(t=y(t)),t}function x(t){return"string"==typeof t||"number"==typeof t}Object.defineProperty(e,"__esModule",{value:!0});var $=n(1),b=o($),T=b["default"];T.version||(T=window.angular),e["default"]=c;var D,L="showAngularStats_autoload",O=null,U=window.self.performance&&window.self.performance.now?function(){return window.self.performance.now()}:function(){return Date.now()},I=U(),k=null,R=s()||0,A=0,q=".ng-scope, .ng-isolate-scope",P=!1,E={watchCount:{},digestLength:{}},j=sessionStorage[L]||!i()&&localStorage[L];j&&a(JSON.parse(j)),T.module("angularStats",[]).directive("angularStats",function(){function t(t){for(var e=t[0];e.parentElement;)e=e.parentElement;return e}var e=1;return{scope:{digestLength:"@",watchCount:"@",watchCountRoot:"@",onDigestLengthUpdate:"&?",onWatchCountUpdate:"&?"},link:function(n,o,i){function a(){if(i.hasOwnProperty("digestLength")){var t=o;i.digestLength&&(t=T.element(o[0].querySelector(i.digestLength))),E.digestLength["ngStatsDirective"+f]=function(e){window.dirDigestNode=t[0],t.text((e||0).toFixed(2))}}}function u(){if(i.hasOwnProperty("watchCount")){var e,r=o;if(n.watchCount&&(r=T.element(o[0].querySelector(i.watchCount))),n.watchCountRoot)if("this"===n.watchCountRoot)e=o;else{var a;if(a=i.hasOwnProperty("watchCountOfChild")?o[0]:t(o),e=T.element(a.querySelector(n.watchCountRoot)),!e.length)throw new Error("no element at selector: "+n.watchCountRoot)}E.watchCount["ngStatsDirective"+f]=function(t){var n=t;e&&(n=d(e)),r.text(n)}}}function c(){i.hasOwnProperty("onWatchCountUpdate")&&(E.watchCount["ngStatsDirectiveUpdate"+f]=function(t){n.onWatchCountUpdate({watchCount:t})})}function l(){i.hasOwnProperty("onDigestLengthUpdate")&&(E.digestLength["ngStatsDirectiveUpdate"+f]=function(t){n.onDigestLengthUpdate({digestLength:t})})}function s(){delete E.digestLength["ngStatsDirectiveUpdate"+f],delete E.watchCount["ngStatsDirectiveUpdate"+f],delete E.digestLength["ngStatsDirective"+f],delete E.watchCount["ngStatsDirective"+f]}r();var f=e++;a(),u(),c(),l(),n.$on("$destroy",s)}}}),t.exports=e["default"]},function(e,n){e.exports=t}])});
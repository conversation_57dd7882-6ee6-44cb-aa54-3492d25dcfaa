/* Romanian initialisation for the jQuery UI date picker plugin.
 *
 * Written by <PERSON> (<EMAIL>)
 * and <PERSON><PERSON> (<EMAIL>)
 */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.ro = {
	closeText: "Închide",
	prevText: "&#xAB; Luna precedentă",
	nextText: "<PERSON> următoare &#xBB;",
	currentText: "<PERSON><PERSON>",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
	"<PERSON>ulie", "August", "Septembrie", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>bri<PERSON>" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Oct", "Nov", "<PERSON>" ],
	dayNames: [ "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "S<PERSON>mb<PERSON>t<PERSON>" ],
	dayNamesShort: [ "<PERSON>m", "Lun", "Mar", "Mie", "Joi", "<PERSON>", "S<PERSON>m" ],
	day<PERSON>ames<PERSON>in: [ "<PERSON>", "Lu", "Ma", "Mi", "Jo", "Vi", "Sâ" ],
	weekHeader: "Săpt",
	date<PERSON>ormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.ro );

return datepicker.regional.ro;

} );

/* Finnish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.fi = {
	closeText: "Sulje",
	prevText: "&#xAB;Edellinen",
	nextText: "Seuraava&#xBB;",
	currentText: "Tän<PERSON>än",
	monthNames: [ "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>" ],
	monthNamesShort: [ "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>" ],
	day<PERSON>ames<PERSON>hort: [ "<PERSON>", "<PERSON>", "<PERSON>i", "Ke", "<PERSON>", "<PERSON>e", "<PERSON>" ],
	day<PERSON>ames: [ "<PERSON>nuntai", "<PERSON><PERSON>ntai", "<PERSON>ii<PERSON>i", "<PERSON><PERSON>viik<PERSON>", "<PERSON><PERSON>i", "<PERSON>jan<PERSON>", "<PERSON><PERSON><PERSON>" ],
	day<PERSON>ames<PERSON>in: [ "<PERSON>", "<PERSON>", "<PERSON>i", "<PERSON>", "<PERSON>", "<PERSON>e", "<PERSON>" ],
	weekHeader: "Vk",
	dateFormat: "d.m.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.fi );

return datepicker.regional.fi;

} );

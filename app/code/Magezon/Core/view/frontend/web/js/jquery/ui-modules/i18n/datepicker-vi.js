/* Vietnamese initialisation for the jQuery UI date picker plugin. */
/* Translated by <PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.vi = {
	closeText: "Đóng",
	prevText: "&#x3C;Trước",
	nextText: "Tiếp&#x3E;",
	currentText: "Hôm nay",
	monthNames: [ "Tháng <PERSON>", "Tháng Hai", "Tháng Ba", "<PERSON>h<PERSON><PERSON>", "Tháng <PERSON>ăm", "<PERSON>háng <PERSON>",
	"<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>h<PERSON>g <PERSON>" ],
	monthNamesShort: [ "Tháng 1", "Tháng 2", "Tháng 3", "<PERSON>háng 4", "Th<PERSON>g 5", "<PERSON>háng 6",
	"Tháng 7", "Tháng 8", "Tháng 9", "Tháng 10", "Tháng 11", "Tháng 12" ],
	day<PERSON>ames: [ "Ch<PERSON> Nh<PERSON>t", "Th<PERSON> <PERSON>", "Th<PERSON> <PERSON>", "Th<PERSON> T<PERSON>", "Th<PERSON> N<PERSON>m", "Th<PERSON> S<PERSON>u", "Thứ Bảy" ],
	dayNamesShort: [ "CN", "T2", "T3", "T4", "T5", "T6", "T7" ],
	dayNamesMin: [ "CN", "T2", "T3", "T4", "T5", "T6", "T7" ],
	weekHeader: "Tu",
	dateFormat: "dd/mm/yy",
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.vi );

return datepicker.regional.vi;

} );

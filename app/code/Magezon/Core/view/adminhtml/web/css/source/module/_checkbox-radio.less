// /**
//  * Copyright Â© Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

@mgz-control-checkbox-radio__background-color: #FFF;
@mgz-field-control__border-color: #adadad;
@mgz-control-checkbox-radio__size: 1.6rem;
@mgz-field-label__indent: 1rem;
@mgz-control-checkbox-radio-mark__color: #514943;
@mgz-field-control__color: #303030;
@mgz-field-control__disabled__background-color: #e9e9e9;
@mgz-field-control__focus__border-color: #007bdb;
@mgz-field-control__hover__border-color: darken(#adadad, 15%);
@mgz-smooth__border-color: border-color .1s linear;
@mgz-icon-check-mage__content: '\e62d';

//
//  Variables
//  _____________________________________________


.bfb-checkbox-item,
.bfb-radio-item {
    margin-top: 0.7rem;
}

//  Checkbox & radio
.mgz-control-radio,
.mgz-control-checkbox {
    cursor: pointer;
    opacity: .01; // hack for successful selenium tests
    overflow: hidden;
    position: absolute !important;
    vertical-align: top;

    &:after { // ToDo UI: Should be deleted with old styles that generate styles for this element
        display: none;
    }

    + label {
        cursor: pointer;
        display: inline-block;
        vertical-align: sub;

        &:before {
            .magezon-icon();
            .mgz-box-sizing(border-box);
            background-color: @mgz-control-checkbox-radio__background-color;
            border: 1px solid @mgz-field-control__border-color;
            color: transparent;
            float: left;
            height: @mgz-control-checkbox-radio__size;
            text-align: center;
            vertical-align: top;
            width: @mgz-control-checkbox-radio__size;
        }
    }

    //  Label with text content
    + label {
        padding-left: @mgz-control-checkbox-radio__size + @mgz-field-label__indent - .4;

        &:before {
            margin: 1px 0 0 -(@mgz-control-checkbox-radio__size + @mgz-field-label__indent - .4);
        }
    }

    //  Checked state
    &:checked {
        + label {
            &:before {
                color: @mgz-control-checkbox-radio-mark__color;
            }
        }
    }

    //  Disabled state
    &.disabled,
    &[disabled] {
        + label {
            color: @mgz-field-control__color;
            cursor: default;
            opacity: .5;

            &:before {
                background-color: @mgz-field-control__disabled__background-color;
                border-color: @mgz-field-control__border-color;
                cursor: default;
            }
        }
    }

    &:not([disabled]),
    &:not(.disabled) {
        //  Focus state
        &:focus {
            + label {
                &:before {
                    ._keyfocus & {
                        border-color: @mgz-field-control__focus__border-color;
                    }
                }
            }
        }

        //  Hover state
        &:hover {
            + label {
                &:before {
                    border-color: @mgz-field-control__hover__border-color;
                }
            }
        }
    }
}

//  Radio
.mgz-control-radio {
    + label {
        &:before {
            border-radius: @mgz-control-checkbox-radio__size;
            content: '';
            transition: @mgz-smooth__border-color, color .1s ease-in;
        }
    }

    //  Discard extend line-height 1, to fix animation
    &.mgz-control-radio {
        + label {
            &:before {
                line-height: 140%;
            }
        }
    }

    //  Radio checked state
    &:checked {
        + label {
            position: relative;

            &:after {
                background-color: @mgz-control-checkbox-radio-mark__color;
                border-radius: 50%;
                content: '';
                height: 10px;
                left: 3px;
                position: absolute;
                top: 4px;
                width: 10px;
            }
        }

        &:not([disabled]),
        &:not(.disabled) {
            //  Prevent hover effects for checked radio
            &:hover {
                cursor: default;

                + label {
                    cursor: default;

                    &:before {
                        border-color: @mgz-field-control__border-color;
                    }
                }
            }
        }
    }
}

//  Checkbox
.mgz-control-checkbox {
    + label {
        &:before {
            &:extend(.abs-icon all);
            border-radius: 1px;
            content: '';
            font-size: 0;
            transition: font-size .1s ease-out, color .1s ease-out, @mgz-smooth__border-color;
        }
    }

    &:checked {
        + label {
            &:before {
                content: @mgz-icon-check-mage__content;
                font-size: 1.1rem;
                line-height: 125%;
            }
        }
    }
}
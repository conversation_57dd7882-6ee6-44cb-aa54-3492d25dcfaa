<?php
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Smtp
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */

$type = $block->getRequest()->getParam('type');
switch ($type) {
    case 'forms':
        $iframeUrl = 'https://magento2.avada.io/forms.html';
        break;
    case 'automation':
        $iframeUrl = 'https://magento2.avada.io/automation.html';
        break;
    case 'newsletters':
        $iframeUrl = 'https://magento2.avada.io/newsletters.html';
        break;
    case 'sms':
        $iframeUrl = 'https://magento2.avada.io/sms.html';
        break;
    default:
        return;
}
?>
<iframe id="mp-smtp-iframe" src="<?= /* @noEscape */ $iframeUrl ?>" frameborder="0" width="100%" scrolling="no"></iframe>

<?xml version="1.0"?>
<!--
/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Core
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
 -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Mageplaza_Core::menu" title="Mageplaza Extensions" sortOrder="50">
                    <resource id="Mageplaza_Core::documentation" title="Documentations" sortOrder="50">
                        <resource id="Mageplaza_Core::userguide" title="User Guides" sortOrder="50"/>
                    </resource>
                    <resource id="Mageplaza_Core::activate" title="Active Module" sortOrder="70"/>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Mageplaza_Core::configuration" title="Mageplaza Information" sortOrder="10"/>
                            <resource id="Mageplaza_Core::marketplace" title="Mageplaza Marketplace" sortOrder="10"/>
                        </resource>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>

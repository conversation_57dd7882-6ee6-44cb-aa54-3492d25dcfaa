/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Core
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */
@import "variables/_icons.less";

.lib-font-face(
  @family-name: @icons-mageplaza__font-name,
  @font-path: @icons-mageplaza__font-name-path,
  @font-weight: normal,
  @font-style: normal
);

.lib-font-face(
  @family-name: @icons-avada__font-name,
  @font-path: @icons-avada__font-name-path,
  @font-weight: normal,
  @font-style: normal
);

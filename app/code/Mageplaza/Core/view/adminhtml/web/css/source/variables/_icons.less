/**
 * Mageplaza
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the mageplaza.com license that is
 * available through the world-wide-web at this URL:
 * https://www.mageplaza.com/LICENSE.txt
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Mageplaza
 * @package     Mageplaza_Core
 * @copyright   Copyright (c) Mageplaza (https://www.mageplaza.com/)
 * @license     https://www.mageplaza.com/LICENSE.txt
 */

@icons-mageplaza__font-name: 'Mageplaza Icons';
@icons-mageplaza__file-name: 'mageplaza-icons';
@icons-mageplaza__font-name-path: '@{baseDir}Mageplaza_Core/fonts/@{icons-mageplaza__file-name}/@{icons-mageplaza__file-name}';
@icon-mageplaza__content: '\e900';

@icons-avada__font-name: '<PERSON><PERSON> Icons';
@icons-avada__file-name: 'avada-icons';
@icons-avada__font-name-path: '@{baseDir}Mageplaza_Core/fonts/@{icons-avada__file-name}/@{icons-avada__file-name}';
@icon-avada__content: '\e900';
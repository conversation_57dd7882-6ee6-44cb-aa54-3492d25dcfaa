<?php
/**
 * Test script to verify schema.org markup implementation
 */

function fetchPageContent($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $content = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200) {
        echo "HTTP Error: $httpCode for URL: $url\n";
        return false;
    }
    
    return $content;
}

function extractSchemaMarkup($content) {
    $schemas = [];
    
    // Find all JSON-LD script tags
    preg_match_all('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $content, $matches);
    
    foreach ($matches[1] as $jsonContent) {
        $decoded = json_decode(trim($jsonContent), true);
        if ($decoded) {
            $schemas[] = $decoded;
        }
    }
    
    return $schemas;
}

function analyzeSchema($schemas, $pageType) {
    echo "\n=== $pageType Page Schema Analysis ===\n";
    
    foreach ($schemas as $schema) {
        if (is_array($schema) && isset($schema[0])) {
            $schema = $schema[0]; // Handle array wrapper
        }
        
        if (isset($schema['@type'])) {
            echo "Schema Type: " . $schema['@type'] . "\n";
            
            if ($schema['@type'] === 'ProductGroup') {
                echo "Product Group Name: " . ($schema['name'] ?? 'N/A') . "\n";
                echo "Product Group ID: " . ($schema['ProductGroupID'] ?? 'N/A') . "\n";
                
                if (isset($schema['@id'])) {
                    echo "Schema @id: " . $schema['@id'] . "\n";
                } else {
                    echo "WARNING: Missing @id property\n";
                }
                
                if (isset($schema['url'])) {
                    echo "WARNING: Found 'url' property (should be @id instead)\n";
                }
                
                if (isset($schema['hasVariant'])) {
                    echo "Number of variants: " . count($schema['hasVariant']) . "\n";
                    
                    // Check variant structure
                    foreach ($schema['hasVariant'] as $i => $variant) {
                        if (isset($variant['@type']) && $variant['@type'] === 'Product') {
                            echo "  Variant $i: Full Product details (name: " . ($variant['name'] ?? 'N/A') . ")\n";
                        } elseif (isset($variant['url']) && count($variant) === 1) {
                            echo "  Variant $i: URL only (" . $variant['url'] . ")\n";
                        } else {
                            echo "  Variant $i: Mixed structure\n";
                        }
                    }
                }
                
                if (isset($schema['variesBy'])) {
                    echo "Varies By: " . implode(', ', $schema['variesBy']) . "\n";
                }
            }
            
            if ($schema['@type'] === 'BreadcrumbList') {
                echo "Breadcrumb List found\n";
            }
        }
    }
}

// Test URLs - you may need to adjust these based on your actual product URLs
$testUrls = [
    'Base Product' => 'https://ppn.project/', // Replace with actual base product URL
    // 'Variant Product' => 'https://ppn.project/some-variant-product-url', // Replace with actual variant URL
];

echo "Schema.org Markup Test\n";
echo "=====================\n";

foreach ($testUrls as $type => $url) {
    echo "\nTesting $type: $url\n";
    
    $content = fetchPageContent($url);
    if ($content === false) {
        continue;
    }
    
    $schemas = extractSchemaMarkup($content);
    
    if (empty($schemas)) {
        echo "No schema.org markup found!\n";
        continue;
    }
    
    analyzeSchema($schemas, $type);
}

echo "\n=== Test Complete ===\n";
?>
